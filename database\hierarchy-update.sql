-- প্রশাসনিক হায়ারার্কি আপডেট
-- Administrative Hierarchy Update

-- Update areas table enum to include all administrative levels
ALTER TABLE `areas` 
MODIFY COLUMN `type` enum('division','district','upazila','municipality','union','village','city','ward') NOT NULL DEFAULT 'ward';

-- Clear existing sample data
DELETE FROM `areas`;

-- Insert Bangladesh Administrative Hierarchy Sample Data

-- ১. বিভাগ (Divisions)
INSERT INTO `areas` (`name`, `name_bn`, `type`, `code`, `description`) VALUES
('Dhaka Division', 'ঢাকা বিভাগ', 'division', 'DIV-01', 'ঢাকা বিভাগ'),
('Chittagong Division', 'চট্টগ্রাম বিভাগ', 'division', 'DIV-02', 'চট্টগ্রাম বিভাগ'),
('Rajshahi Division', 'রাজশাহী বিভাগ', 'division', 'DIV-03', 'রাজশাহী বিভাগ'),
('Khulna Division', 'খুলনা বিভাগ', 'division', 'DIV-04', 'খুলনা বিভাগ'),
('Sylhet Division', 'সিলেট বিভাগ', 'division', 'DIV-05', 'সিলেট বিভাগ'),
('Barisal Division', 'বরিশাল বিভাগ', 'division', 'DIV-06', 'বরিশাল বিভাগ'),
('Rangpur Division', 'রংপুর বিভাগ', 'division', 'DIV-07', 'রংপুর বিভাগ'),
('Mymensingh Division', 'ময়মনসিংহ বিভাগ', 'division', 'DIV-08', 'ময়মনসিংহ বিভাগ');

-- ২. জেলা (Districts) - ঢাকা বিভাগের অধীনে
INSERT INTO `areas` (`name`, `name_bn`, `type`, `parent_id`, `code`, `description`) VALUES
('Dhaka District', 'ঢাকা জেলা', 'district', 1, 'DIS-01', 'ঢাকা জেলা'),
('Gazipur District', 'গাজীপুর জেলা', 'district', 1, 'DIS-02', 'গাজীপুর জেলা'),
('Narayanganj District', 'নারায়ণগঞ্জ জেলা', 'district', 1, 'DIS-03', 'নারায়ণগঞ্জ জেলা'),
('Manikganj District', 'মানিকগঞ্জ জেলা', 'district', 1, 'DIS-04', 'মানিকগঞ্জ জেলা');

-- ৩. উপজেলা (Upazilas) - ঢাকা জেলার অধীনে
INSERT INTO `areas` (`name`, `name_bn`, `type`, `parent_id`, `code`, `description`) VALUES
('Dhanmondi Upazila', 'ধানমন্ডি উপজেলা', 'upazila', 9, 'UPZ-01', 'ধানমন্ডি উপজেলা'),
('Gulshan Upazila', 'গুলশান উপজেলা', 'upazila', 9, 'UPZ-02', 'গুলশান উপজেলা'),
('Ramna Upazila', 'রমনা উপজেলা', 'upazila', 9, 'UPZ-03', 'রমনা উপজেলা'),
('Tejgaon Upazila', 'তেজগাঁও উপজেলা', 'upazila', 9, 'UPZ-04', 'তেজগাঁও উপজেলা');

-- ৪. পৌরসভা (Municipalities) - ধানমন্ডি উপজেলার অধীনে
INSERT INTO `areas` (`name`, `name_bn`, `type`, `parent_id`, `code`, `description`) VALUES
('Dhanmondi Municipality', 'ধানমন্ডি পৌরসভা', 'municipality', 13, 'MUN-01', 'ধানমন্ডি পৌরসভা');

-- ৫. ইউনিয়ন (Unions) - গুলশান উপজেলার অধীনে
INSERT INTO `areas` (`name`, `name_bn`, `type`, `parent_id`, `code`, `description`) VALUES
('Gulshan Union', 'গুলশান ইউনিয়ন', 'union', 14, 'UNI-01', 'গুলশান ইউনিয়ন'),
('Banani Union', 'বনানী ইউনিয়ন', 'union', 14, 'UNI-02', 'বনানী ইউনিয়ন');

-- ৬. গ্রাম (Villages) - গুলশান ইউনিয়নের অধীনে
INSERT INTO `areas` (`name`, `name_bn`, `type`, `parent_id`, `code`, `description`) VALUES
('Gulshan Village', 'গুলশান গ্রাম', 'village', 18, 'VIL-01', 'গুলশান গ্রাম'),
('Mohakhali Village', 'মহাখালী গ্রাম', 'village', 18, 'VIL-02', 'মহাখালী গ্রাম');

-- ৭. শহর (Cities) - রমনা উপজেলার অধীনে
INSERT INTO `areas` (`name`, `name_bn`, `type`, `parent_id`, `code`, `description`) VALUES
('Ramna City', 'রমনা শহর', 'city', 15, 'CTY-01', 'রমনা শহর এলাকা');

-- ৮. ওয়ার্ড (Wards)
-- ধানমন্ডি পৌরসভার অধীনে
INSERT INTO `areas` (`name`, `name_bn`, `type`, `parent_id`, `code`, `description`) VALUES
('Dhanmondi Ward 01', 'ধানমন্ডি ওয়ার্ড ০১', 'ward', 17, 'WAR-01', 'ধানমন্ডি ওয়ার্ড নং ০১'),
('Dhanmondi Ward 02', 'ধানমন্ডি ওয়ার্ড ০২', 'ward', 17, 'WAR-02', 'ধানমন্ডি ওয়ার্ড নং ০২'),
('Dhanmondi Ward 03', 'ধানমন্ডি ওয়ার্ড ০৩', 'ward', 17, 'WAR-03', 'ধানমন্ডি ওয়ার্ড নং ০৩');

-- গুলশান গ্রামের অধীনে
INSERT INTO `areas` (`name`, `name_bn`, `type`, `parent_id`, `code`, `description`) VALUES
('Gulshan Ward 01', 'গুলশান ওয়ার্ড ০১', 'ward', 20, 'WAR-04', 'গুলশান ওয়ার্ড নং ০১'),
('Gulshan Ward 02', 'গুলশান ওয়ার্ড ০২', 'ward', 20, 'WAR-05', 'গুলশান ওয়ার্ড নং ০২');

-- বনানী ইউনিয়নের অধীনে
INSERT INTO `areas` (`name`, `name_bn`, `type`, `parent_id`, `code`, `description`) VALUES
('Banani Village', 'বনানী গ্রাম', 'village', 19, 'VIL-03', 'বনানী গ্রাম');

-- বনানী গ্রামের অধীনে
INSERT INTO `areas` (`name`, `name_bn`, `type`, `parent_id`, `code`, `description`) VALUES
('Banani Ward 01', 'বনানী ওয়ার্ড ০১', 'ward', 27, 'WAR-06', 'বনানী ওয়ার্ড নং ০১'),
('Banani Ward 02', 'বনানী ওয়ার্ড ০২', 'ward', 27, 'WAR-07', 'বনানী ওয়ার্ড নং ০২');

-- রমনা শহরের অধীনে
INSERT INTO `areas` (`name`, `name_bn`, `type`, `parent_id`, `code`, `description`) VALUES
('Ramna Ward 01', 'রমনা ওয়ার্ড ০১', 'ward', 22, 'WAR-08', 'রমনা ওয়ার্ড নং ০১'),
('Ramna Ward 02', 'রমনা ওয়ার্ড ০২', 'ward', 22, 'WAR-09', 'রমনা ওয়ার্ড নং ০২');

-- নমুনা ভোটার ডেটা আপডেট (existing voters table এ area_id আপডেট)
UPDATE `voters` SET `area_id` = 23 WHERE `id` = 1; -- ধানমন্ডি ওয়ার্ড ০১
UPDATE `voters` SET `area_id` = 24 WHERE `id` = 2; -- ধানমন্ডি ওয়ার্ড ০২
UPDATE `voters` SET `area_id` = 25 WHERE `id` = 3; -- ধানমন্ডি ওয়ার্ড ০৩
UPDATE `voters` SET `area_id` = 26 WHERE `id` = 4; -- গুলশান ওয়ার্ড ০১
UPDATE `voters` SET `area_id` = 27 WHERE `id` = 5; -- গুলশান ওয়ার্ড ০২

-- Add some indexes for better performance
ALTER TABLE `areas` ADD INDEX `idx_type_parent` (`type`, `parent_id`);
ALTER TABLE `areas` ADD INDEX `idx_parent_active` (`parent_id`, `is_active`);

COMMIT;
