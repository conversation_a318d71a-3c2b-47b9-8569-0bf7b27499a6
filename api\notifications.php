<?php
/**
 * Notifications API
 * Handle notification checks and updates
 */

header('Content-Type: application/json');
header('Cache-Control: no-cache, must-revalidate');

require_once '../config/config.php';

// Check if user is logged in
if (!isLoggedIn()) {
    http_response_code(401);
    echo json_encode(['error' => 'Unauthorized']);
    exit;
}

$action = $_GET['action'] ?? '';
$user = getCurrentUser();

try {
    switch ($action) {
        case 'check':
            // Check for new notifications
            $notifications = getNotifications($user['id']);
            echo json_encode([
                'success' => true,
                'data' => [
                    'notifications' => $notifications,
                    'count' => count($notifications)
                ],
                'timestamp' => time()
            ]);
            break;
            
        case 'mark_read':
            $notification_id = intval($_POST['id'] ?? 0);
            if ($notification_id > 0) {
                markNotificationAsRead($notification_id, $user['id']);
                echo json_encode(['success' => true]);
            } else {
                echo json_encode(['success' => false, 'error' => 'Invalid notification ID']);
            }
            break;
            
        case 'mark_all_read':
            markAllNotificationsAsRead($user['id']);
            echo json_encode(['success' => true]);
            break;
            
        default:
            http_response_code(400);
            echo json_encode(['error' => 'Invalid action']);
            break;
    }
    
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode(['error' => 'Server error: ' . $e->getMessage()]);
}

// Helper functions
function getNotifications($user_id, $limit = 10) {
    try {
        $db = getDB();
        
        // For now, return empty array since we don't have notifications table
        // In future, this would query actual notifications
        return [];
        
        /*
        $stmt = $db->prepare("
            SELECT id, title, message, type, is_read, created_at 
            FROM notifications 
            WHERE user_id = ? 
            ORDER BY created_at DESC 
            LIMIT ?
        ");
        $stmt->execute([$user_id, $limit]);
        return $stmt->fetchAll();
        */
        
    } catch (Exception $e) {
        error_log("Failed to get notifications: " . $e->getMessage());
        return [];
    }
}

function markNotificationAsRead($notification_id, $user_id) {
    try {
        $db = getDB();
        
        // For now, just return true since we don't have notifications table
        return true;
        
        /*
        $stmt = $db->prepare("
            UPDATE notifications 
            SET is_read = 1 
            WHERE id = ? AND user_id = ?
        ");
        return $stmt->execute([$notification_id, $user_id]);
        */
        
    } catch (Exception $e) {
        error_log("Failed to mark notification as read: " . $e->getMessage());
        return false;
    }
}

function markAllNotificationsAsRead($user_id) {
    try {
        $db = getDB();
        
        // For now, just return true since we don't have notifications table
        return true;
        
        /*
        $stmt = $db->prepare("
            UPDATE notifications 
            SET is_read = 1 
            WHERE user_id = ?
        ");
        return $stmt->execute([$user_id]);
        */
        
    } catch (Exception $e) {
        error_log("Failed to mark all notifications as read: " . $e->getMessage());
        return false;
    }
}
?>
