<?php
/**
 * Setup Candidates Table
 * Create candidates table with all necessary fields
 */

require_once 'config/config.php';

try {
    $db = getDB();

    echo "<h2>🗳️ Setting up Candidates System...</h2>";

    // First, check if required tables exist
    echo "<h3>📋 Checking Prerequisites...</h3>";

    $required_tables = ['areas', 'users'];
    foreach ($required_tables as $table) {
        $stmt = $db->query("SHOW TABLES LIKE '$table'");
        if ($stmt->rowCount() == 0) {
            echo "<p>❌ Required table '$table' not found. Please create it first.</p>";
            echo "<p>💡 <strong>Solution:</strong> Run the main setup script first to create basic tables.</p>";
            exit;
        } else {
            echo "<p>✅ Table '$table' exists</p>";
        }
    }

    // Create positions table first (no foreign keys)
    echo "<h3>🏛️ Creating Positions Table...</h3>";
    $sql = "CREATE TABLE IF NOT EXISTS positions (
        id INT AUTO_INCREMENT PRIMARY KEY,
        position_code VARCHAR(20) UNIQUE NOT NULL,
        title VARCHAR(255) NOT NULL,
        title_bn VARCHAR(255),
        description TEXT,
        description_bn TEXT,
        election_level ENUM('ward', 'village', 'upazila', 'district', 'national') NOT NULL,
        area_type ENUM('ward', 'village', 'union', 'municipality', 'upazila', 'district', 'division', 'national') DEFAULT NULL,
        is_active TINYINT(1) DEFAULT 1,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        INDEX idx_position_code (position_code),
        INDEX idx_election_level (election_level),
        INDEX idx_area_type (area_type),
        INDEX idx_is_active (is_active)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";

    $db->exec($sql);
    echo "<p>✅ Positions table created successfully!</p>";

    // Create candidates table (with foreign keys)
    echo "<h3>👥 Creating Candidates Table...</h3>";
    $sql = "CREATE TABLE IF NOT EXISTS candidates (
        id INT AUTO_INCREMENT PRIMARY KEY,
        candidate_id VARCHAR(50) UNIQUE NOT NULL,
        name VARCHAR(255) NOT NULL,
        name_bn VARCHAR(255),
        photo VARCHAR(255),
        symbol VARCHAR(255),
        symbol_name VARCHAR(255),
        symbol_name_bn VARCHAR(255),
        election_level ENUM('ward', 'village', 'upazila', 'district', 'national') NOT NULL,
        area_id INT DEFAULT NULL,
        position_id INT DEFAULT NULL,
        position VARCHAR(100),
        position_bn VARCHAR(100),
        party_name VARCHAR(255),
        party_name_bn VARCHAR(255),
        description TEXT,
        description_bn TEXT,
        contact_mobile VARCHAR(20),
        contact_email VARCHAR(255),
        is_active TINYINT(1) DEFAULT 1,
        created_by INT DEFAULT NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        INDEX idx_candidate_id (candidate_id),
        INDEX idx_election_level (election_level),
        INDEX idx_area_id (area_id),
        INDEX idx_position_id (position_id),
        INDEX idx_is_active (is_active)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";

    $db->exec($sql);
    echo "<p>✅ Candidates table created successfully!</p>";

    // Add foreign key constraints separately (safer approach)
    echo "<h3>🔗 Adding Foreign Key Constraints...</h3>";

    try {
        // Check if foreign key already exists before adding
        $stmt = $db->query("SELECT CONSTRAINT_NAME FROM information_schema.KEY_COLUMN_USAGE
                           WHERE TABLE_NAME = 'candidates' AND CONSTRAINT_NAME = 'fk_candidates_area'");
        if ($stmt->rowCount() == 0) {
            $db->exec("ALTER TABLE candidates ADD CONSTRAINT fk_candidates_area
                      FOREIGN KEY (area_id) REFERENCES areas(id) ON DELETE SET NULL");
            echo "<p>✅ Area foreign key constraint added</p>";
        } else {
            echo "<p>ℹ️ Area foreign key constraint already exists</p>";
        }
    } catch (Exception $e) {
        echo "<p>⚠️ Could not add area foreign key: " . $e->getMessage() . "</p>";
    }

    try {
        $stmt = $db->query("SELECT CONSTRAINT_NAME FROM information_schema.KEY_COLUMN_USAGE
                           WHERE TABLE_NAME = 'candidates' AND CONSTRAINT_NAME = 'fk_candidates_position'");
        if ($stmt->rowCount() == 0) {
            $db->exec("ALTER TABLE candidates ADD CONSTRAINT fk_candidates_position
                      FOREIGN KEY (position_id) REFERENCES positions(id) ON DELETE SET NULL");
            echo "<p>✅ Position foreign key constraint added</p>";
        } else {
            echo "<p>ℹ️ Position foreign key constraint already exists</p>";
        }
    } catch (Exception $e) {
        echo "<p>⚠️ Could not add position foreign key: " . $e->getMessage() . "</p>";
    }

    try {
        $stmt = $db->query("SELECT CONSTRAINT_NAME FROM information_schema.KEY_COLUMN_USAGE
                           WHERE TABLE_NAME = 'candidates' AND CONSTRAINT_NAME = 'fk_candidates_user'");
        if ($stmt->rowCount() == 0) {
            $db->exec("ALTER TABLE candidates ADD CONSTRAINT fk_candidates_user
                      FOREIGN KEY (created_by) REFERENCES users(id) ON DELETE SET NULL");
            echo "<p>✅ User foreign key constraint added</p>";
        } else {
            echo "<p>ℹ️ User foreign key constraint already exists</p>";
        }
    } catch (Exception $e) {
        echo "<p>⚠️ Could not add user foreign key: " . $e->getMessage() . "</p>";
    }
    
    // Create positions table
    $sql = "CREATE TABLE IF NOT EXISTS positions (
        id INT AUTO_INCREMENT PRIMARY KEY,
        position_code VARCHAR(20) UNIQUE NOT NULL,
        title VARCHAR(255) NOT NULL,
        title_bn VARCHAR(255),
        description TEXT,
        description_bn TEXT,
        election_level ENUM('ward', 'village', 'upazila', 'district', 'national') NOT NULL,
        area_type ENUM('ward', 'village', 'union', 'municipality', 'upazila', 'district', 'division', 'national') DEFAULT NULL,
        is_active TINYINT(1) DEFAULT 1,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        INDEX idx_position_code (position_code),
        INDEX idx_election_level (election_level),
        INDEX idx_area_type (area_type),
        INDEX idx_is_active (is_active)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";

    $db->exec($sql);
    echo "<p>✅ Positions table created successfully!</p>";

    // Create candidate_elections table for multiple elections
    echo "<h3>🗳️ Creating Candidate Elections Table...</h3>";
    $sql = "CREATE TABLE IF NOT EXISTS candidate_elections (
        id INT AUTO_INCREMENT PRIMARY KEY,
        candidate_id INT NOT NULL,
        position_id INT DEFAULT NULL,
        election_id INT DEFAULT NULL,
        election_name VARCHAR(255) NOT NULL,
        election_name_bn VARCHAR(255),
        election_date DATE,
        status ENUM('registered', 'approved', 'rejected', 'withdrawn') DEFAULT 'registered',
        vote_count INT DEFAULT 0,
        is_winner TINYINT(1) DEFAULT 0,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        INDEX idx_candidate_id (candidate_id),
        INDEX idx_position_id (position_id),
        INDEX idx_election_id (election_id),
        INDEX idx_status (status)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";

    $db->exec($sql);
    echo "<p>✅ Candidate Elections table created successfully!</p>";

    // Add foreign keys for candidate_elections separately
    try {
        $stmt = $db->query("SELECT CONSTRAINT_NAME FROM information_schema.KEY_COLUMN_USAGE
                           WHERE TABLE_NAME = 'candidate_elections' AND CONSTRAINT_NAME = 'fk_candidate_elections_candidate'");
        if ($stmt->rowCount() == 0) {
            $db->exec("ALTER TABLE candidate_elections ADD CONSTRAINT fk_candidate_elections_candidate
                      FOREIGN KEY (candidate_id) REFERENCES candidates(id) ON DELETE CASCADE");
            echo "<p>✅ Candidate elections foreign key constraint added</p>";
        }
    } catch (Exception $e) {
        echo "<p>⚠️ Could not add candidate elections foreign key: " . $e->getMessage() . "</p>";
    }

    try {
        $stmt = $db->query("SELECT CONSTRAINT_NAME FROM information_schema.KEY_COLUMN_USAGE
                           WHERE TABLE_NAME = 'candidate_elections' AND CONSTRAINT_NAME = 'fk_candidate_elections_position'");
        if ($stmt->rowCount() == 0) {
            $db->exec("ALTER TABLE candidate_elections ADD CONSTRAINT fk_candidate_elections_position
                      FOREIGN KEY (position_id) REFERENCES positions(id) ON DELETE SET NULL");
            echo "<p>✅ Candidate elections position foreign key constraint added</p>";
        }
    } catch (Exception $e) {
        echo "<p>⚠️ Could not add candidate elections position foreign key: " . $e->getMessage() . "</p>";
    }
    
    // Create uploads directory for candidate photos and symbols
    $upload_dirs = [
        'uploads/candidates/photos',
        'uploads/candidates/symbols',
        'uploads/candidates/thumbnails'
    ];
    
    foreach ($upload_dirs as $dir) {
        if (!file_exists($dir)) {
            mkdir($dir, 0755, true);
            echo "<p>✅ Created directory: $dir</p>";
        }
    }
    
    // Insert sample election levels data
    echo "<h3>📊 Sample Election Levels:</h3>";
    
    $election_levels = [
        'ward' => 'ওয়ার্ড কমিশনার',
        'village' => 'গ্রাম প্রধান/চেয়ারম্যান',
        'upazila' => 'উপজেলা চেয়ারম্যান',
        'district' => 'জেলা প্রশাসক',
        'national' => 'জাতীয় সংসদ সদস্য'
    ];
    
    echo "<ul>";
    foreach ($election_levels as $level => $position) {
        echo "<li><strong>" . ucfirst($level) . ":</strong> $position</li>";
    }
    echo "</ul>";
    
    // Insert sample symbols
    echo "<h3>🎯 Creating Sample Election Symbols...</h3>";
    
    $sample_symbols = [
        ['name' => 'Boat', 'name_bn' => 'নৌকা', 'file' => 'boat.png'],
        ['name' => 'Rice Sheaf', 'name_bn' => 'ধানের শীষ', 'file' => 'rice.png'],
        ['name' => 'Tiger', 'name_bn' => 'বাঘ', 'file' => 'tiger.png'],
        ['name' => 'Elephant', 'name_bn' => 'হাতি', 'file' => 'elephant.png'],
        ['name' => 'Lotus', 'name_bn' => 'পদ্ম', 'file' => 'lotus.png'],
        ['name' => 'Eagle', 'name_bn' => 'ঈগল', 'file' => 'eagle.png'],
        ['name' => 'Rose', 'name_bn' => 'গোলাপ', 'file' => 'rose.png'],
        ['name' => 'Star', 'name_bn' => 'তারা', 'file' => 'star.png'],
        ['name' => 'Sun', 'name_bn' => 'সূর্য', 'file' => 'sun.png'],
        ['name' => 'Moon', 'name_bn' => 'চাঁদ', 'file' => 'moon.png']
    ];
    
    // Create symbols table
    echo "<h3>🎯 Creating Election Symbols Table...</h3>";
    $sql = "CREATE TABLE IF NOT EXISTS election_symbols (
        id INT AUTO_INCREMENT PRIMARY KEY,
        name VARCHAR(100) NOT NULL,
        name_bn VARCHAR(100),
        symbol_file VARCHAR(255),
        is_available TINYINT(1) DEFAULT 1,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        INDEX idx_name (name),
        INDEX idx_is_available (is_available)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";

    $db->exec($sql);
    echo "<p>✅ Election Symbols table created successfully!</p>";
    
    // Insert sample symbols
    $stmt = $db->prepare("INSERT IGNORE INTO election_symbols (name, name_bn, symbol_file) VALUES (?, ?, ?)");
    foreach ($sample_symbols as $symbol) {
        $stmt->execute([$symbol['name'], $symbol['name_bn'], $symbol['file']]);
    }
    
    echo "<p>✅ Sample election symbols created!</p>";

    // Insert sample positions
    echo "<h3>🏛️ Creating Sample Positions...</h3>";

    $sample_positions = [
        // Ward Level
        ['WC', 'Ward Commissioner', 'ওয়ার্ড কমিশনার', 'Elected representative of a ward', 'ওয়ার্ডের নির্বাচিত প্রতিনিধি', 'ward', 'ward'],
        ['WM', 'Ward Member', 'ওয়ার্ড সদস্য', 'Member of ward council', 'ওয়ার্ড কাউন্সিলের সদস্য', 'ward', 'ward'],

        // Village Level
        ['VP', 'Village Chairman', 'গ্রাম চেয়ারম্যান', 'Chairman of village council', 'গ্রাম কাউন্সিলের চেয়ারম্যান', 'village', 'village'],
        ['VM', 'Village Member', 'গ্রাম সদস্য', 'Member of village council', 'গ্রাম কাউন্সিলের সদস্য', 'village', 'village'],

        // Union Level
        ['UC', 'Union Chairman', 'ইউনিয়ন চেয়ারম্যান', 'Chairman of union parishad', 'ইউনিয়ন পরিষদের চেয়ারম্যান', 'village', 'union'],
        ['UM', 'Union Member', 'ইউনিয়ন সদস্য', 'Member of union parishad', 'ইউনিয়ন পরিষদের সদস্য', 'village', 'union'],
        ['UWM', 'Union Women Member', 'ইউনিয়ন মহিলা সদস্য', 'Women reserved member of union parishad', 'ইউনিয়ন পরিষদের মহিলা সংরক্ষিত সদস্য', 'village', 'union'],

        // Municipality Level
        ['MC', 'Mayor', 'মেয়র', 'Mayor of municipality', 'পৌরসভার মেয়র', 'village', 'municipality'],
        ['CC', 'City Councilor', 'সিটি কাউন্সিলর', 'Councilor of city corporation', 'সিটি কর্পোরেশনের কাউন্সিলর', 'village', 'municipality'],

        // Upazila Level
        ['UZC', 'Upazila Chairman', 'উপজেলা চেয়ারম্যান', 'Chairman of upazila parishad', 'উপজেলা পরিষদের চেয়ারম্যান', 'upazila', 'upazila'],
        ['UZVP', 'Upazila Vice Chairman', 'উপজেলা ভাইস চেয়ারম্যান', 'Vice Chairman of upazila parishad', 'উপজেলা পরিষদের ভাইস চেয়ারম্যান', 'upazila', 'upazila'],
        ['UZWVP', 'Upazila Women Vice Chairman', 'উপজেলা মহিলা ভাইস চেয়ারম্যান', 'Women Vice Chairman of upazila parishad', 'উপজেলা পরিষদের মহিলা ভাইস চেয়ারম্যান', 'upazila', 'upazila'],

        // District Level
        ['DC', 'District Commissioner', 'জেলা প্রশাসক', 'Chief administrative officer of district', 'জেলার প্রধান প্রশাসনিক কর্মকর্তা', 'district', 'district'],
        ['ZPC', 'Zilla Parishad Chairman', 'জেলা পরিষদ চেয়ারম্যান', 'Chairman of zilla parishad', 'জেলা পরিষদের চেয়ারম্যান', 'district', 'district'],

        // National Level
        ['MP', 'Member of Parliament', 'সংসদ সদস্য', 'Elected member of national parliament', 'জাতীয় সংসদের নির্বাচিত সদস্য', 'national', 'national'],
        ['PM', 'Prime Minister', 'প্রধানমন্ত্রী', 'Head of government', 'সরকার প্রধান', 'national', 'national'],
        ['PRES', 'President', 'রাষ্ট্রপতি', 'Head of state', 'রাষ্ট্র প্রধান', 'national', 'national']
    ];

    $stmt = $db->prepare("INSERT IGNORE INTO positions (position_code, title, title_bn, description, description_bn, election_level, area_type) VALUES (?, ?, ?, ?, ?, ?, ?)");

    foreach ($sample_positions as $position) {
        $stmt->execute($position);
    }

    echo "<p>✅ Sample positions created!</p>";

    echo "<div style='background: #d4edda; padding: 20px; border-radius: 10px; margin: 20px 0;'>";
    echo "<h3>🎉 Candidates System Setup Complete!</h3>";
    echo "<p><strong>Next Steps:</strong></p>";
    echo "<ul>";
    echo "<li>✅ Database tables created</li>";
    echo "<li>✅ Upload directories created</li>";
    echo "<li>✅ Sample symbols added</li>";
    echo "<li>✅ Sample positions added</li>";
    echo "<li>🔗 <a href='admin/candidates.php'>Go to Candidates Management</a></li>";
    echo "<li>🔗 <a href='admin/positions.php'>Manage Positions</a></li>";
    echo "</ul>";
    echo "</div>";
    
} catch (Exception $e) {
    echo "<div style='background: #f8d7da; padding: 20px; border-radius: 10px;'>";
    echo "<h3>❌ Error setting up candidates system:</h3>";
    echo "<p>" . $e->getMessage() . "</p>";
    echo "</div>";
}
?>

<!DOCTYPE html>
<html>
<head>
    <title>Setup Candidates System</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
</head>
<body>
<div class="container mt-4">
    <div class="row">
        <div class="col-12">
            <!-- Content generated above -->
        </div>
    </div>
</div>
</body>
</html>
