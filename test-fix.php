<?php
/**
 * Test the getDB() function fix
 */

// Display all errors
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h2>Testing getDB() Function Fix</h2>";

try {
    // Include the config file which should now work without redeclaration error
    require_once 'config/config.php';
    
    echo "<p style='color: green;'>✅ Config file loaded successfully - no redeclaration error!</p>";
    
    // Test the getDB() function
    $db = getDB();
    echo "<p style='color: green;'>✅ getDB() function works!</p>";
    
    // Test a simple query
    $stmt = $db->query("SELECT 1 as test");
    $result = $stmt->fetch();
    
    if ($result && $result['test'] == 1) {
        echo "<p style='color: green;'>✅ Database connection successful!</p>";
    } else {
        echo "<p style='color: red;'>❌ Database query failed</p>";
    }
    
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ Error: " . $e->getMessage() . "</p>";
}

echo "<br><p><a href='index.php'>← Back to Main Site</a></p>";
?>
