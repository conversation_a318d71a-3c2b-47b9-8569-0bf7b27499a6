<?php
/**
 * Sidebar Layout Test Page
 * সাইডবার লেআউট টেস্ট পেজ
 */

require_once 'config/config.php';

$page_title = 'সাইডবার লেআউট টেস্ট - Sidebar Layout Test';
$show_top_nav = true;

// Start output buffering for content
ob_start();
?>

<div class="container-fluid py-4">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="alert alert-success">
                <h4 class="alert-heading">
                    <i class="fas fa-check-circle"></i> সাইডবার সফলভাবে কাজ করছে!
                </h4>
                <p class="mb-0">
                    সাইডবার এখন পাশাপাশি layout এ কাজ করছে। ডেস্কটপে সাইডবার সবসময় দেখা যাবে এবং মোবাইলে toggle করা যাবে।
                </p>
            </div>
        </div>
    </div>

    <!-- Layout Info -->
    <div class="row mb-4">
        <div class="col-md-6">
            <div class="card">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0">
                        <i class="fas fa-desktop"></i> ডেস্কটপ লেআউট
                    </h5>
                </div>
                <div class="card-body">
                    <ul class="list-unstyled mb-0">
                        <li class="mb-2">
                            <i class="fas fa-check text-success"></i>
                            সাইডবার সবসময় দেখা যায়
                        </li>
                        <li class="mb-2">
                            <i class="fas fa-check text-success"></i>
                            মেইন কন্টেন্ট পাশে থাকে
                        </li>
                        <li class="mb-2">
                            <i class="fas fa-check text-success"></i>
                            280px সাইডবার width
                        </li>
                        <li class="mb-0">
                            <i class="fas fa-check text-success"></i>
                            Fixed position
                        </li>
                    </ul>
                </div>
            </div>
        </div>
        
        <div class="col-md-6">
            <div class="card">
                <div class="card-header bg-info text-white">
                    <h5 class="mb-0">
                        <i class="fas fa-mobile-alt"></i> মোবাইল লেআউট
                    </h5>
                </div>
                <div class="card-body">
                    <ul class="list-unstyled mb-0">
                        <li class="mb-2">
                            <i class="fas fa-check text-success"></i>
                            সাইডবার overlay হিসেবে
                        </li>
                        <li class="mb-2">
                            <i class="fas fa-check text-success"></i>
                            Toggle button দিয়ে খোলা/বন্ধ
                        </li>
                        <li class="mb-2">
                            <i class="fas fa-check text-success"></i>
                            Full width সাইডবার
                        </li>
                        <li class="mb-0">
                            <i class="fas fa-check text-success"></i>
                            Auto close on link click
                        </li>
                    </ul>
                </div>
            </div>
        </div>
    </div>

    <!-- Test Content -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-vial"></i> টেস্ট কন্টেন্ট
                    </h5>
                </div>
                <div class="card-body">
                    <p>
                        এই পেজটি সাইডবার লেআউট টেস্ট করার জন্য তৈরি। আপনি দেখতে পাচ্ছেন যে:
                    </p>
                    
                    <div class="row">
                        <div class="col-md-4 mb-3">
                            <div class="card bg-light">
                                <div class="card-body text-center">
                                    <i class="fas fa-arrows-alt-h fa-2x text-primary mb-2"></i>
                                    <h6>Responsive Width</h6>
                                    <small class="text-muted">কন্টেন্ট সাইডবার অনুযায়ী adjust হয়</small>
                                </div>
                            </div>
                        </div>
                        
                        <div class="col-md-4 mb-3">
                            <div class="card bg-light">
                                <div class="card-body text-center">
                                    <i class="fas fa-layer-group fa-2x text-success mb-2"></i>
                                    <h6>Proper Z-Index</h6>
                                    <small class="text-muted">সাইডবার সঠিক layer এ আছে</small>
                                </div>
                            </div>
                        </div>
                        
                        <div class="col-md-4 mb-3">
                            <div class="card bg-light">
                                <div class="card-body text-center">
                                    <i class="fas fa-magic fa-2x text-warning mb-2"></i>
                                    <h6>Smooth Animation</h6>
                                    <small class="text-muted">সব transition smooth</small>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Screen Size Info -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-info-circle"></i> বর্তমান স্ক্রিন তথ্য
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-3">
                            <strong>Screen Width:</strong>
                            <span id="screenWidth" class="badge bg-primary">-</span>
                        </div>
                        <div class="col-md-3">
                            <strong>Layout Mode:</strong>
                            <span id="layoutMode" class="badge bg-success">-</span>
                        </div>
                        <div class="col-md-3">
                            <strong>Sidebar State:</strong>
                            <span id="sidebarState" class="badge bg-info">-</span>
                        </div>
                        <div class="col-md-3">
                            <strong>Content Margin:</strong>
                            <span id="contentMargin" class="badge bg-warning">-</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Sample Content -->
    <div class="row">
        <div class="col-md-8">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-file-alt"></i> নমুনা কন্টেন্ট
                    </h5>
                </div>
                <div class="card-body">
                    <p>
                        এটি একটি নমুনা কন্টেন্ট যা দেখাচ্ছে যে সাইডবার এবং মেইন কন্টেন্ট সঠিকভাবে কাজ করছে। 
                        আপনি বিভিন্ন ডিভাইসে এই পেজটি দেখে responsive behavior পরীক্ষা করতে পারেন।
                    </p>
                    
                    <h6>মূল বৈশিষ্ট্যসমূহ:</h6>
                    <ul>
                        <li>ডেস্কটপে সাইডবার সবসময় দৃশ্যমান</li>
                        <li>মোবাইলে toggle করা যায়</li>
                        <li>Smooth animations</li>
                        <li>Responsive design</li>
                        <li>Touch-friendly interface</li>
                    </ul>
                    
                    <div class="alert alert-info mt-3">
                        <strong>পরীক্ষা করুন:</strong> ব্রাউজারের width পরিবর্তন করে দেখুন কিভাবে layout adapt হয়।
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-md-4">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-tools"></i> টেস্ট অ্যাকশন
                    </h5>
                </div>
                <div class="card-body">
                    <div class="d-grid gap-2">
                        <button class="btn btn-primary" onclick="testSidebarToggle()">
                            <i class="fas fa-bars"></i> Toggle Sidebar
                        </button>
                        <button class="btn btn-success" onclick="updateScreenInfo()">
                            <i class="fas fa-sync"></i> Update Info
                        </button>
                        <button class="btn btn-info" onclick="testResponsive()">
                            <i class="fas fa-mobile-alt"></i> Test Responsive
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// Update screen information
function updateScreenInfo() {
    const width = $(window).width();
    const sidebar = $('#modernSidebar');
    const mainContent = $('.main-content');
    
    $('#screenWidth').text(width + 'px');
    
    if (width > 991) {
        $('#layoutMode').text('Desktop').removeClass().addClass('badge bg-success');
        $('#sidebarState').text('Always Visible').removeClass().addClass('badge bg-success');
    } else {
        $('#layoutMode').text('Mobile').removeClass().addClass('badge bg-warning');
        $('#sidebarState').text(sidebar.hasClass('active') ? 'Open' : 'Closed').removeClass().addClass('badge bg-info');
    }
    
    $('#contentMargin').text(mainContent.css('margin-left'));
}

function testSidebarToggle() {
    const sidebar = $('#modernSidebar');
    const overlay = $('#sidebarOverlay');
    
    if ($(window).width() <= 991) {
        sidebar.toggleClass('active');
        overlay.toggleClass('active');
    }
    
    setTimeout(updateScreenInfo, 300);
}

function testResponsive() {
    alert('ব্রাউজারের width পরিবর্তন করে responsive behavior দেখুন:\n\n' +
          '• 992px+ = Desktop mode (sidebar always visible)\n' +
          '• 991px- = Mobile mode (sidebar toggleable)\n' +
          '• 768px- = Full width sidebar on mobile');
}

// Initialize and update on resize
$(document).ready(function() {
    updateScreenInfo();
    $(window).on('resize', updateScreenInfo);
});
</script>

<?php
$content = ob_get_clean();

// Include the sidebar layout
include 'includes/sidebar-layout.php';
?>
