<?php
/**
 * Colorful Modern Sidebar Demo
 * কালারফুল মডার্ন সাইডবার ডেমো
 */

require_once 'config/config.php';

$page_title = '🌈 কালারফুল মডার্ন সাইডবার - Colorful Modern Sidebar';
$show_top_nav = true;

// Start output buffering for content
ob_start();
?>

<div class="container-fluid py-4">
    <!-- Hero Section -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card border-0" style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white;">
                <div class="card-body text-center py-5">
                    <h1 class="display-3 mb-3" style="text-shadow: 2px 2px 4px rgba(0,0,0,0.3);">
                        🎨 কালারফুল সাইডবার
                    </h1>
                    <p class="lead mb-4" style="font-size: 1.3rem;">
                        আধুনিক gradient effects, animations এবং glassmorphism design সহ
                    </p>
                    <div class="d-flex gap-3 justify-content-center flex-wrap">
                        <span class="badge px-4 py-2" style="background: linear-gradient(45deg, #ff6b6b, #feca57); font-size: 1rem;">
                            <i class="fas fa-palette"></i> Gradient Design
                        </span>
                        <span class="badge px-4 py-2" style="background: linear-gradient(45deg, #48dbfb, #0abde3); font-size: 1rem;">
                            <i class="fas fa-magic"></i> Smooth Animations
                        </span>
                        <span class="badge px-4 py-2" style="background: linear-gradient(45deg, #ff9ff3, #f368e0); font-size: 1rem;">
                            <i class="fas fa-gem"></i> Glassmorphism
                        </span>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Features Grid -->
    <div class="row mb-4">
        <div class="col-md-4 mb-3">
            <div class="card h-100 border-0" style="background: linear-gradient(135deg, #ff6b6b, #feca57); color: white;">
                <div class="card-body text-center">
                    <div class="mb-3">
                        <i class="fas fa-paint-brush fa-4x" style="text-shadow: 2px 2px 4px rgba(0,0,0,0.3);"></i>
                    </div>
                    <h5 class="card-title">Gradient Backgrounds</h5>
                    <p class="card-text">
                        সুন্দর gradient backgrounds যা animated এবং eye-catching
                    </p>
                </div>
            </div>
        </div>
        
        <div class="col-md-4 mb-3">
            <div class="card h-100 border-0" style="background: linear-gradient(135deg, #48dbfb, #0abde3); color: white;">
                <div class="card-body text-center">
                    <div class="mb-3">
                        <i class="fas fa-sparkles fa-4x" style="text-shadow: 2px 2px 4px rgba(0,0,0,0.3);"></i>
                    </div>
                    <h5 class="card-title">Hover Effects</h5>
                    <p class="card-text">
                        Interactive hover effects যা user experience বাড়ায়
                    </p>
                </div>
            </div>
        </div>
        
        <div class="col-md-4 mb-3">
            <div class="card h-100 border-0" style="background: linear-gradient(135deg, #ff9ff3, #f368e0); color: white;">
                <div class="card-body text-center">
                    <div class="mb-3">
                        <i class="fas fa-mobile-alt fa-4x" style="text-shadow: 2px 2px 4px rgba(0,0,0,0.3);"></i>
                    </div>
                    <h5 class="card-title">Responsive Design</h5>
                    <p class="card-text">
                        সকল ডিভাইসে perfect responsive behavior
                    </p>
                </div>
            </div>
        </div>
    </div>

    <!-- Design Elements -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card border-0" style="background: rgba(255, 255, 255, 0.9); backdrop-filter: blur(10px);">
                <div class="card-header border-0" style="background: linear-gradient(135deg, #667eea, #764ba2); color: white;">
                    <h5 class="mb-0">
                        <i class="fas fa-star"></i> ডিজাইন এলিমেন্টস
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <h6 style="color: #667eea;">🎨 Visual Effects:</h6>
                            <ul class="list-unstyled">
                                <li class="mb-2">
                                    <i class="fas fa-check" style="color: #48dbfb;"></i>
                                    Animated gradient backgrounds
                                </li>
                                <li class="mb-2">
                                    <i class="fas fa-check" style="color: #48dbfb;"></i>
                                    Glassmorphism effects
                                </li>
                                <li class="mb-2">
                                    <i class="fas fa-check" style="color: #48dbfb;"></i>
                                    Colorful icons with gradients
                                </li>
                                <li class="mb-2">
                                    <i class="fas fa-check" style="color: #48dbfb;"></i>
                                    Smooth transitions
                                </li>
                                <li class="mb-2">
                                    <i class="fas fa-check" style="color: #48dbfb;"></i>
                                    Box shadows and blur effects
                                </li>
                            </ul>
                        </div>
                        <div class="col-md-6">
                            <h6 style="color: #ff6b6b;">⚡ Interactive Features:</h6>
                            <ul class="list-unstyled">
                                <li class="mb-2">
                                    <i class="fas fa-check" style="color: #feca57;"></i>
                                    Hover animations
                                </li>
                                <li class="mb-2">
                                    <i class="fas fa-check" style="color: #feca57;"></i>
                                    Active state highlighting
                                </li>
                                <li class="mb-2">
                                    <i class="fas fa-check" style="color: #feca57;"></i>
                                    Smooth scrolling
                                </li>
                                <li class="mb-2">
                                    <i class="fas fa-check" style="color: #feca57;"></i>
                                    Touch-friendly mobile interface
                                </li>
                                <li class="mb-2">
                                    <i class="fas fa-check" style="color: #feca57;"></i>
                                    Keyboard navigation support
                                </li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Color Palette -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card border-0" style="background: rgba(255, 255, 255, 0.9); backdrop-filter: blur(10px);">
                <div class="card-header border-0" style="background: linear-gradient(135deg, #ff6b6b, #feca57); color: white;">
                    <h5 class="mb-0">
                        <i class="fas fa-palette"></i> কালার প্যালেট
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-2 mb-3">
                            <div class="text-center">
                                <div style="width: 80px; height: 80px; background: linear-gradient(135deg, #ff6b6b, #feca57); border-radius: 50%; margin: 0 auto 10px; box-shadow: 0 5px 15px rgba(255, 107, 107, 0.4);"></div>
                                <small><strong>Primary</strong><br>#ff6b6b → #feca57</small>
                            </div>
                        </div>
                        <div class="col-md-2 mb-3">
                            <div class="text-center">
                                <div style="width: 80px; height: 80px; background: linear-gradient(135deg, #48dbfb, #0abde3); border-radius: 50%; margin: 0 auto 10px; box-shadow: 0 5px 15px rgba(72, 219, 251, 0.4);"></div>
                                <small><strong>Secondary</strong><br>#48dbfb → #0abde3</small>
                            </div>
                        </div>
                        <div class="col-md-2 mb-3">
                            <div class="text-center">
                                <div style="width: 80px; height: 80px; background: linear-gradient(135deg, #667eea, #764ba2); border-radius: 50%; margin: 0 auto 10px; box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);"></div>
                                <small><strong>Accent</strong><br>#667eea → #764ba2</small>
                            </div>
                        </div>
                        <div class="col-md-2 mb-3">
                            <div class="text-center">
                                <div style="width: 80px; height: 80px; background: linear-gradient(135deg, #ff9ff3, #f368e0); border-radius: 50%; margin: 0 auto 10px; box-shadow: 0 5px 15px rgba(255, 159, 243, 0.4);"></div>
                                <small><strong>Highlight</strong><br>#ff9ff3 → #f368e0</small>
                            </div>
                        </div>
                        <div class="col-md-2 mb-3">
                            <div class="text-center">
                                <div style="width: 80px; height: 80px; background: linear-gradient(135deg, #ffd700, #ffed4e); border-radius: 50%; margin: 0 auto 10px; box-shadow: 0 5px 15px rgba(255, 215, 0, 0.4);"></div>
                                <small><strong>Gold</strong><br>#ffd700 → #ffed4e</small>
                            </div>
                        </div>
                        <div class="col-md-2 mb-3">
                            <div class="text-center">
                                <div style="width: 80px; height: 80px; background: linear-gradient(135deg, #4ecdc4, #44a08d); border-radius: 50%; margin: 0 auto 10px; box-shadow: 0 5px 15px rgba(78, 205, 196, 0.4);"></div>
                                <small><strong>Success</strong><br>#4ecdc4 → #44a08d</small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Test Actions -->
    <div class="row">
        <div class="col-12">
            <div class="card border-0" style="background: rgba(255, 255, 255, 0.9); backdrop-filter: blur(10px);">
                <div class="card-header border-0" style="background: linear-gradient(135deg, #48dbfb, #0abde3); color: white;">
                    <h5 class="mb-0">
                        <i class="fas fa-play"></i> ইন্টারঅ্যাক্টিভ টেস্ট
                    </h5>
                </div>
                <div class="card-body text-center">
                    <p class="mb-4">সাইডবারের বিভিন্ন ফিচার টেস্ট করুন:</p>
                    
                    <div class="d-flex gap-3 justify-content-center flex-wrap">
                        <button class="btn btn-lg" style="background: linear-gradient(135deg, #ff6b6b, #feca57); color: white; border: none;" onclick="testHoverEffects()">
                            <i class="fas fa-mouse-pointer"></i> Hover Effects
                        </button>
                        <button class="btn btn-lg" style="background: linear-gradient(135deg, #48dbfb, #0abde3); color: white; border: none;" onclick="testAnimations()">
                            <i class="fas fa-magic"></i> Animations
                        </button>
                        <button class="btn btn-lg" style="background: linear-gradient(135deg, #667eea, #764ba2); color: white; border: none;" onclick="testResponsive()">
                            <i class="fas fa-mobile-alt"></i> Responsive
                        </button>
                        <button class="btn btn-lg" style="background: linear-gradient(135deg, #ff9ff3, #f368e0); color: white; border: none;" onclick="testColors()">
                            <i class="fas fa-palette"></i> Colors
                        </button>
                    </div>
                    
                    <div class="mt-4">
                        <small class="text-muted">
                            <i class="fas fa-info-circle"></i>
                            সাইডবারের menu items এ hover করে effects দেখুন
                        </small>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
function testHoverEffects() {
    alert('সাইডবারের menu items এ mouse hover করে দেখুন:\n\n' +
          '• Color transitions\n' +
          '• Scale effects\n' +
          '• Icon animations\n' +
          '• Glow effects');
}

function testAnimations() {
    // Add temporary animation class to nav items
    $('.nav-item').each(function(index) {
        $(this).delay(index * 100).queue(function() {
            $(this).css('animation', 'pulse 1s ease-in-out');
            setTimeout(() => {
                $(this).css('animation', '');
            }, 1000);
            $(this).dequeue();
        });
    });
    
    APP.showAlert('Navigation items animated! 🎉', 'success', 3000);
}

function testResponsive() {
    const width = $(window).width();
    alert(`Current screen width: ${width}px\n\n` +
          'Responsive breakpoints:\n' +
          '• Mobile: < 992px (Overlay sidebar)\n' +
          '• Desktop: ≥ 992px (Side-by-side layout)\n\n' +
          'Resize your browser to test!');
}

function testColors() {
    // Temporarily change sidebar colors
    const sidebar = $('.modern-sidebar');
    const originalBg = sidebar.css('background');
    
    sidebar.css('background', 'linear-gradient(180deg, #ff6b6b 0%, #feca57 50%, #48dbfb 100%)');
    
    setTimeout(() => {
        sidebar.css('background', originalBg);
    }, 3000);
    
    APP.showAlert('Color scheme temporarily changed! 🌈', 'info', 3000);
}
</script>

<?php
$content = ob_get_clean();

// Include the sidebar layout
include 'includes/sidebar-layout.php';
?>
