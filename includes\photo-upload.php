<?php
/**
 * Photo Upload Helper Functions
 * Handle voter photo uploads securely
 */

// Photo upload settings
define('VOTER_PHOTO_MAX_SIZE', 2 * 1024 * 1024); // 2MB
define('VOTER_PHOTO_ALLOWED_TYPES', ['jpg', 'jpeg', 'png', 'gif', 'webp']);
define('VOTER_PHOTO_UPLOAD_DIR', __DIR__ . '/../uploads/voters/');
define('VOTER_PHOTO_URL_BASE', APP_URL . '/uploads/voters/');

/**
 * Upload voter photo
 */
function uploadVoterPhoto($file, $voter_id, $uploaded_by) {
    try {
        // Validate file
        $validation = validatePhotoUpload($file);
        if (!$validation['success']) {
            return $validation;
        }
        
        // Create upload directory if not exists
        if (!file_exists(VOTER_PHOTO_UPLOAD_DIR)) {
            mkdir(VOTER_PHOTO_UPLOAD_DIR, 0755, true);
        }
        
        // Generate unique filename
        $extension = strtolower(pathinfo($file['name'], PATHINFO_EXTENSION));
        $filename = 'voter_' . $voter_id . '_' . time() . '_' . uniqid() . '.' . $extension;
        $filepath = VOTER_PHOTO_UPLOAD_DIR . $filename;
        
        // Move uploaded file
        if (!move_uploaded_file($file['tmp_name'], $filepath)) {
            return ['success' => false, 'message' => 'ফাইল আপলোড করতে ব্যর্থ'];
        }
        
        // Resize image if needed (only if GD extension is available)
        if (extension_loaded('gd')) {
            $resized = resizeVoterPhoto($filepath, 300, 300);
            if (!$resized) {
                // If resize fails, keep original but log warning
                error_log("Failed to resize voter photo: $filepath");
            }
        } else {
            // GD not available, keep original size
            error_log("GD extension not available, keeping original photo size: $filepath");
        }
        
        // Update database
        $db = getDB();
        
        // Remove old photo if exists
        $stmt = $db->prepare("SELECT photo FROM voters WHERE id = ?");
        $stmt->execute([$voter_id]);
        $old_photo = $stmt->fetchColumn();
        
        if ($old_photo && file_exists(VOTER_PHOTO_UPLOAD_DIR . $old_photo)) {
            unlink(VOTER_PHOTO_UPLOAD_DIR . $old_photo);
        }
        
        // Update voter photo
        $stmt = $db->prepare("UPDATE voters SET photo = ? WHERE id = ?");
        $stmt->execute([$filename, $voter_id]);
        
        // Log upload
        $stmt = $db->prepare("
            INSERT INTO voter_uploads (voter_id, original_filename, stored_filename, file_size, mime_type, upload_type, uploaded_by) 
            VALUES (?, ?, ?, ?, ?, 'photo', ?)
        ");
        $stmt->execute([
            $voter_id,
            $file['name'],
            $filename,
            $file['size'],
            $file['type'],
            $uploaded_by
        ]);
        
        logActivity('UPLOAD_PHOTO', 'voter', $voter_id);
        
        return [
            'success' => true,
            'message' => 'ছবি সফলভাবে আপলোড হয়েছে',
            'filename' => $filename,
            'url' => VOTER_PHOTO_URL_BASE . $filename
        ];
        
    } catch (Exception $e) {
        error_log("Photo upload failed: " . $e->getMessage());
        return ['success' => false, 'message' => 'ছবি আপলোড করতে ব্যর্থ: ' . $e->getMessage()];
    }
}

/**
 * Validate photo upload
 */
function validatePhotoUpload($file) {
    // Check if file was uploaded
    if (!isset($file['tmp_name']) || !is_uploaded_file($file['tmp_name'])) {
        return ['success' => false, 'message' => 'কোন ফাইল আপলোড করা হয়নি'];
    }
    
    // Check file size
    if ($file['size'] > VOTER_PHOTO_MAX_SIZE) {
        $max_mb = VOTER_PHOTO_MAX_SIZE / (1024 * 1024);
        return ['success' => false, 'message' => "ফাইল সাইজ {$max_mb}MB এর বেশি হতে পারবে না"];
    }
    
    // Check file type
    $extension = strtolower(pathinfo($file['name'], PATHINFO_EXTENSION));
    if (!in_array($extension, VOTER_PHOTO_ALLOWED_TYPES)) {
        $allowed = implode(', ', VOTER_PHOTO_ALLOWED_TYPES);
        return ['success' => false, 'message' => "শুধুমাত্র এই ফরম্যাট অনুমোদিত: $allowed"];
    }
    
    // Check MIME type
    $finfo = finfo_open(FILEINFO_MIME_TYPE);
    $mime_type = finfo_file($finfo, $file['tmp_name']);
    finfo_close($finfo);
    
    $allowed_mimes = [
        'image/jpeg',
        'image/jpg', 
        'image/png',
        'image/gif',
        'image/webp'
    ];
    
    if (!in_array($mime_type, $allowed_mimes)) {
        return ['success' => false, 'message' => 'অবৈধ ফাইল টাইপ'];
    }
    
    // Check if it's actually an image
    $image_info = getimagesize($file['tmp_name']);
    if ($image_info === false) {
        return ['success' => false, 'message' => 'ফাইলটি একটি বৈধ ছবি নয়'];
    }
    
    return ['success' => true];
}

/**
 * Resize voter photo
 */
function resizeVoterPhoto($filepath, $max_width = 300, $max_height = 300) {
    // Check if GD extension is loaded
    if (!extension_loaded('gd')) {
        error_log("GD extension not available for image resizing");
        return false;
    }

    try {
        $image_info = getimagesize($filepath);
        if (!$image_info) return false;
        
        list($width, $height, $type) = $image_info;
        
        // Skip if already small enough
        if ($width <= $max_width && $height <= $max_height) {
            return true;
        }
        
        // Calculate new dimensions
        $ratio = min($max_width / $width, $max_height / $height);
        $new_width = round($width * $ratio);
        $new_height = round($height * $ratio);
        
        // Create image resource
        switch ($type) {
            case IMAGETYPE_JPEG:
                $source = imagecreatefromjpeg($filepath);
                break;
            case IMAGETYPE_PNG:
                $source = imagecreatefrompng($filepath);
                break;
            case IMAGETYPE_GIF:
                $source = imagecreatefromgif($filepath);
                break;
            case IMAGETYPE_WEBP:
                $source = imagecreatefromwebp($filepath);
                break;
            default:
                return false;
        }
        
        if (!$source) return false;
        
        // Create new image
        $destination = imagecreatetruecolor($new_width, $new_height);
        
        // Preserve transparency for PNG and GIF
        if ($type == IMAGETYPE_PNG || $type == IMAGETYPE_GIF) {
            imagealphablending($destination, false);
            imagesavealpha($destination, true);
            $transparent = imagecolorallocatealpha($destination, 255, 255, 255, 127);
            imagefilledrectangle($destination, 0, 0, $new_width, $new_height, $transparent);
        }
        
        // Resize
        imagecopyresampled($destination, $source, 0, 0, 0, 0, $new_width, $new_height, $width, $height);
        
        // Save resized image
        $result = false;
        switch ($type) {
            case IMAGETYPE_JPEG:
                $result = imagejpeg($destination, $filepath, 85);
                break;
            case IMAGETYPE_PNG:
                $result = imagepng($destination, $filepath, 6);
                break;
            case IMAGETYPE_GIF:
                $result = imagegif($destination, $filepath);
                break;
            case IMAGETYPE_WEBP:
                $result = imagewebp($destination, $filepath, 85);
                break;
        }
        
        // Clean up
        imagedestroy($source);
        imagedestroy($destination);
        
        return $result;
        
    } catch (Exception $e) {
        error_log("Image resize failed: " . $e->getMessage());
        return false;
    }
}

/**
 * Get voter photo URL
 */
function getVoterPhotoUrl($photo_filename) {
    if (empty($photo_filename)) {
        return APP_URL . '/assets/images/default-avatar.png';
    }
    
    $filepath = VOTER_PHOTO_UPLOAD_DIR . $photo_filename;
    if (!file_exists($filepath)) {
        return APP_URL . '/assets/images/default-avatar.png';
    }
    
    return VOTER_PHOTO_URL_BASE . $photo_filename;
}

/**
 * Delete voter photo
 */
function deleteVoterPhoto($voter_id) {
    try {
        $db = getDB();
        
        // Get current photo
        $stmt = $db->prepare("SELECT photo FROM voters WHERE id = ?");
        $stmt->execute([$voter_id]);
        $photo = $stmt->fetchColumn();
        
        if ($photo) {
            // Delete file
            $filepath = VOTER_PHOTO_UPLOAD_DIR . $photo;
            if (file_exists($filepath)) {
                unlink($filepath);
            }
            
            // Update database
            $stmt = $db->prepare("UPDATE voters SET photo = NULL WHERE id = ?");
            $stmt->execute([$voter_id]);
            
            logActivity('DELETE_PHOTO', 'voter', $voter_id);
        }
        
        return ['success' => true, 'message' => 'ছবি মুছে ফেলা হয়েছে'];
        
    } catch (Exception $e) {
        error_log("Photo deletion failed: " . $e->getMessage());
        return ['success' => false, 'message' => 'ছবি মুছতে ব্যর্থ'];
    }
}
?>
