<?php
/**
 * Candidates Management
 * Manage election candidates with photos and symbols
 */

require_once '../config/config.php';

// Check if user is logged in and is admin
if (!isLoggedIn() || !isAdmin()) {
    redirect(APP_URL . '/auth/login.php');
}

$action = $_GET['action'] ?? 'list';
$message = '';
$error = '';

// Handle form submissions
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (!validateCSRFToken($_POST['csrf_token'] ?? '')) {
        $error = 'Invalid CSRF token';
    } else {
        switch ($action) {
            case 'add':
                $result = addCandidate($_POST, $_FILES);
                if ($result['success']) {
                    setFlashMessage('success', $result['message']);
                    redirect($_SERVER['PHP_SELF']);
                } else {
                    $error = $result['message'];
                }
                break;
                
            case 'edit':
                $result = updateCandidate($_POST, $_FILES);
                if ($result['success']) {
                    setFlashMessage('success', $result['message']);
                    redirect($_SERVER['PHP_SELF']);
                } else {
                    $error = $result['message'];
                }
                break;
                
            case 'delete':
                $result = deleteCandidate($_POST['candidate_id']);
                if ($result['success']) {
                    setFlashMessage('success', $result['message']);
                } else {
                    setFlashMessage('error', $result['message']);
                }
                redirect($_SERVER['PHP_SELF']);
                break;
        }
    }
}

// Get candidates list with pagination
$page = intval($_GET['page'] ?? 1);
$limit = 20;
$offset = ($page - 1) * $limit;

$search = $_GET['search'] ?? '';
$level_filter = $_GET['level'] ?? '';
$area_filter = $_GET['area'] ?? '';

try {
    $db = getDB();
    
    // Build search query
    $where_conditions = ['c.is_active = 1'];
    $params = [];
    
    if (!empty($search)) {
        $where_conditions[] = "(c.name LIKE ? OR c.name_bn LIKE ? OR c.candidate_id LIKE ?)";
        $search_term = "%$search%";
        $params[] = $search_term;
        $params[] = $search_term;
        $params[] = $search_term;
    }
    
    if (!empty($level_filter)) {
        $where_conditions[] = "c.election_level = ?";
        $params[] = $level_filter;
    }
    
    if (!empty($area_filter)) {
        $where_conditions[] = "c.area_id = ?";
        $params[] = $area_filter;
    }
    
    $where_clause = implode(' AND ', $where_conditions);
    
    // Get total count
    $count_sql = "SELECT COUNT(*) FROM candidates c WHERE $where_clause";
    $stmt = $db->prepare($count_sql);
    $stmt->execute($params);
    $total_candidates = $stmt->fetchColumn();
    $total_pages = ceil($total_candidates / $limit);
    
    // Get candidates
    $sql = "SELECT c.*, a.name as area_name, a.name_bn as area_name_bn,
                   p.title as position_title, p.title_bn as position_title_bn,
                   u.username as created_by_name
            FROM candidates c
            LEFT JOIN areas a ON c.area_id = a.id
            LEFT JOIN positions p ON c.position_id = p.id
            LEFT JOIN users u ON c.created_by = u.id
            WHERE $where_clause
            ORDER BY c.created_at DESC
            LIMIT $limit OFFSET $offset";
    
    $stmt = $db->prepare($sql);
    $stmt->execute($params);
    $candidates = $stmt->fetchAll();
    
    // Get areas for filters
    $areas_stmt = $db->query("SELECT id, name, name_bn, type FROM areas WHERE is_active = 1 ORDER BY type, name");
    $areas = $areas_stmt->fetchAll();
    
    // Get election symbols
    $symbols_stmt = $db->query("SELECT * FROM election_symbols WHERE is_available = 1 ORDER BY name");
    $symbols = $symbols_stmt->fetchAll();

    // Get positions
    $positions_stmt = $db->query("SELECT * FROM positions WHERE is_active = 1 ORDER BY election_level, title");
    $positions = $positions_stmt->fetchAll();
    
} catch (Exception $e) {
    $error = 'Database error: ' . $e->getMessage();
    $candidates = [];
    $areas = [];
    $symbols = [];
    $total_candidates = 0;
    $total_pages = 0;
}

// Get flash messages
$success = getFlashMessage('success');
$flash_error = getFlashMessage('error');
$warning = getFlashMessage('warning');

// Election levels
$election_levels = [
    'ward' => 'ওয়ার্ড কমিশনার',
    'village' => 'গ্রাম প্রধান/চেয়ারম্যান', 
    'upazila' => 'উপজেলা চেয়ারম্যান',
    'district' => 'জেলা প্রশাসক',
    'national' => 'জাতীয় সংসদ সদস্য'
];

// Functions
function addCandidate($data, $files) {
    try {
        $db = getDB();
        
        // Generate candidate ID
        $stmt = $db->query("SELECT COUNT(*) + 1 as next_id FROM candidates");
        $next_id = $stmt->fetch()['next_id'];
        $candidate_id = 'C' . str_pad($next_id, 4, '0', STR_PAD_LEFT);
        
        // Handle photo upload
        $photo_path = null;
        if (isset($files['photo']) && $files['photo']['error'] === UPLOAD_ERR_OK) {
            $photo_result = uploadCandidatePhoto($files['photo'], $candidate_id);
            if ($photo_result['success']) {
                $photo_path = $photo_result['path'];
            } else {
                return ['success' => false, 'message' => 'Photo upload failed: ' . $photo_result['message']];
            }
        }
        
        // Handle symbol upload
        $symbol_path = null;
        if (isset($files['symbol']) && $files['symbol']['error'] === UPLOAD_ERR_OK) {
            $symbol_result = uploadCandidateSymbol($files['symbol'], $candidate_id);
            if ($symbol_result['success']) {
                $symbol_path = $symbol_result['path'];
            } else {
                return ['success' => false, 'message' => 'Symbol upload failed: ' . $symbol_result['message']];
            }
        }
        
        // Insert candidate
        $stmt = $db->prepare("
            INSERT INTO candidates (
                candidate_id, name, name_bn, photo, symbol, symbol_name, symbol_name_bn,
                election_level, area_id, position_id, position, position_bn, party_name, party_name_bn,
                description, description_bn, contact_mobile, contact_email, created_by
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        ");

        $stmt->execute([
            $candidate_id,
            sanitizeInput($data['name']),
            sanitizeInput($data['name_bn']) ?: null,
            $photo_path,
            $symbol_path,
            sanitizeInput($data['symbol_name']) ?: null,
            sanitizeInput($data['symbol_name_bn']) ?: null,
            sanitizeInput($data['election_level']),
            intval($data['area_id']) ?: null,
            intval($data['position_id']) ?: null,
            sanitizeInput($data['position']) ?: null,
            sanitizeInput($data['position_bn']) ?: null,
            sanitizeInput($data['party_name']) ?: null,
            sanitizeInput($data['party_name_bn']) ?: null,
            sanitizeInput($data['description']) ?: null,
            sanitizeInput($data['description_bn']) ?: null,
            sanitizeInput($data['contact_mobile']) ?: null,
            sanitizeInput($data['contact_email']) ?: null,
            getCurrentUser()['id']
        ]);
        
        return ['success' => true, 'message' => 'প্রার্থী সফলভাবে যোগ করা হয়েছে'];
        
    } catch (Exception $e) {
        return ['success' => false, 'message' => 'Error: ' . $e->getMessage()];
    }
}

function uploadCandidatePhoto($file, $candidate_id) {
    $allowed_types = ['image/jpeg', 'image/png', 'image/gif'];
    $max_size = 5 * 1024 * 1024; // 5MB
    
    if (!in_array($file['type'], $allowed_types)) {
        return ['success' => false, 'message' => 'Invalid file type. Only JPG, PNG, GIF allowed.'];
    }
    
    if ($file['size'] > $max_size) {
        return ['success' => false, 'message' => 'File too large. Maximum 5MB allowed.'];
    }
    
    $extension = pathinfo($file['name'], PATHINFO_EXTENSION);
    $filename = $candidate_id . '_photo.' . $extension;
    $upload_path = 'uploads/candidates/photos/' . $filename;
    
    if (move_uploaded_file($file['tmp_name'], $upload_path)) {
        return ['success' => true, 'path' => $upload_path];
    } else {
        return ['success' => false, 'message' => 'Failed to move uploaded file.'];
    }
}

function uploadCandidateSymbol($file, $candidate_id) {
    $allowed_types = ['image/jpeg', 'image/png', 'image/gif', 'image/svg+xml'];
    $max_size = 2 * 1024 * 1024; // 2MB
    
    if (!in_array($file['type'], $allowed_types)) {
        return ['success' => false, 'message' => 'Invalid file type. Only JPG, PNG, GIF, SVG allowed.'];
    }
    
    if ($file['size'] > $max_size) {
        return ['success' => false, 'message' => 'File too large. Maximum 2MB allowed.'];
    }
    
    $extension = pathinfo($file['name'], PATHINFO_EXTENSION);
    $filename = $candidate_id . '_symbol.' . $extension;
    $upload_path = 'uploads/candidates/symbols/' . $filename;
    
    if (move_uploaded_file($file['tmp_name'], $upload_path)) {
        return ['success' => true, 'path' => $upload_path];
    } else {
        return ['success' => false, 'message' => 'Failed to move uploaded file.'];
    }
}

function updateCandidate($data, $files) {
    try {
        $db = getDB();

        $candidate_id = intval($data['candidate_id']);

        // Handle photo upload
        $photo_path = null;
        if (isset($files['photo']) && $files['photo']['error'] === UPLOAD_ERR_OK) {
            $photo_result = uploadCandidatePhoto($files['photo'], $data['existing_candidate_id']);
            if ($photo_result['success']) {
                $photo_path = $photo_result['path'];
            }
        }

        // Handle symbol upload
        $symbol_path = null;
        if (isset($files['symbol']) && $files['symbol']['error'] === UPLOAD_ERR_OK) {
            $symbol_result = uploadCandidateSymbol($files['symbol'], $data['existing_candidate_id']);
            if ($symbol_result['success']) {
                $symbol_path = $symbol_result['path'];
            }
        }

        // Build update query
        $update_fields = [
            'name = ?',
            'name_bn = ?',
            'symbol_name = ?',
            'symbol_name_bn = ?',
            'election_level = ?',
            'area_id = ?',
            'position_id = ?',
            'position = ?',
            'position_bn = ?',
            'party_name = ?',
            'party_name_bn = ?',
            'description = ?',
            'description_bn = ?',
            'contact_mobile = ?',
            'contact_email = ?'
        ];

        $params = [
            sanitizeInput($data['name']),
            sanitizeInput($data['name_bn']) ?: null,
            sanitizeInput($data['symbol_name']) ?: null,
            sanitizeInput($data['symbol_name_bn']) ?: null,
            sanitizeInput($data['election_level']),
            intval($data['area_id']) ?: null,
            intval($data['position_id']) ?: null,
            sanitizeInput($data['position']) ?: null,
            sanitizeInput($data['position_bn']) ?: null,
            sanitizeInput($data['party_name']) ?: null,
            sanitizeInput($data['party_name_bn']) ?: null,
            sanitizeInput($data['description']) ?: null,
            sanitizeInput($data['description_bn']) ?: null,
            sanitizeInput($data['contact_mobile']) ?: null,
            sanitizeInput($data['contact_email']) ?: null
        ];

        // Add photo update if uploaded
        if ($photo_path) {
            $update_fields[] = 'photo = ?';
            $params[] = $photo_path;
        }

        // Add symbol update if uploaded
        if ($symbol_path) {
            $update_fields[] = 'symbol = ?';
            $params[] = $symbol_path;
        }

        $params[] = $candidate_id;

        $sql = "UPDATE candidates SET " . implode(', ', $update_fields) . " WHERE id = ?";
        $stmt = $db->prepare($sql);
        $stmt->execute($params);

        return ['success' => true, 'message' => 'প্রার্থী সফলভাবে আপডেট করা হয়েছে'];

    } catch (Exception $e) {
        return ['success' => false, 'message' => 'Error: ' . $e->getMessage()];
    }
}

function deleteCandidate($candidate_id) {
    try {
        $db = getDB();

        $stmt = $db->prepare("UPDATE candidates SET is_active = 0 WHERE candidate_id = ?");
        $stmt->execute([$candidate_id]);

        return ['success' => true, 'message' => 'প্রার্থী সফলভাবে মুছে ফেলা হয়েছে'];

    } catch (Exception $e) {
        return ['success' => false, 'message' => 'Error: ' . $e->getMessage()];
    }
}

// Helper function to check if user is logged in
if (!function_exists('getCurrentUser')) {
    function getCurrentUser() {
        return $_SESSION['user'] ?? ['id' => 1, 'username' => 'admin'];
    }
}

// Helper function to generate CSRF token
if (!function_exists('generateCSRFToken')) {
    function generateCSRFToken() {
        if (!isset($_SESSION['csrf_token'])) {
            $_SESSION['csrf_token'] = bin2hex(random_bytes(32));
        }
        return $_SESSION['csrf_token'];
    }
}

// Helper function to validate CSRF token
if (!function_exists('validateCSRFToken')) {
    function validateCSRFToken($token) {
        return isset($_SESSION['csrf_token']) && hash_equals($_SESSION['csrf_token'], $token);
    }
}
?>

<!DOCTYPE html>
<html lang="bn">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>প্রার্থী ব্যবস্থাপনা - OVS</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body>

<?php include '../includes/admin-nav.php'; ?>

<div class="container-fluid mt-4">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h2><i class="fas fa-user-tie"></i> প্রার্থী ব্যবস্থাপনা</h2>
                <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addCandidateModal">
                    <i class="fas fa-plus"></i> নতুন প্রার্থী
                </button>
            </div>

            <!-- Flash Messages -->
            <?php if ($success): ?>
                <div class="alert alert-success alert-dismissible fade show">
                    <?php echo $success; ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            <?php endif; ?>

            <?php if ($error || $flash_error): ?>
                <div class="alert alert-danger alert-dismissible fade show">
                    <?php echo $error ?: $flash_error; ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            <?php endif; ?>

            <!-- Filters -->
            <div class="card mb-4">
                <div class="card-body">
                    <form method="GET" class="row g-3">
                        <div class="col-md-3">
                            <input type="text" class="form-control" name="search" placeholder="নাম বা আইডি দিয়ে খুঁজুন" value="<?php echo htmlspecialchars($search); ?>">
                        </div>
                        <div class="col-md-3">
                            <select class="form-select" name="level">
                                <option value="">সব ধরনের নির্বাচন</option>
                                <?php foreach ($election_levels as $level => $label): ?>
                                    <option value="<?php echo $level; ?>" <?php echo $level_filter === $level ? 'selected' : ''; ?>>
                                        <?php echo $label; ?>
                                    </option>
                                <?php endforeach; ?>
                            </select>
                        </div>
                        <div class="col-md-3">
                            <select class="form-select" name="area">
                                <option value="">সব এলাকা</option>
                                <?php foreach ($areas as $area): ?>
                                    <option value="<?php echo $area['id']; ?>" <?php echo $area_filter == $area['id'] ? 'selected' : ''; ?>>
                                        <?php echo $area['name_bn'] ?: $area['name']; ?> (<?php echo ucfirst($area['type']); ?>)
                                    </option>
                                <?php endforeach; ?>
                            </select>
                        </div>
                        <div class="col-md-3">
                            <button type="submit" class="btn btn-outline-primary">
                                <i class="fas fa-search"></i> খুঁজুন
                            </button>
                            <a href="<?php echo $_SERVER['PHP_SELF']; ?>" class="btn btn-outline-secondary">
                                <i class="fas fa-refresh"></i> রিসেট
                            </a>
                        </div>
                    </form>
                </div>
            </div>

            <!-- Candidates List -->
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-list"></i> প্রার্থী তালিকা 
                        <span class="badge bg-primary"><?php echo $total_candidates; ?></span>
                    </h5>
                </div>
                <div class="card-body">
                    <?php if (empty($candidates)): ?>
                        <div class="text-center py-5">
                            <i class="fas fa-user-tie fa-3x text-muted mb-3"></i>
                            <h5 class="text-muted">কোন প্রার্থী পাওয়া যায়নি</h5>
                            <p class="text-muted">নতুন প্রার্থী যোগ করতে উপরের বাটনে ক্লিক করুন</p>
                        </div>
                    <?php else: ?>
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead>
                                    <tr>
                                        <th>ছবি</th>
                                        <th>প্রার্থী তথ্য</th>
                                        <th>নির্বাচনী এলাকা</th>
                                        <th>প্রতীক</th>
                                        <th>যোগাযোগ</th>
                                        <th>অ্যাকশন</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($candidates as $candidate): ?>
                                        <tr>
                                            <td>
                                                <?php if ($candidate['photo']): ?>
                                                    <img src="../<?php echo $candidate['photo']; ?>" 
                                                         alt="<?php echo htmlspecialchars($candidate['name']); ?>" 
                                                         class="rounded-circle" width="50" height="50" style="object-fit: cover;">
                                                <?php else: ?>
                                                    <div class="bg-secondary rounded-circle d-flex align-items-center justify-content-center" 
                                                         style="width: 50px; height: 50px;">
                                                        <i class="fas fa-user text-white"></i>
                                                    </div>
                                                <?php endif; ?>
                                            </td>
                                            <td>
                                                <div>
                                                    <strong><?php echo htmlspecialchars($candidate['name']); ?></strong>
                                                    <?php if ($candidate['name_bn']): ?>
                                                        <br><small class="text-muted"><?php echo htmlspecialchars($candidate['name_bn']); ?></small>
                                                    <?php endif; ?>
                                                </div>
                                                <small class="text-primary"><?php echo $candidate['candidate_id']; ?></small>
                                                <?php if ($candidate['party_name']): ?>
                                                    <br><small class="badge bg-info"><?php echo htmlspecialchars($candidate['party_name']); ?></small>
                                                <?php endif; ?>
                                            </td>
                                            <td>
                                                <span class="badge bg-<?php echo getElectionLevelColor($candidate['election_level']); ?>">
                                                    <?php echo $election_levels[$candidate['election_level']] ?? $candidate['election_level']; ?>
                                                </span>
                                                <?php if ($candidate['position_title']): ?>
                                                    <br><strong class="text-success"><?php echo htmlspecialchars($candidate['position_title_bn'] ?: $candidate['position_title']); ?></strong>
                                                <?php endif; ?>
                                                <?php if ($candidate['area_name']): ?>
                                                    <br><small><?php echo htmlspecialchars($candidate['area_name_bn'] ?: $candidate['area_name']); ?></small>
                                                <?php endif; ?>
                                            </td>
                                            <td>
                                                <?php if ($candidate['symbol']): ?>
                                                    <img src="../<?php echo $candidate['symbol']; ?>" 
                                                         alt="Symbol" width="40" height="40" style="object-fit: contain;">
                                                <?php endif; ?>
                                                <?php if ($candidate['symbol_name']): ?>
                                                    <br><small><?php echo htmlspecialchars($candidate['symbol_name_bn'] ?: $candidate['symbol_name']); ?></small>
                                                <?php endif; ?>
                                            </td>
                                            <td>
                                                <?php if ($candidate['contact_mobile']): ?>
                                                    <div><i class="fas fa-phone"></i> <?php echo $candidate['contact_mobile']; ?></div>
                                                <?php endif; ?>
                                                <?php if ($candidate['contact_email']): ?>
                                                    <div><i class="fas fa-envelope"></i> <?php echo $candidate['contact_email']; ?></div>
                                                <?php endif; ?>
                                            </td>
                                            <td>
                                                <div class="btn-group" role="group">
                                                    <button type="button" class="btn btn-sm btn-outline-primary" 
                                                            onclick="viewCandidate('<?php echo $candidate['candidate_id']; ?>')">
                                                        <i class="fas fa-eye"></i>
                                                    </button>
                                                    <button type="button" class="btn btn-sm btn-outline-warning" 
                                                            onclick="editCandidate('<?php echo $candidate['candidate_id']; ?>')">
                                                        <i class="fas fa-edit"></i>
                                                    </button>
                                                    <button type="button" class="btn btn-sm btn-outline-danger" 
                                                            onclick="deleteCandidate('<?php echo $candidate['candidate_id']; ?>', '<?php echo htmlspecialchars($candidate['name']); ?>')">
                                                        <i class="fas fa-trash"></i>
                                                    </button>
                                                </div>
                                            </td>
                                        </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>

                        <!-- Pagination -->
                        <?php if ($total_pages > 1): ?>
                            <nav aria-label="Page navigation">
                                <ul class="pagination justify-content-center">
                                    <?php for ($i = 1; $i <= $total_pages; $i++): ?>
                                        <li class="page-item <?php echo $i === $page ? 'active' : ''; ?>">
                                            <a class="page-link" href="?page=<?php echo $i; ?>&search=<?php echo urlencode($search); ?>&level=<?php echo urlencode($level_filter); ?>&area=<?php echo urlencode($area_filter); ?>">
                                                <?php echo $i; ?>
                                            </a>
                                        </li>
                                    <?php endfor; ?>
                                </ul>
                            </nav>
                        <?php endif; ?>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
</div>

<?php
function getElectionLevelColor($level) {
    $colors = [
        'ward' => 'primary',
        'village' => 'success', 
        'upazila' => 'info',
        'district' => 'warning',
        'national' => 'danger'
    ];
    return $colors[$level] ?? 'secondary';
}
?>

<!-- Add Candidate Modal -->
<div class="modal fade" id="addCandidateModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-user-plus"></i> নতুন প্রার্থী যোগ করুন
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form method="POST" action="?action=add" enctype="multipart/form-data">
                <div class="modal-body">
                    <input type="hidden" name="csrf_token" value="<?php echo generateCSRFToken(); ?>">

                    <!-- Basic Information -->
                    <div class="row mb-3">
                        <div class="col-12">
                            <h6 class="text-primary"><i class="fas fa-user"></i> মৌলিক তথ্য</h6>
                            <hr>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-floating mb-3">
                                <input type="text" class="form-control" name="name" required>
                                <label>নাম (ইংরেজি) *</label>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-floating mb-3">
                                <input type="text" class="form-control" name="name_bn">
                                <label>নাম (বাংলা)</label>
                            </div>
                        </div>
                    </div>

                    <!-- Photo Upload -->
                    <div class="row mb-3">
                        <div class="col-md-6">
                            <label class="form-label">প্রার্থীর ছবি</label>
                            <input type="file" class="form-control" name="photo" accept="image/*" onchange="previewPhoto(this)">
                            <small class="text-muted">JPG, PNG, GIF - সর্বোচ্চ 5MB</small>
                            <div id="photo-preview" class="mt-2"></div>
                        </div>
                        <div class="col-md-6">
                            <label class="form-label">নির্বাচনী প্রতীক</label>
                            <input type="file" class="form-control" name="symbol" accept="image/*" onchange="previewSymbol(this)">
                            <small class="text-muted">JPG, PNG, GIF, SVG - সর্বোচ্চ 2MB</small>
                            <div id="symbol-preview" class="mt-2"></div>
                        </div>
                    </div>

                    <!-- Symbol Information -->
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-floating mb-3">
                                <input type="text" class="form-control" name="symbol_name">
                                <label>প্রতীকের নাম (ইংরেজি)</label>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-floating mb-3">
                                <input type="text" class="form-control" name="symbol_name_bn">
                                <label>প্রতীকের নাম (বাংলা)</label>
                            </div>
                        </div>
                    </div>

                    <!-- Election Information -->
                    <div class="row mb-3">
                        <div class="col-12">
                            <h6 class="text-primary"><i class="fas fa-vote-yea"></i> নির্বাচনী তথ্য</h6>
                            <hr>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-4">
                            <div class="form-floating mb-3">
                                <select class="form-select" name="election_level" required onchange="updatePositionAndAreaOptions(this.value)">
                                    <option value="">নির্বাচনের ধরন নির্বাচন করুন</option>
                                    <?php foreach ($election_levels as $level => $label): ?>
                                        <option value="<?php echo $level; ?>"><?php echo $label; ?></option>
                                    <?php endforeach; ?>
                                </select>
                                <label>নির্বাচনের ধরন *</label>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="form-floating mb-3">
                                <select class="form-select" name="position_id" id="position_select" required>
                                    <option value="">পদ নির্বাচন করুন</option>
                                    <?php foreach ($positions as $position): ?>
                                        <option value="<?php echo $position['id']; ?>"
                                                data-level="<?php echo $position['election_level']; ?>"
                                                data-area-type="<?php echo $position['area_type']; ?>">
                                            <?php echo $position['title_bn'] ?: $position['title']; ?>
                                        </option>
                                    <?php endforeach; ?>
                                </select>
                                <label>প্রতিদ্বন্দ্বিতার পদ *</label>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="form-floating mb-3">
                                <select class="form-select" name="area_id" id="area_select">
                                    <option value="">এলাকা নির্বাচন করুন</option>
                                    <?php foreach ($areas as $area): ?>
                                        <option value="<?php echo $area['id']; ?>" data-type="<?php echo $area['type']; ?>">
                                            <?php echo $area['name_bn'] ?: $area['name']; ?> (<?php echo ucfirst($area['type']); ?>)
                                        </option>
                                    <?php endforeach; ?>
                                </select>
                                <label>নির্বাচনী এলাকা</label>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-floating mb-3">
                                <input type="text" class="form-control" name="position">
                                <label>পদবী (ইংরেজি)</label>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-floating mb-3">
                                <input type="text" class="form-control" name="position_bn">
                                <label>পদবী (বাংলা)</label>
                            </div>
                        </div>
                    </div>

                    <!-- Party Information -->
                    <div class="row mb-3">
                        <div class="col-12">
                            <h6 class="text-primary"><i class="fas fa-flag"></i> দলীয় তথ্য</h6>
                            <hr>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-floating mb-3">
                                <input type="text" class="form-control" name="party_name">
                                <label>দলের নাম (ইংরেজি)</label>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-floating mb-3">
                                <input type="text" class="form-control" name="party_name_bn">
                                <label>দলের নাম (বাংলা)</label>
                            </div>
                        </div>
                    </div>

                    <!-- Contact Information -->
                    <div class="row mb-3">
                        <div class="col-12">
                            <h6 class="text-primary"><i class="fas fa-phone"></i> যোগাযোগের তথ্য</h6>
                            <hr>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-floating mb-3">
                                <input type="tel" class="form-control" name="contact_mobile">
                                <label>মোবাইল নম্বর</label>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-floating mb-3">
                                <input type="email" class="form-control" name="contact_email">
                                <label>ইমেইল ঠিকানা</label>
                            </div>
                        </div>
                    </div>

                    <!-- Description -->
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-floating mb-3">
                                <textarea class="form-control" name="description" style="height: 100px"></textarea>
                                <label>বিবরণ (ইংরেজি)</label>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-floating mb-3">
                                <textarea class="form-control" name="description_bn" style="height: 100px"></textarea>
                                <label>বিবরণ (বাংলা)</label>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                        <i class="fas fa-times"></i> বাতিল
                    </button>
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-save"></i> সংরক্ষণ করুন
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
// Preview uploaded photo
function previewPhoto(input) {
    const preview = document.getElementById('photo-preview');
    preview.innerHTML = '';

    if (input.files && input.files[0]) {
        const reader = new FileReader();
        reader.onload = function(e) {
            preview.innerHTML = `<img src="${e.target.result}" class="img-thumbnail" style="max-width: 150px; max-height: 150px;">`;
        };
        reader.readAsDataURL(input.files[0]);
    }
}

// Preview uploaded symbol
function previewSymbol(input) {
    const preview = document.getElementById('symbol-preview');
    preview.innerHTML = '';

    if (input.files && input.files[0]) {
        const reader = new FileReader();
        reader.onload = function(e) {
            preview.innerHTML = `<img src="${e.target.result}" class="img-thumbnail" style="max-width: 100px; max-height: 100px;">`;
        };
        reader.readAsDataURL(input.files[0]);
    }
}

// Update position and area options based on election level
function updatePositionAndAreaOptions(level) {
    updatePositionOptions(level);
    updateAreaOptions(level);
}

// Update position options based on election level
function updatePositionOptions(level) {
    const positionSelect = document.getElementById('position_select');
    const options = positionSelect.querySelectorAll('option[data-level]');

    // Show all options first
    options.forEach(option => {
        option.style.display = 'block';
    });

    // Filter based on election level
    if (level) {
        options.forEach(option => {
            if (option.dataset.level !== level) {
                option.style.display = 'none';
            }
        });
    }

    // Reset selection
    positionSelect.value = '';

    // Update area options when position changes
    positionSelect.addEventListener('change', function() {
        const selectedOption = this.options[this.selectedIndex];
        if (selectedOption && selectedOption.dataset.areaType) {
            updateAreaOptionsByType(selectedOption.dataset.areaType);
        }
    });
}

// Update area options based on election level
function updateAreaOptions(level) {
    const areaSelect = document.getElementById('area_select');
    const options = areaSelect.querySelectorAll('option[data-type]');

    // Show all options first
    options.forEach(option => {
        option.style.display = 'block';
    });

    // Filter based on election level
    if (level) {
        const levelTypeMap = {
            'ward': 'ward',
            'village': ['village', 'union', 'municipality'],
            'upazila': 'upazila',
            'district': 'district',
            'national': '' // Show all for national
        };

        const targetTypes = levelTypeMap[level];

        if (targetTypes) {
            options.forEach(option => {
                const optionType = option.dataset.type;
                let shouldShow = false;

                if (Array.isArray(targetTypes)) {
                    shouldShow = targetTypes.includes(optionType);
                } else if (targetTypes === '') {
                    shouldShow = true; // Show all for national
                } else {
                    shouldShow = optionType === targetTypes;
                }

                option.style.display = shouldShow ? 'block' : 'none';
            });
        }
    }

    // Reset selection
    areaSelect.value = '';
}

// Update area options based on specific area type
function updateAreaOptionsByType(areaType) {
    const areaSelect = document.getElementById('area_select');
    const options = areaSelect.querySelectorAll('option[data-type]');

    if (!areaType || areaType === 'national') {
        // Show all options
        options.forEach(option => {
            option.style.display = 'block';
        });
    } else {
        // Filter by specific area type
        options.forEach(option => {
            option.style.display = option.dataset.type === areaType ? 'block' : 'none';
        });
    }

    // Reset selection
    areaSelect.value = '';
}

// Candidate management functions
function viewCandidate(candidateId) {
    // Implementation for viewing candidate details
    alert('View candidate: ' + candidateId);
}

function editCandidate(candidateId) {
    // Implementation for editing candidate
    alert('Edit candidate: ' + candidateId);
}

function deleteCandidate(candidateId, candidateName) {
    if (confirm('আপনি কি নিশ্চিত যে "' + candidateName + '" প্রার্থীকে মুছে ফেলতে চান?')) {
        const form = document.createElement('form');
        form.method = 'POST';
        form.action = '?action=delete';

        const csrfInput = document.createElement('input');
        csrfInput.type = 'hidden';
        csrfInput.name = 'csrf_token';
        csrfInput.value = '<?php echo generateCSRFToken(); ?>';

        const candidateInput = document.createElement('input');
        candidateInput.type = 'hidden';
        candidateInput.name = 'candidate_id';
        candidateInput.value = candidateId;

        form.appendChild(csrfInput);
        form.appendChild(candidateInput);
        document.body.appendChild(form);
        form.submit();
    }
}
</script>

<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
