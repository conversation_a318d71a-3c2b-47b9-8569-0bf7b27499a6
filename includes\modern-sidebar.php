<?php
/**
 * Modern Sidebar Component
 * মডার্ন সাইডবার কম্পোনেন্ট
 */

// Get current page for active menu highlighting
$current_page = basename($_SERVER['PHP_SELF']);
$current_dir = basename(dirname($_SERVER['PHP_SELF']));

// Define menu items based on user role
$menu_items = [];

if (isLoggedIn()) {
    // Main Navigation
    $menu_items = [
        [
            'type' => 'divider',
            'title' => 'মূল নেভিগেশন'
        ],
        [
            'title' => 'ড্যাশবোর্ড',
            'title_en' => 'Dashboard',
            'icon' => 'fas fa-tachometer-alt',
            'url' => APP_URL . '/dashboard.php',
            'active' => $current_page === 'dashboard.php',
            'badge' => 'নতুন',
            'badge_color' => 'success'
        ],
        [
            'title' => 'পোল ব্রাউজ করুন',
            'title_en' => 'Browse Polls',
            'icon' => 'fas fa-list',
            'url' => APP_URL . '/polls/',
            'active' => $current_dir === 'polls' && $current_page === 'index.php',
            'submenu' => [
                [
                    'title' => 'সব পোল',
                    'icon' => 'fas fa-list-ul',
                    'url' => APP_URL . '/polls/all.php'
                ],
                [
                    'title' => 'জনপ্রিয় পোল',
                    'icon' => 'fas fa-fire',
                    'url' => APP_URL . '/polls/popular.php'
                ],
                [
                    'title' => 'সাম্প্রতিক পোল',
                    'icon' => 'fas fa-clock',
                    'url' => APP_URL . '/polls/recent.php'
                ]
            ]
        ],
        [
            'title' => 'নতুন পোল তৈরি',
            'title_en' => 'Create Poll',
            'icon' => 'fas fa-plus-circle',
            'url' => APP_URL . '/polls/create.php',
            'active' => $current_page === 'create.php',
            'submenu' => [
                [
                    'title' => 'সাধারণ পোল',
                    'icon' => 'fas fa-poll',
                    'url' => APP_URL . '/polls/create.php?type=simple'
                ],
                [
                    'title' => 'মাল্টিপল চয়েস',
                    'icon' => 'fas fa-check-square',
                    'url' => APP_URL . '/polls/create.php?type=multiple'
                ],
                [
                    'title' => 'রেটিং পোল',
                    'icon' => 'fas fa-star',
                    'url' => APP_URL . '/polls/create.php?type=rating'
                ],
                [
                    'title' => 'ইমেজ পোল',
                    'icon' => 'fas fa-image',
                    'url' => APP_URL . '/polls/create.php?type=image'
                ]
            ]
        ],
        [
            'title' => 'আমার পোল',
            'title_en' => 'My Polls',
            'icon' => 'fas fa-poll',
            'url' => APP_URL . '/polls/manage.php',
            'active' => $current_page === 'manage.php',
            'submenu' => [
                [
                    'title' => 'সক্রিয় পোল',
                    'icon' => 'fas fa-play-circle',
                    'url' => APP_URL . '/polls/manage.php?status=active'
                ],
                [
                    'title' => 'খসড়া পোল',
                    'icon' => 'fas fa-edit',
                    'url' => APP_URL . '/polls/manage.php?status=draft'
                ],
                [
                    'title' => 'সমাপ্ত পোল',
                    'icon' => 'fas fa-check-circle',
                    'url' => APP_URL . '/polls/manage.php?status=ended'
                ],
                [
                    'title' => 'পরিসংখ্যান',
                    'icon' => 'fas fa-chart-bar',
                    'url' => APP_URL . '/polls/statistics.php'
                ]
            ]
        ],
        [
            'title' => 'ভোট ইতিহাস',
            'title_en' => 'Vote History',
            'icon' => 'fas fa-history',
            'url' => APP_URL . '/votes/history.php',
            'active' => $current_page === 'history.php'
        ],
        [
            'title' => 'প্রোফাইল',
            'title_en' => 'Profile',
            'icon' => 'fas fa-user',
            'url' => APP_URL . '/profile.php',
            'active' => $current_page === 'profile.php',
            'submenu' => [
                [
                    'title' => 'ব্যক্তিগত তথ্য',
                    'icon' => 'fas fa-id-card',
                    'url' => APP_URL . '/profile/personal.php'
                ],
                [
                    'title' => 'নিরাপত্তা',
                    'icon' => 'fas fa-shield-alt',
                    'url' => APP_URL . '/profile/security.php'
                ],
                [
                    'title' => 'পছন্দসমূহ',
                    'icon' => 'fas fa-cog',
                    'url' => APP_URL . '/profile/preferences.php'
                ]
            ]
        ]
    ];

    // Admin menu items
    if (isAdmin()) {
        $admin_items = [
            [
                'type' => 'divider',
                'title' => 'অ্যাডমিন প্যানেল'
            ],
            [
                'title' => 'অ্যাডমিন ড্যাশবোর্ড',
                'title_en' => 'Admin Dashboard',
                'icon' => 'fas fa-crown',
                'url' => APP_URL . '/admin/',
                'active' => $current_dir === 'admin' && $current_page === 'index.php',
                'badge' => 'অ্যাডমিন',
                'badge_color' => 'warning'
            ],
            [
                'title' => 'ভোটার ব্যবস্থাপনা',
                'title_en' => 'Voter Management',
                'icon' => 'fas fa-users',
                'url' => APP_URL . '/admin/voters.php',
                'active' => $current_page === 'voters.php',
                'submenu' => [
                    [
                        'title' => 'সব ভোটার',
                        'icon' => 'fas fa-list',
                        'url' => APP_URL . '/admin/voters.php'
                    ],
                    [
                        'title' => 'নতুন ভোটার যোগ',
                        'icon' => 'fas fa-user-plus',
                        'url' => APP_URL . '/admin/voters/add.php'
                    ],
                    [
                        'title' => 'ভোটার আমদানি',
                        'icon' => 'fas fa-file-import',
                        'url' => APP_URL . '/admin/voters/import.php'
                    ],
                    [
                        'title' => 'ভোটার রিপোর্ট',
                        'icon' => 'fas fa-chart-pie',
                        'url' => APP_URL . '/admin/voters/report.php'
                    ]
                ]
            ],
            [
                'title' => 'প্রার্থী ব্যবস্থাপনা',
                'title_en' => 'Candidate Management',
                'icon' => 'fas fa-user-tie',
                'url' => APP_URL . '/admin/candidates.php',
                'active' => $current_page === 'candidates.php',
                'submenu' => [
                    [
                        'title' => 'সব প্রার্থী',
                        'icon' => 'fas fa-list',
                        'url' => APP_URL . '/admin/candidates.php'
                    ],
                    [
                        'title' => 'নতুন প্রার্থী',
                        'icon' => 'fas fa-plus',
                        'url' => APP_URL . '/admin/candidates/add.php'
                    ],
                    [
                        'title' => 'প্রার্থী প্রোফাইল',
                        'icon' => 'fas fa-id-badge',
                        'url' => APP_URL . '/admin/candidates/profiles.php'
                    ],
                    [
                        'title' => 'নির্বাচনী এলাকা',
                        'icon' => 'fas fa-map',
                        'url' => APP_URL . '/admin/candidates/constituencies.php'
                    ]
                ]
            ],
            [
                'title' => 'এলাকা ব্যবস্থাপনা',
                'title_en' => 'Area Management',
                'icon' => 'fas fa-map-marked-alt',
                'url' => APP_URL . '/admin/areas.php',
                'active' => $current_page === 'areas.php',
                'submenu' => [
                    [
                        'title' => 'বিভাগসমূহ',
                        'icon' => 'fas fa-map',
                        'url' => APP_URL . '/admin/areas/divisions.php'
                    ],
                    [
                        'title' => 'জেলাসমূহ',
                        'icon' => 'fas fa-map-marker',
                        'url' => APP_URL . '/admin/areas/districts.php'
                    ],
                    [
                        'title' => 'উপজেলাসমূহ',
                        'icon' => 'fas fa-location-arrow',
                        'url' => APP_URL . '/admin/areas/upazilas.php'
                    ],
                    [
                        'title' => 'ভোটিং কেন্দ্র',
                        'icon' => 'fas fa-building',
                        'url' => APP_URL . '/admin/areas/centers.php'
                    ]
                ]
            ],
            [
                'title' => 'পোল ব্যবস্থাপনা',
                'title_en' => 'Poll Management',
                'icon' => 'fas fa-chart-bar',
                'url' => APP_URL . '/admin/polls.php',
                'active' => $current_page === 'polls.php',
                'submenu' => [
                    [
                        'title' => 'সব পোল',
                        'icon' => 'fas fa-list',
                        'url' => APP_URL . '/admin/polls.php'
                    ],
                    [
                        'title' => 'পোল অনুমোদন',
                        'icon' => 'fas fa-check-circle',
                        'url' => APP_URL . '/admin/polls/approve.php'
                    ],
                    [
                        'title' => 'পোল মনিটরিং',
                        'icon' => 'fas fa-eye',
                        'url' => APP_URL . '/admin/polls/monitor.php'
                    ],
                    [
                        'title' => 'ফলাফল যাচাই',
                        'icon' => 'fas fa-clipboard-check',
                        'url' => APP_URL . '/admin/polls/verify.php'
                    ]
                ]
            ],
            [
                'title' => 'ব্যবহারকারী ব্যবস্থাপনা',
                'title_en' => 'User Management',
                'icon' => 'fas fa-users-cog',
                'url' => APP_URL . '/admin/users.php',
                'active' => $current_page === 'users.php',
                'submenu' => [
                    [
                        'title' => 'সব ব্যবহারকারী',
                        'icon' => 'fas fa-users',
                        'url' => APP_URL . '/admin/users.php'
                    ],
                    [
                        'title' => 'অ্যাডমিন তালিকা',
                        'icon' => 'fas fa-user-shield',
                        'url' => APP_URL . '/admin/users/admins.php'
                    ],
                    [
                        'title' => 'ভূমিকা ব্যবস্থাপনা',
                        'icon' => 'fas fa-key',
                        'url' => APP_URL . '/admin/users/roles.php'
                    ],
                    [
                        'title' => 'অনুমতি সেটিংস',
                        'icon' => 'fas fa-lock',
                        'url' => APP_URL . '/admin/users/permissions.php'
                    ]
                ]
            ],
            [
                'title' => 'রিপোর্ট ও অ্যানালিটিক্স',
                'title_en' => 'Reports & Analytics',
                'icon' => 'fas fa-chart-line',
                'url' => APP_URL . '/admin/analytics.php',
                'active' => $current_page === 'analytics.php',
                'submenu' => [
                    [
                        'title' => 'ভোট পরিসংখ্যান',
                        'icon' => 'fas fa-chart-pie',
                        'url' => APP_URL . '/admin/analytics/votes.php'
                    ],
                    [
                        'title' => 'ব্যবহারকারী রিপোর্ট',
                        'icon' => 'fas fa-users',
                        'url' => APP_URL . '/admin/analytics/users.php'
                    ],
                    [
                        'title' => 'সিস্টেম পারফরমেন্স',
                        'icon' => 'fas fa-tachometer-alt',
                        'url' => APP_URL . '/admin/analytics/performance.php'
                    ],
                    [
                        'title' => 'কাস্টম রিপোর্ট',
                        'icon' => 'fas fa-file-alt',
                        'url' => APP_URL . '/admin/analytics/custom.php'
                    ]
                ]
            ]
        ];
        $menu_items = array_merge($menu_items, $admin_items);
    }

    // Communication & Notifications
    $communication_items = [
        [
            'type' => 'divider',
            'title' => 'যোগাযোগ ও নোটিফিকেশন'
        ],
        [
            'title' => 'নোটিফিকেশন',
            'title_en' => 'Notifications',
            'icon' => 'fas fa-bell',
            'url' => APP_URL . '/notifications.php',
            'active' => $current_page === 'notifications.php',
            'badge' => '5',
            'badge_color' => 'danger',
            'submenu' => [
                [
                    'title' => 'সব নোটিফিকেশন',
                    'icon' => 'fas fa-list',
                    'url' => APP_URL . '/notifications.php'
                ],
                [
                    'title' => 'পোল নোটিফিকেশন',
                    'icon' => 'fas fa-poll',
                    'url' => APP_URL . '/notifications/polls.php'
                ],
                [
                    'title' => 'সিস্টেম নোটিফিকেশন',
                    'icon' => 'fas fa-cog',
                    'url' => APP_URL . '/notifications/system.php'
                ]
            ]
        ],
        [
            'title' => 'মেসেজ',
            'title_en' => 'Messages',
            'icon' => 'fas fa-envelope',
            'url' => APP_URL . '/messages.php',
            'active' => $current_page === 'messages.php',
            'badge' => '3',
            'badge_color' => 'info',
            'submenu' => [
                [
                    'title' => 'ইনবক্স',
                    'icon' => 'fas fa-inbox',
                    'url' => APP_URL . '/messages/inbox.php'
                ],
                [
                    'title' => 'পাঠানো',
                    'icon' => 'fas fa-paper-plane',
                    'url' => APP_URL . '/messages/sent.php'
                ],
                [
                    'title' => 'নতুন মেসেজ',
                    'icon' => 'fas fa-edit',
                    'url' => APP_URL . '/messages/compose.php'
                ]
            ]
        ],
        [
            'title' => 'ঘোষণা',
            'title_en' => 'Announcements',
            'icon' => 'fas fa-bullhorn',
            'url' => APP_URL . '/announcements.php',
            'active' => $current_page === 'announcements.php'
        ]
    ];
    $menu_items = array_merge($menu_items, $communication_items);

    // Tools & Utilities
    $tools_items = [
        [
            'type' => 'divider',
            'title' => 'টুলস ও ইউটিলিটি'
        ],
        [
            'title' => 'ফাইল ম্যানেজার',
            'title_en' => 'File Manager',
            'icon' => 'fas fa-folder',
            'url' => APP_URL . '/files.php',
            'active' => $current_page === 'files.php',
            'submenu' => [
                [
                    'title' => 'আমার ফাইল',
                    'icon' => 'fas fa-file',
                    'url' => APP_URL . '/files/my-files.php'
                ],
                [
                    'title' => 'আপলোড',
                    'icon' => 'fas fa-upload',
                    'url' => APP_URL . '/files/upload.php'
                ],
                [
                    'title' => 'শেয়ার করা ফাইল',
                    'icon' => 'fas fa-share',
                    'url' => APP_URL . '/files/shared.php'
                ]
            ]
        ],
        [
            'title' => 'ক্যালেন্ডার',
            'title_en' => 'Calendar',
            'icon' => 'fas fa-calendar',
            'url' => APP_URL . '/calendar.php',
            'active' => $current_page === 'calendar.php',
            'submenu' => [
                [
                    'title' => 'আজকের ইভেন্ট',
                    'icon' => 'fas fa-calendar-day',
                    'url' => APP_URL . '/calendar/today.php'
                ],
                [
                    'title' => 'আসন্ন ইভেন্ট',
                    'icon' => 'fas fa-calendar-week',
                    'url' => APP_URL . '/calendar/upcoming.php'
                ],
                [
                    'title' => 'নতুন ইভেন্ট',
                    'icon' => 'fas fa-plus',
                    'url' => APP_URL . '/calendar/create.php'
                ]
            ]
        ],
        [
            'title' => 'টাস্ক ম্যানেজার',
            'title_en' => 'Task Manager',
            'icon' => 'fas fa-tasks',
            'url' => APP_URL . '/tasks.php',
            'active' => $current_page === 'tasks.php',
            'submenu' => [
                [
                    'title' => 'আমার টাস্ক',
                    'icon' => 'fas fa-list-check',
                    'url' => APP_URL . '/tasks/my-tasks.php'
                ],
                [
                    'title' => 'টিম টাস্ক',
                    'icon' => 'fas fa-users',
                    'url' => APP_URL . '/tasks/team-tasks.php'
                ],
                [
                    'title' => 'সম্পন্ন টাস্ক',
                    'icon' => 'fas fa-check-circle',
                    'url' => APP_URL . '/tasks/completed.php'
                ]
            ]
        ],
        [
            'title' => 'নোটস',
            'title_en' => 'Notes',
            'icon' => 'fas fa-sticky-note',
            'url' => APP_URL . '/notes.php',
            'active' => $current_page === 'notes.php'
        ],
        [
            'title' => 'বুকমার্ক',
            'title_en' => 'Bookmarks',
            'icon' => 'fas fa-bookmark',
            'url' => APP_URL . '/bookmarks.php',
            'active' => $current_page === 'bookmarks.php'
        ]
    ];
    $menu_items = array_merge($menu_items, $tools_items);

    // Support & Documentation
    $support_items = [
        [
            'type' => 'divider',
            'title' => 'সাহায্য ও ডকুমেন্টেশন'
        ],
        [
            'title' => 'সাহায্য কেন্দ্র',
            'title_en' => 'Help Center',
            'icon' => 'fas fa-question-circle',
            'url' => APP_URL . '/help.php',
            'active' => $current_page === 'help.php',
            'submenu' => [
                [
                    'title' => 'FAQ',
                    'icon' => 'fas fa-question',
                    'url' => APP_URL . '/help/faq.php'
                ],
                [
                    'title' => 'টিউটোরিয়াল',
                    'icon' => 'fas fa-play-circle',
                    'url' => APP_URL . '/help/tutorials.php'
                ],
                [
                    'title' => 'ভিডিও গাইড',
                    'icon' => 'fas fa-video',
                    'url' => APP_URL . '/help/videos.php'
                ]
            ]
        ],
        [
            'title' => 'ফিডব্যাক',
            'title_en' => 'Feedback',
            'icon' => 'fas fa-comment',
            'url' => APP_URL . '/feedback.php',
            'active' => $current_page === 'feedback.php'
        ],
        [
            'title' => 'ডকুমেন্টেশন',
            'title_en' => 'Documentation',
            'icon' => 'fas fa-book',
            'url' => APP_URL . '/docs.php',
            'active' => $current_page === 'docs.php',
            'submenu' => [
                [
                    'title' => 'ব্যবহারকারী গাইড',
                    'icon' => 'fas fa-user-guide',
                    'url' => APP_URL . '/docs/user-guide.php'
                ],
                [
                    'title' => 'অ্যাডমিন গাইড',
                    'icon' => 'fas fa-crown',
                    'url' => APP_URL . '/docs/admin-guide.php'
                ],
                [
                    'title' => 'API ডকুমেন্টেশন',
                    'icon' => 'fas fa-code',
                    'url' => APP_URL . '/docs/api.php'
                ]
            ]
        ],
        [
            'title' => 'যোগাযোগ',
            'title_en' => 'Contact',
            'icon' => 'fas fa-phone',
            'url' => APP_URL . '/contact.php',
            'active' => $current_page === 'contact.php'
        ]
    ];
    $menu_items = array_merge($menu_items, $support_items);

    // Additional tools for admin
    if (isAdmin()) {
        $admin_tools = [
            [
                'type' => 'divider',
                'title' => 'অ্যাডমিন টুলস'
            ],
            [
                'title' => 'ডাটাবেস ম্যানেজার',
                'title_en' => 'Database Manager',
                'icon' => 'fas fa-database',
                'url' => APP_URL . '/admin/database.php',
                'active' => $current_page === 'database.php'
            ],
            [
                'title' => 'সিস্টেম লগ',
                'title_en' => 'System Logs',
                'icon' => 'fas fa-file-alt',
                'url' => APP_URL . '/admin/logs.php',
                'active' => $current_page === 'logs.php'
            ],
            [
                'title' => 'ব্যাকআপ ম্যানেজার',
                'title_en' => 'Backup Manager',
                'icon' => 'fas fa-save',
                'url' => APP_URL . '/admin/backup.php',
                'active' => $current_page === 'backup.php'
            ],
            [
                'title' => 'সিকিউরিটি',
                'title_en' => 'Security',
                'icon' => 'fas fa-shield-alt',
                'url' => APP_URL . '/admin/security.php',
                'active' => $current_page === 'security.php'
            ],
            [
                'title' => 'পারফরমেন্স',
                'title_en' => 'Performance',
                'icon' => 'fas fa-tachometer-alt',
                'url' => APP_URL . '/admin/performance.php',
                'active' => $current_page === 'performance.php'
            ],
            [
                'title' => 'মেইল সেটিংস',
                'title_en' => 'Mail Settings',
                'icon' => 'fas fa-mail-bulk',
                'url' => APP_URL . '/admin/mail.php',
                'active' => $current_page === 'mail.php'
            ]
        ];
        $menu_items = array_merge($menu_items, $admin_tools);
    }

} else {
    // Guest user menu items
    $menu_items = [
        [
            'title' => 'হোম',
            'title_en' => 'Home',
            'icon' => 'fas fa-home',
            'url' => APP_URL . '/',
            'active' => $current_page === 'index.php'
        ],
        [
            'title' => 'পাবলিক পোল',
            'title_en' => 'Public Polls',
            'icon' => 'fas fa-list',
            'url' => APP_URL . '/polls/',
            'active' => $current_dir === 'polls'
        ],
        [
            'title' => 'লগইন',
            'title_en' => 'Login',
            'icon' => 'fas fa-sign-in-alt',
            'url' => APP_URL . '/auth/login.php',
            'active' => $current_page === 'login.php'
        ],
        [
            'title' => 'রেজিস্ট্রেশন',
            'title_en' => 'Register',
            'icon' => 'fas fa-user-plus',
            'url' => APP_URL . '/auth/register.php',
            'active' => $current_page === 'register.php'
        ]
    ];
}
?>

<!-- Modern Sidebar -->
<div class="modern-sidebar" id="modernSidebar">
    <!-- Sidebar Header -->
    <div class="sidebar-header">
        <div class="sidebar-brand">
            <i class="fas fa-vote-yea"></i>
            <span class="brand-text">OVS</span>
        </div>
        <button class="sidebar-toggle" id="sidebarToggle">
            <i class="fas fa-times"></i>
        </button>
    </div>

    <!-- User Info (if logged in) -->
    <?php if (isLoggedIn()): ?>
    <div class="sidebar-user">
        <div class="user-avatar">
            <i class="fas fa-user"></i>
        </div>
        <div class="user-info">
            <div class="user-name"><?php echo htmlspecialchars(getCurrentUser()['username'] ?? 'User'); ?></div>
            <div class="user-role"><?php echo htmlspecialchars(getCurrentUser()['role'] ?? 'user'); ?></div>
        </div>
    </div>
    <?php endif; ?>

    <!-- Navigation Menu -->
    <nav class="sidebar-nav">
        <ul class="nav-list">
            <?php foreach ($menu_items as $item): ?>
                <?php if (isset($item['type']) && $item['type'] === 'divider'): ?>
                    <li class="nav-divider">
                        <span class="divider-text"><?php echo htmlspecialchars($item['title']); ?></span>
                    </li>
                <?php else: ?>
                    <li class="nav-item <?php echo isset($item['submenu']) ? 'has-submenu' : ''; ?>">
                        <a href="<?php echo htmlspecialchars($item['url']); ?>"
                           class="nav-link <?php echo $item['active'] ? 'active' : ''; ?> <?php echo isset($item['submenu']) ? 'submenu-toggle' : ''; ?>"
                           title="<?php echo htmlspecialchars($item['title_en'] ?? $item['title']); ?>"
                           <?php if (isset($item['submenu'])): ?>data-bs-toggle="collapse" data-bs-target="#submenu-<?php echo md5($item['title']); ?>"<?php endif; ?>>
                            <i class="<?php echo htmlspecialchars($item['icon']); ?>"></i>
                            <span class="nav-text"><?php echo htmlspecialchars($item['title']); ?></span>
                            <?php if (isset($item['badge'])): ?>
                                <span class="badge bg-<?php echo $item['badge_color'] ?? 'primary'; ?> ms-auto">
                                    <?php echo htmlspecialchars($item['badge']); ?>
                                </span>
                            <?php endif; ?>
                            <?php if (isset($item['submenu'])): ?>
                                <i class="fas fa-chevron-down submenu-arrow ms-auto"></i>
                            <?php endif; ?>
                        </a>

                        <?php if (isset($item['submenu'])): ?>
                        <div class="collapse submenu" id="submenu-<?php echo md5($item['title']); ?>">
                            <ul class="submenu-list">
                                <?php foreach ($item['submenu'] as $subitem): ?>
                                <li class="submenu-item">
                                    <a href="<?php echo htmlspecialchars($subitem['url']); ?>"
                                       class="submenu-link"
                                       title="<?php echo htmlspecialchars($subitem['title']); ?>">
                                        <i class="<?php echo htmlspecialchars($subitem['icon']); ?>"></i>
                                        <span><?php echo htmlspecialchars($subitem['title']); ?></span>
                                    </a>
                                </li>
                                <?php endforeach; ?>
                            </ul>
                        </div>
                        <?php endif; ?>
                    </li>
                <?php endif; ?>
            <?php endforeach; ?>
        </ul>
    </nav>

    <!-- Sidebar Footer -->
    <div class="sidebar-footer">
        <?php if (isLoggedIn()): ?>
            <a href="<?php echo APP_URL; ?>/auth/logout.php" class="logout-btn">
                <i class="fas fa-sign-out-alt"></i>
                <span>লগআউট</span>
            </a>
        <?php endif; ?>
        <div class="app-version">
            <small>Version <?php echo APP_VERSION; ?></small>
        </div>
    </div>
</div>

<!-- Sidebar Overlay -->
<div class="sidebar-overlay" id="sidebarOverlay"></div>

<!-- Mobile Sidebar Toggle Button -->
<button class="mobile-sidebar-toggle" id="mobileSidebarToggle">
    <i class="fas fa-bars"></i>
</button>
