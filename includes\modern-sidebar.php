<?php
/**
 * Modern Sidebar Component
 * মডার্ন সাইডবার কম্পোনেন্ট
 */

// Get current page for active menu highlighting
$current_page = basename($_SERVER['PHP_SELF']);
$current_dir = basename(dirname($_SERVER['PHP_SELF']));

// Define menu items based on user role
$menu_items = [];

if (isLoggedIn()) {
    // Common user menu items
    $menu_items = [
        [
            'title' => 'ড্যাশবোর্ড',
            'title_en' => 'Dashboard',
            'icon' => 'fas fa-tachometer-alt',
            'url' => APP_URL . '/dashboard.php',
            'active' => $current_page === 'dashboard.php'
        ],
        [
            'title' => 'পোল ব্রাউজ করুন',
            'title_en' => 'Browse Polls',
            'icon' => 'fas fa-list',
            'url' => APP_URL . '/polls/',
            'active' => $current_dir === 'polls' && $current_page === 'index.php'
        ],
        [
            'title' => 'নতুন পোল তৈরি',
            'title_en' => 'Create Poll',
            'icon' => 'fas fa-plus-circle',
            'url' => APP_URL . '/polls/create.php',
            'active' => $current_page === 'create.php'
        ],
        [
            'title' => 'আমার পোল',
            'title_en' => 'My Polls',
            'icon' => 'fas fa-poll',
            'url' => APP_URL . '/polls/manage.php',
            'active' => $current_page === 'manage.php'
        ],
        [
            'title' => 'প্রোফাইল',
            'title_en' => 'Profile',
            'icon' => 'fas fa-user',
            'url' => APP_URL . '/profile.php',
            'active' => $current_page === 'profile.php'
        ]
    ];

    // Admin menu items
    if (isAdmin()) {
        $admin_items = [
            [
                'type' => 'divider',
                'title' => 'অ্যাডমিন প্যানেল'
            ],
            [
                'title' => 'অ্যাডমিন ড্যাশবোর্ড',
                'title_en' => 'Admin Dashboard',
                'icon' => 'fas fa-cogs',
                'url' => APP_URL . '/admin/',
                'active' => $current_dir === 'admin' && $current_page === 'index.php'
            ],
            [
                'title' => 'ভোটার ব্যবস্থাপনা',
                'title_en' => 'Voter Management',
                'icon' => 'fas fa-users',
                'url' => APP_URL . '/admin/voters.php',
                'active' => $current_page === 'voters.php'
            ],
            [
                'title' => 'প্রার্থী ব্যবস্থাপনা',
                'title_en' => 'Candidate Management',
                'icon' => 'fas fa-user-tie',
                'url' => APP_URL . '/admin/candidates.php',
                'active' => $current_page === 'candidates.php'
            ],
            [
                'title' => 'এলাকা ব্যবস্থাপনা',
                'title_en' => 'Area Management',
                'icon' => 'fas fa-map-marked-alt',
                'url' => APP_URL . '/admin/areas.php',
                'active' => $current_page === 'areas.php'
            ],
            [
                'title' => 'পোল ব্যবস্থাপনা',
                'title_en' => 'Poll Management',
                'icon' => 'fas fa-chart-bar',
                'url' => APP_URL . '/admin/polls.php',
                'active' => $current_page === 'polls.php'
            ],
            [
                'title' => 'ব্যবহারকারী ব্যবস্থাপনা',
                'title_en' => 'User Management',
                'icon' => 'fas fa-users-cog',
                'url' => APP_URL . '/admin/users.php',
                'active' => $current_page === 'users.php'
            ],
            [
                'title' => 'রিপোর্ট ও অ্যানালিটিক্স',
                'title_en' => 'Reports & Analytics',
                'icon' => 'fas fa-chart-line',
                'url' => APP_URL . '/admin/analytics.php',
                'active' => $current_page === 'analytics.php'
            ],
            [
                'title' => 'সিস্টেম সেটিংস',
                'title_en' => 'System Settings',
                'icon' => 'fas fa-cog',
                'url' => APP_URL . '/admin/settings.php',
                'active' => $current_page === 'settings.php'
            ]
        ];
        $menu_items = array_merge($menu_items, $admin_items);
    }

    // Quick actions
    $quick_actions = [
        [
            'type' => 'divider',
            'title' => 'দ্রুত অ্যাকশন'
        ],
        [
            'title' => 'নোটিফিকেশন',
            'title_en' => 'Notifications',
            'icon' => 'fas fa-bell',
            'url' => APP_URL . '/notifications.php',
            'active' => $current_page === 'notifications.php'
        ],
        [
            'title' => 'মেসেজ',
            'title_en' => 'Messages',
            'icon' => 'fas fa-envelope',
            'url' => APP_URL . '/messages.php',
            'active' => $current_page === 'messages.php'
        ],
        [
            'title' => 'ফাইল ম্যানেজার',
            'title_en' => 'File Manager',
            'icon' => 'fas fa-folder',
            'url' => APP_URL . '/files.php',
            'active' => $current_page === 'files.php'
        ],
        [
            'title' => 'ক্যালেন্ডার',
            'title_en' => 'Calendar',
            'icon' => 'fas fa-calendar',
            'url' => APP_URL . '/calendar.php',
            'active' => $current_page === 'calendar.php'
        ],
        [
            'title' => 'টাস্ক ম্যানেজার',
            'title_en' => 'Task Manager',
            'icon' => 'fas fa-tasks',
            'url' => APP_URL . '/tasks.php',
            'active' => $current_page === 'tasks.php'
        ],
        [
            'title' => 'সাহায্য ও সাপোর্ট',
            'title_en' => 'Help & Support',
            'icon' => 'fas fa-question-circle',
            'url' => APP_URL . '/help.php',
            'active' => $current_page === 'help.php'
        ],
        [
            'title' => 'ফিডব্যাক',
            'title_en' => 'Feedback',
            'icon' => 'fas fa-comment',
            'url' => APP_URL . '/feedback.php',
            'active' => $current_page === 'feedback.php'
        ],
        [
            'title' => 'ডকুমেন্টেশন',
            'title_en' => 'Documentation',
            'icon' => 'fas fa-book',
            'url' => APP_URL . '/docs.php',
            'active' => $current_page === 'docs.php'
        ],
        [
            'title' => 'API ডকস',
            'title_en' => 'API Docs',
            'icon' => 'fas fa-code',
            'url' => APP_URL . '/api-docs.php',
            'active' => $current_page === 'api-docs.php'
        ]
    ];
    $menu_items = array_merge($menu_items, $quick_actions);

    // Additional tools for admin
    if (isAdmin()) {
        $admin_tools = [
            [
                'type' => 'divider',
                'title' => 'অ্যাডমিন টুলস'
            ],
            [
                'title' => 'ডাটাবেস ম্যানেজার',
                'title_en' => 'Database Manager',
                'icon' => 'fas fa-database',
                'url' => APP_URL . '/admin/database.php',
                'active' => $current_page === 'database.php'
            ],
            [
                'title' => 'সিস্টেম লগ',
                'title_en' => 'System Logs',
                'icon' => 'fas fa-file-alt',
                'url' => APP_URL . '/admin/logs.php',
                'active' => $current_page === 'logs.php'
            ],
            [
                'title' => 'ব্যাকআপ ম্যানেজার',
                'title_en' => 'Backup Manager',
                'icon' => 'fas fa-save',
                'url' => APP_URL . '/admin/backup.php',
                'active' => $current_page === 'backup.php'
            ],
            [
                'title' => 'সিকিউরিটি',
                'title_en' => 'Security',
                'icon' => 'fas fa-shield-alt',
                'url' => APP_URL . '/admin/security.php',
                'active' => $current_page === 'security.php'
            ],
            [
                'title' => 'পারফরমেন্স',
                'title_en' => 'Performance',
                'icon' => 'fas fa-tachometer-alt',
                'url' => APP_URL . '/admin/performance.php',
                'active' => $current_page === 'performance.php'
            ],
            [
                'title' => 'মেইল সেটিংস',
                'title_en' => 'Mail Settings',
                'icon' => 'fas fa-mail-bulk',
                'url' => APP_URL . '/admin/mail.php',
                'active' => $current_page === 'mail.php'
            ]
        ];
        $menu_items = array_merge($menu_items, $admin_tools);
    }

} else {
    // Guest user menu items
    $menu_items = [
        [
            'title' => 'হোম',
            'title_en' => 'Home',
            'icon' => 'fas fa-home',
            'url' => APP_URL . '/',
            'active' => $current_page === 'index.php'
        ],
        [
            'title' => 'পাবলিক পোল',
            'title_en' => 'Public Polls',
            'icon' => 'fas fa-list',
            'url' => APP_URL . '/polls/',
            'active' => $current_dir === 'polls'
        ],
        [
            'title' => 'লগইন',
            'title_en' => 'Login',
            'icon' => 'fas fa-sign-in-alt',
            'url' => APP_URL . '/auth/login.php',
            'active' => $current_page === 'login.php'
        ],
        [
            'title' => 'রেজিস্ট্রেশন',
            'title_en' => 'Register',
            'icon' => 'fas fa-user-plus',
            'url' => APP_URL . '/auth/register.php',
            'active' => $current_page === 'register.php'
        ]
    ];
}
?>

<!-- Modern Sidebar -->
<div class="modern-sidebar" id="modernSidebar">
    <!-- Sidebar Header -->
    <div class="sidebar-header">
        <div class="sidebar-brand">
            <i class="fas fa-vote-yea"></i>
            <span class="brand-text">OVS</span>
        </div>
        <button class="sidebar-toggle" id="sidebarToggle">
            <i class="fas fa-times"></i>
        </button>
    </div>

    <!-- User Info (if logged in) -->
    <?php if (isLoggedIn()): ?>
    <div class="sidebar-user">
        <div class="user-avatar">
            <i class="fas fa-user"></i>
        </div>
        <div class="user-info">
            <div class="user-name"><?php echo htmlspecialchars(getCurrentUser()['username'] ?? 'User'); ?></div>
            <div class="user-role"><?php echo htmlspecialchars(getCurrentUser()['role'] ?? 'user'); ?></div>
        </div>
    </div>
    <?php endif; ?>

    <!-- Navigation Menu -->
    <nav class="sidebar-nav">
        <ul class="nav-list">
            <?php foreach ($menu_items as $item): ?>
                <?php if (isset($item['type']) && $item['type'] === 'divider'): ?>
                    <li class="nav-divider">
                        <span class="divider-text"><?php echo htmlspecialchars($item['title']); ?></span>
                    </li>
                <?php else: ?>
                    <li class="nav-item">
                        <a href="<?php echo htmlspecialchars($item['url']); ?>" 
                           class="nav-link <?php echo $item['active'] ? 'active' : ''; ?>"
                           title="<?php echo htmlspecialchars($item['title_en'] ?? $item['title']); ?>">
                            <i class="<?php echo htmlspecialchars($item['icon']); ?>"></i>
                            <span class="nav-text"><?php echo htmlspecialchars($item['title']); ?></span>
                        </a>
                    </li>
                <?php endif; ?>
            <?php endforeach; ?>
        </ul>
    </nav>

    <!-- Sidebar Footer -->
    <div class="sidebar-footer">
        <?php if (isLoggedIn()): ?>
            <a href="<?php echo APP_URL; ?>/auth/logout.php" class="logout-btn">
                <i class="fas fa-sign-out-alt"></i>
                <span>লগআউট</span>
            </a>
        <?php endif; ?>
        <div class="app-version">
            <small>Version <?php echo APP_VERSION; ?></small>
        </div>
    </div>
</div>

<!-- Sidebar Overlay -->
<div class="sidebar-overlay" id="sidebarOverlay"></div>

<!-- Mobile Sidebar Toggle Button -->
<button class="mobile-sidebar-toggle" id="mobileSidebarToggle">
    <i class="fas fa-bars"></i>
</button>
