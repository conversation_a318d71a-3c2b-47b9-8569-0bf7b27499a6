-- এলাকা/ওয়ার্ড ভিত্তিক ভোটিং সিস্টেমের জন্য ডাটাবেস আপডেট
-- Area/Ward Based Voting System Database Update

-- ১. এলাকা/ওয়ার্ড টেবিল তৈরি
CREATE TABLE `areas` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(100) NOT NULL,
  `name_bn` varchar(100) DEFAULT NULL,
  `type` enum('ward','area','district','division') NOT NULL DEFAULT 'ward',
  `parent_id` int(11) DEFAULT NULL,
  `code` varchar(20) DEFAULT NULL,
  `description` text DEFAULT NULL,
  `is_active` tinyint(1) NOT NULL DEFAULT 1,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `code` (`code`),
  KEY `parent_id` (`parent_id`),
  <PERSON><PERSON>Y `idx_type` (`type`),
  KEY `idx_active` (`is_active`),
  CONSTRAINT `areas_ibfk_1` FOREIGN KEY (`parent_id`) REFERENCES `areas` (`id`) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- ২. ভোটার তথ্য টেবিল
CREATE TABLE `voters` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `voter_id` varchar(50) NOT NULL,
  `name` varchar(100) NOT NULL,
  `name_bn` varchar(100) DEFAULT NULL,
  `father_name` varchar(100) DEFAULT NULL,
  `mother_name` varchar(100) DEFAULT NULL,
  `nid` varchar(20) DEFAULT NULL,
  `mobile` varchar(15) DEFAULT NULL,
  `email` varchar(100) DEFAULT NULL,
  `date_of_birth` date DEFAULT NULL,
  `gender` enum('male','female','other') DEFAULT NULL,
  `area_id` int(11) NOT NULL,
  `address` text DEFAULT NULL,
  `photo` varchar(255) DEFAULT NULL,
  `is_active` tinyint(1) NOT NULL DEFAULT 1,
  `user_id` int(11) DEFAULT NULL,
  `created_by` int(11) DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `voter_id` (`voter_id`),
  UNIQUE KEY `nid` (`nid`),
  KEY `area_id` (`area_id`),
  KEY `user_id` (`user_id`),
  KEY `created_by` (`created_by`),
  KEY `idx_active` (`is_active`),
  CONSTRAINT `voters_ibfk_1` FOREIGN KEY (`area_id`) REFERENCES `areas` (`id`) ON DELETE RESTRICT,
  CONSTRAINT `voters_ibfk_2` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE SET NULL,
  CONSTRAINT `voters_ibfk_3` FOREIGN KEY (`created_by`) REFERENCES `users` (`id`) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- ৩. users টেবিলে area_id যোগ করা
ALTER TABLE `users` ADD COLUMN `area_id` int(11) DEFAULT NULL AFTER `location`;
ALTER TABLE `users` ADD COLUMN `voter_id` varchar(50) DEFAULT NULL AFTER `area_id`;
ALTER TABLE `users` ADD KEY `area_id` (`area_id`);
ALTER TABLE `users` ADD KEY `voter_id` (`voter_id`);
ALTER TABLE `users` ADD CONSTRAINT `users_ibfk_1` FOREIGN KEY (`area_id`) REFERENCES `areas` (`id`) ON DELETE SET NULL;

-- ৪. polls টেবিলে voting access control যোগ করা
ALTER TABLE `polls` ADD COLUMN `access_type` enum('public','area_based','mixed') NOT NULL DEFAULT 'public' AFTER `is_public`;
ALTER TABLE `polls` ADD COLUMN `allowed_areas` json DEFAULT NULL AFTER `access_type`;
ALTER TABLE `polls` ADD COLUMN `require_voter_verification` tinyint(1) NOT NULL DEFAULT 0 AFTER `allowed_areas`;

-- ৫. votes টেবিলে voter_id যোগ করা
ALTER TABLE `votes` ADD COLUMN `voter_id` varchar(50) DEFAULT NULL AFTER `user_id`;
ALTER TABLE `votes` ADD KEY `voter_id` (`voter_id`);

-- ৬. ভোটার আপলোড ব্যাচ ট্র্যাকিং
CREATE TABLE `voter_uploads` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `filename` varchar(255) NOT NULL,
  `original_filename` varchar(255) NOT NULL,
  `total_records` int(11) NOT NULL DEFAULT 0,
  `successful_imports` int(11) NOT NULL DEFAULT 0,
  `failed_imports` int(11) NOT NULL DEFAULT 0,
  `status` enum('pending','processing','completed','failed') NOT NULL DEFAULT 'pending',
  `error_log` text DEFAULT NULL,
  `uploaded_by` int(11) NOT NULL,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `completed_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `uploaded_by` (`uploaded_by`),
  KEY `idx_status` (`status`),
  CONSTRAINT `voter_uploads_ibfk_1` FOREIGN KEY (`uploaded_by`) REFERENCES `users` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- ৭. নমুনা এলাকা/ওয়ার্ড ডেটা
INSERT INTO `areas` (`name`, `name_bn`, `type`, `code`, `description`) VALUES
('ঢাকা বিভাগ', 'ঢাকা বিভাগ', 'division', 'DIV-01', 'ঢাকা বিভাগ'),
('ঢাকা জেলা', 'ঢাকা জেলা', 'district', 'DIS-01', 'ঢাকা জেলা'),
('ধানমন্ডি', 'ধানমন্ডি', 'area', 'AREA-01', 'ধানমন্ডি এলাকা'),
('ওয়ার্ড নং ১', 'ওয়ার্ড নং ১', 'ward', 'WARD-01', 'ধানমন্ডি ওয়ার্ড নং ১'),
('ওয়ার্ড নং ২', 'ওয়ার্ড নং ২', 'ward', 'WARD-02', 'ধানমন্ডি ওয়ার্ড নং ২'),
('গুলশান', 'গুলশান', 'area', 'AREA-02', 'গুলশান এলাকা'),
('ওয়ার্ড নং ৩', 'ওয়ার্ড নং ৩', 'ward', 'WARD-03', 'গুলশান ওয়ার্ড নং ৩'),
('ওয়ার্ড নং ৪', 'ওয়ার্ড নং ৪', 'ward', 'WARD-04', 'গুলশান ওয়ার্ড নং ৪'),
('বনানী', 'বনানী', 'area', 'AREA-03', 'বনানী এলাকা'),
('ওয়ার্ড নং ৫', 'ওয়ার্ড নং ৫', 'ward', 'WARD-05', 'বনানী ওয়ার্ড নং ৫');

-- ৮. এলাকার parent relationship সেট করা
UPDATE `areas` SET `parent_id` = 2 WHERE `id` IN (3, 6, 9); -- এলাকাগুলো ঢাকা জেলার অধীনে
UPDATE `areas` SET `parent_id` = 1 WHERE `id` = 2; -- ঢাকা জেলা ঢাকা বিভাগের অধীনে
UPDATE `areas` SET `parent_id` = 3 WHERE `id` IN (4, 5); -- ধানমন্ডির ওয়ার্ডগুলো
UPDATE `areas` SET `parent_id` = 6 WHERE `id` IN (7, 8); -- গুলশানের ওয়ার্ডগুলো
UPDATE `areas` SET `parent_id` = 9 WHERE `id` = 10; -- বনানীর ওয়ার্ড

-- ৯. নমুনা ভোটার ডেটা
INSERT INTO `voters` (`voter_id`, `name`, `name_bn`, `nid`, `mobile`, `area_id`, `address`, `created_by`) VALUES
('V001', 'আহমেদ আলী', 'আহমেদ আলী', '1234567890123', '01712345678', 4, 'ধানমন্ডি, ঢাকা', 1),
('V002', 'ফাতেমা খাতুন', 'ফাতেমা খাতুন', '1234567890124', '01812345678', 4, 'ধানমন্ডি, ঢাকা', 1),
('V003', 'মোহাম্মদ রহিম', 'মোহাম্মদ রহিম', '1234567890125', '01912345678', 5, 'ধানমন্ডি, ঢাকা', 1),
('V004', 'রাশিদা বেগম', 'রাশিদা বেগম', '1234567890126', '01612345678', 7, 'গুলশান, ঢাকা', 1),
('V005', 'করিম উদ্দিন', 'করিম উদ্দিন', '1234567890127', '01512345678', 7, 'গুলশান, ঢাকা', 1),
('V006', 'সালমা আক্তার', 'সালমা আক্তার', '1234567890128', '01412345678', 8, 'গুলশান, ঢাকা', 1),
('V007', 'আব্দুল কাদের', 'আব্দুল কাদের', '1234567890129', '01312345678', 10, 'বনানী, ঢাকা', 1),
('V008', 'নাসিরা খাতুন', 'নাসিরা খাতুন', '1234567890130', '01212345678', 10, 'বনানী, ঢাকা', 1);

-- ১০. existing users এর সাথে area link করা
UPDATE `users` SET `area_id` = 4 WHERE `id` = 2; -- john_doe ধানমন্ডি ওয়ার্ড ১
UPDATE `users` SET `area_id` = 7 WHERE `id` = 3; -- jane_smith গুলশান ওয়ার্ড ৩
UPDATE `users` SET `area_id` = 10 WHERE `id` = 5; -- demo_user বনানী ওয়ার্ড ৫

-- ১১. system config আপডেট
INSERT INTO `system_config` (`config_key`, `config_value`) VALUES
('enable_area_based_voting', 'true'),
('require_voter_verification', 'false'),
('allow_voter_self_registration', 'true'),
('voter_id_format', '"V{number:3}"'),
('default_access_type', '"public"');

COMMIT;
