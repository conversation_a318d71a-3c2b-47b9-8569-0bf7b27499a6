# Setup Guide - Dynamic Realtime Online Voting System

## Prerequisites

- **XAMPP/WAMP/LAMP** - Web server with PHP 8+ and MySQL 8+
- **Web Browser** - Modern browser with JavaScript enabled
- **Text Editor** - For configuration (optional)

## Installation Steps

### 1. Download and Extract
```bash
# If using Git
git clone <repository-url> /path/to/xampp/htdocs/ovs

# Or extract ZIP file to your web server directory
# Example: C:\xampp\htdocs\ovs (Windows)
# Example: /var/www/html/ovs (Linux)
```

### 2. Database Setup

1. **Start your web server** (Apache and MySQL)

2. **Access phpMyAdmin**
   - Open: `http://localhost/phpmyadmin`
   - Login with your MySQL credentials

3. **Create Database**
   ```sql
   CREATE DATABASE ovs_db CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
   ```

4. **Import Schema**
   - Select the `ovs_db` database
   - Go to "Import" tab
   - Choose file: `database/schema.sql`
   - Click "Go" to import

5. **Import Sample Data (Optional)**
   - Go to "Import" tab again
   - Choose file: `database/seeds.sql`
   - Click "Go" to import sample data

### 3. Configuration

1. **Copy Configuration File**
   ```bash
   cp config/config.example.php config/config.php
   ```

2. **Edit Database Settings**
   Open `config/config.php` and update:
   ```php
   // Database Configuration
   define('DB_HOST', 'localhost');
   define('DB_NAME', 'ovs_db');
   define('DB_USER', 'root');        // Your MySQL username
   define('DB_PASS', '');            // Your MySQL password
   
   // Application Settings
   define('APP_URL', 'http://localhost/ovs');  // Your site URL
   ```

3. **Set Permissions (Linux/Mac)**
   ```bash
   chmod 755 assets/uploads/
   chmod 644 config/config.php
   ```

### 4. Test Installation

1. **Access the Application**
   - Open: `http://localhost/ovs`
   - You should see the homepage

2. **Test Database Connection**
   - If you see database errors, check your configuration
   - Verify MySQL is running and credentials are correct

## Demo Accounts

If you imported the sample data, you can use these accounts:

| Role | Email | Password | Description |
|------|-------|----------|-------------|
| Admin | <EMAIL> | admin123 | Full system access |
| User | <EMAIL> | password123 | Regular user |
| User | <EMAIL> | password123 | Regular user |
| Moderator | <EMAIL> | password123 | Moderation access |

## Features Available

### ✅ **Currently Working**
- User registration and login
- Session management with security
- Poll creation with multiple types:
  - Single choice
  - Multiple choice
  - Text response
  - Rating scale (1-10)
- Real-time AJAX updates
- Responsive Bootstrap UI
- Database with sample data
- Basic security (CSRF, input validation)

### 🚧 **In Development**
- Poll voting interface
- Real-time results display
- User dashboard
- Admin panel
- Advanced customization
- Email notifications
- Analytics and reporting

## Troubleshooting

### Common Issues

1. **Database Connection Error**
   - Check MySQL is running
   - Verify database credentials in `config/config.php`
   - Ensure database `ovs_db` exists

2. **Permission Denied Errors**
   - Check file permissions on uploads directory
   - Ensure web server can write to `assets/uploads/`

3. **Page Not Found (404)**
   - Verify the correct URL path
   - Check if `.htaccess` is supported (Apache)
   - Ensure all files are in the correct directory

4. **JavaScript Errors**
   - Check browser console for errors
   - Verify jQuery and Bootstrap are loading
   - Clear browser cache

### Debug Mode

Enable debug mode in `config/config.php`:
```php
define('APP_DEBUG', true);
```

This will show detailed error messages to help troubleshoot issues.

## Next Steps

1. **Create Your First Poll**
   - Login with demo account or register new account
   - Go to "Create Poll" 
   - Test different poll types

2. **Customize Settings**
   - Update `config/config.php` with your preferences
   - Modify `assets/css/style.css` for custom styling

3. **Add Real Users**
   - Disable demo accounts in production
   - Set up email configuration for notifications
   - Configure proper security settings

## Security Notes

### For Production Use:

1. **Change Default Passwords**
   ```sql
   UPDATE users SET password = '$2y$12$newhashedpassword' WHERE email = '<EMAIL>';
   ```

2. **Update Configuration**
   ```php
   define('APP_DEBUG', false);  // Disable debug mode
   define('APP_URL', 'https://yourdomain.com');  // Use HTTPS
   ```

3. **Secure File Permissions**
   ```bash
   chmod 600 config/config.php
   chmod 755 assets/uploads/
   ```

4. **Enable HTTPS**
   - Configure SSL certificate
   - Update session settings for secure cookies

## Support

- Check the README.md for detailed feature documentation
- Review the code comments for implementation details
- Test with sample data before adding real polls

---

**Congratulations!** Your Dynamic Realtime Online Voting System is now ready to use! 🎉
