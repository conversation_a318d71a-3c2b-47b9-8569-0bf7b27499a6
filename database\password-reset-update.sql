-- Password Reset Feature Database Update
-- Add password reset columns to users table

ALTER TABLE `users` 
ADD COLUMN `password_reset_token` varchar(255) DEFAULT NULL AFTER `password`,
ADD COLUMN `password_reset_expires` timestamp NULL DEFAULT NULL AFTER `password_reset_token`;

-- Add index for faster token lookup
ALTER TABLE `users` 
ADD KEY `idx_password_reset_token` (`password_reset_token`),
ADD KEY `idx_password_reset_expires` (`password_reset_expires`);

COMMIT;
