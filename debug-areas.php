<?php
/**
 * Debug Areas Page
 * Find the cause of blinking/refreshing
 */

require_once '../config/config.php';

// Require admin access
if (!isLoggedIn() || !isAdmin()) {
    redirect(APP_URL . '/auth/login.php');
}

echo "<h2>🔍 Debug Areas Page</h2>";

// Check for any redirects or headers
if (headers_sent($file, $line)) {
    echo "<div style='background: #f8d7da; padding: 10px; border-radius: 5px;'>";
    echo "⚠️ Headers already sent from: $file on line $line";
    echo "</div>";
}

// Check for any output buffering
echo "<div style='background: #e7f3ff; padding: 10px; border-radius: 5px; margin: 10px 0;'>";
echo "<h4>Debug Information:</h4>";
echo "<ul>";
echo "<li>Output buffering level: " . ob_get_level() . "</li>";
echo "<li>Headers sent: " . (headers_sent() ? 'Yes' : 'No') . "</li>";
echo "<li>PHP errors: " . (error_get_last() ? 'Yes - ' . error_get_last()['message'] : 'None') . "</li>";
echo "<li>Memory usage: " . memory_get_usage(true) . " bytes</li>";
echo "</ul>";
echo "</div>";

// Test database connection
try {
    $db = getDB();
    echo "<div style='background: #d4edda; padding: 10px; border-radius: 5px; margin: 10px 0;'>";
    echo "✅ Database connection: OK";
    echo "</div>";
    
    // Test areas query
    $stmt = $db->query("SELECT COUNT(*) as count FROM areas WHERE is_active = 1");
    $area_count = $stmt->fetch()['count'];
    echo "<div style='background: #d4edda; padding: 10px; border-radius: 5px; margin: 10px 0;'>";
    echo "✅ Areas count: $area_count";
    echo "</div>";
    
} catch (Exception $e) {
    echo "<div style='background: #f8d7da; padding: 10px; border-radius: 5px; margin: 10px 0;'>";
    echo "❌ Database error: " . $e->getMessage();
    echo "</div>";
}

// Check for any meta refresh tags or JavaScript redirects
echo "<div style='background: #fff3cd; padding: 10px; border-radius: 5px; margin: 10px 0;'>";
echo "<h4>Possible Causes of Blinking:</h4>";
echo "<ol>";
echo "<li>Meta refresh tag in header</li>";
echo "<li>JavaScript auto-refresh/reload</li>";
echo "<li>AJAX polling</li>";
echo "<li>PHP redirect loop</li>";
echo "<li>Browser auto-refresh extension</li>";
echo "</ol>";
echo "</div>";

// Simple test page without complex JavaScript
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Debug Areas</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- NO META REFRESH TAG -->
</head>
<body>
    <div class="container mt-4">
        <h3>🧪 Simple Areas Test</h3>
        
        <div class="alert alert-info">
            <h5>Test Instructions:</h5>
            <ol>
                <li>Watch this page for 30 seconds</li>
                <li>Check if it blinks/refreshes</li>
                <li>Open browser console (F12) and check for errors</li>
                <li>Check Network tab for automatic requests</li>
            </ol>
        </div>
        
        <div class="card">
            <div class="card-header">
                <h5>Current Time: <span id="current-time"><?php echo date('Y-m-d H:i:s'); ?></span></h5>
            </div>
            <div class="card-body">
                <p>If this time changes automatically, the page is refreshing.</p>
                <p>Current timestamp: <?php echo time(); ?></p>
                
                <h6>Quick Areas List:</h6>
                <?php
                try {
                    $stmt = $db->query("SELECT id, name_bn, type FROM areas WHERE is_active = 1 LIMIT 5");
                    $areas = $stmt->fetchAll();
                    
                    if (!empty($areas)) {
                        echo "<ul>";
                        foreach ($areas as $area) {
                            echo "<li>" . htmlspecialchars($area['name_bn']) . " (" . $area['type'] . ")</li>";
                        }
                        echo "</ul>";
                    } else {
                        echo "<p>No areas found</p>";
                    }
                } catch (Exception $e) {
                    echo "<p style='color: red;'>Error: " . $e->getMessage() . "</p>";
                }
                ?>
            </div>
        </div>
        
        <div class="mt-3">
            <a href="areas.php" class="btn btn-primary">Go to Original Areas Page</a>
            <a href="../" class="btn btn-secondary">Go to Home</a>
        </div>
    </div>

    <!-- Minimal JavaScript - NO AUTO REFRESH -->
    <script>
        console.log('Debug page loaded at:', new Date());
        
        // Check for any intervals or timeouts
        let intervalCount = 0;
        let timeoutCount = 0;
        
        const originalSetInterval = window.setInterval;
        const originalSetTimeout = window.setTimeout;
        
        window.setInterval = function(...args) {
            intervalCount++;
            console.warn('setInterval called #' + intervalCount, args);
            return originalSetInterval.apply(this, args);
        };
        
        window.setTimeout = function(...args) {
            timeoutCount++;
            console.warn('setTimeout called #' + timeoutCount, args);
            return originalSetTimeout.apply(this, args);
        };
        
        // Log any page visibility changes
        document.addEventListener('visibilitychange', function() {
            console.log('Page visibility changed:', document.hidden ? 'hidden' : 'visible');
        });
        
        // Log any beforeunload events
        window.addEventListener('beforeunload', function(e) {
            console.log('Page is about to unload/refresh');
        });
        
        // Check for any automatic form submissions
        document.addEventListener('submit', function(e) {
            console.warn('Form submitted:', e.target);
        });
        
        // Monitor for any location changes
        let currentLocation = window.location.href;
        setInterval(function() {
            if (window.location.href !== currentLocation) {
                console.warn('Location changed from', currentLocation, 'to', window.location.href);
                currentLocation = window.location.href;
            }
        }, 1000);
    </script>
</body>
</html>
