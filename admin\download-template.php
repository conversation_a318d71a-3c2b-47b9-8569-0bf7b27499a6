<?php
/**
 * Download Voter Upload Template
 * Generate CSV template for voter bulk upload
 */

require_once '../config/config.php';

// Check if user is logged in and is admin
if (!isLoggedIn() || !isAdmin()) {
    redirect(APP_URL . '/auth/login.php');
}

$format = $_GET['format'] ?? 'csv';

if ($format === 'csv') {
    // Set headers for CSV download
    header('Content-Type: text/csv; charset=utf-8');
    header('Content-Disposition: attachment; filename="voter-upload-template.csv"');
    header('Cache-Control: no-cache, must-revalidate');
    
    // Create file pointer
    $output = fopen('php://output', 'w');
    
    // Add BOM for UTF-8 (helps with Bengali characters in Excel)
    fprintf($output, chr(0xEF).chr(0xBB).chr(0xBF));
    
    // CSV Headers with hierarchical structure
    $headers = [
        'voter_id',
        'name',
        'name_bn',
        'father_name',
        'mother_name',
        'nid',
        'mobile',
        'email',
        'date_of_birth',
        'gender',
        'division_name',
        'district_name',
        'upazila_name',
        'union_name',
        'village_name',
        'ward_name',
        'ward_id',
        'address'
    ];
    
    fputcsv($output, $headers);
    
    // Get actual ward IDs from database
    try {
        $db = getDB();
        $stmt = $db->query("SELECT id FROM areas WHERE type = 'ward' AND is_active = 1 ORDER BY id LIMIT 5");
        $ward_ids = $stmt->fetchAll(PDO::FETCH_COLUMN);

        // If no wards found, use placeholder IDs
        if (empty($ward_ids)) {
            $ward_ids = [1, 2, 3, 4, 5];
        }
    } catch (Exception $e) {
        $ward_ids = [1, 2, 3, 4, 5];
    }

    // Sample data with hierarchical structure using real ward IDs
    // voter_id is empty - will be auto-generated during upload
    $sample_data = [
        [
            '', // Empty voter_id - will be auto-generated
            'Md. Rahman Khan',
            'মোঃ রহমান খান',
            'Md. Abdul Khan',
            'Mst. Fatema Khan',
            '1234567890123',
            '01712345678',
            '<EMAIL>',
            '1985-05-15',
            'male',
            'ঢাকা বিভাগ',
            'ঢাকা জেলা',
            'ধানমন্ডি উপজেলা',
            'ধানমন্ডি পৌরসভা',
            '',
            'ধানমন্ডি ওয়ার্ড ০১',
            $ward_ids[0] ?? 1,
            'বাড়ি নং ১২৩, রোড নং ৫, ধানমন্ডি, ঢাকা'
        ],
        [
            '', // Empty voter_id - will be auto-generated
            'Mst. Salma Begum',
            'মোসাঃ সালমা বেগম',
            'Md. Karim Uddin',
            'Mst. Rashida Begum',
            '2345678901234',
            '01823456789',
            '<EMAIL>',
            '1990-08-22',
            'female',
            'ঢাকা বিভাগ',
            'ঢাকা জেলা',
            'ধানমন্ডি উপজেলা',
            'ধানমন্ডি পৌরসভা',
            '',
            'ধানমন্ডি ওয়ার্ড ০২',
            $ward_ids[1] ?? 2,
            'বাড়ি নং ৪৫৬, রোড নং ৭, ধানমন্ডি, ঢাকা'
        ],
        [
            '', // Empty voter_id - will be auto-generated
            'Md. Kamal Hossain',
            'মোঃ কামাল হোসেন',
            'Md. Nazrul Islam',
            'Mst. Rahima Khatun',
            '3456789012345',
            '01934567890',
            '<EMAIL>',
            '1988-12-10',
            'male',
            'ঢাকা বিভাগ',
            'ঢাকা জেলা',
            'গুলশান উপজেলা',
            'গুলশান ইউনিয়ন',
            'গুলশান গ্রাম',
            'গুলশান ওয়ার্ড ০১',
            $ward_ids[2] ?? 3,
            'বাড়ি নং ৭৮৯, রোড নং ৯, গুলশান, ঢাকা'
        ],
        [
            '', // Empty voter_id - will be auto-generated
            'Mst. Nasreen Akter',
            'মোসাঃ নাসরিন আক্তার',
            'Md. Shahid Ullah',
            'Mst. Jahanara Begum',
            '4567890123456',
            '01645678901',
            '<EMAIL>',
            '1992-03-18',
            'female',
            'ঢাকা বিভাগ',
            'ঢাকা জেলা',
            'গুলশান উপজেলা',
            'গুলশান ইউনিয়ন',
            'গুলশান গ্রাম',
            'গুলশান ওয়ার্ড ০২',
            $ward_ids[3] ?? 4,
            'বাড়ি নং ১০১, রোড নং ১১, গুলশান, ঢাকা'
        ],
        [
            '', // Empty voter_id - will be auto-generated
            'Md. Aminul Islam',
            'মোঃ আমিনুল ইসলাম',
            'Md. Rafiqul Islam',
            'Mst. Rokeya Begum',
            '5678901234567',
            '01756789012',
            '<EMAIL>',
            '1987-07-25',
            'male',
            'ঢাকা বিভাগ',
            'ঢাকা জেলা',
            'তেজগাঁও উপজেলা',
            'তেজগাঁও ইউনিয়ন',
            'তেজগাঁও গ্রাম',
            'তেজগাঁও ওয়ার্ড ০১',
            $ward_ids[4] ?? 5,
            'বাড়ি নং ২০২, রোড নং ১৩, তেজগাঁও, ঢাকা'
        ]
    ];
    
    // Add sample data
    foreach ($sample_data as $row) {
        fputcsv($output, $row);
    }
    
    fclose($output);
    exit;
    
} elseif ($format === 'areas') {
    // Download areas list for reference
    header('Content-Type: text/csv; charset=utf-8');
    header('Content-Disposition: attachment; filename="areas-reference.csv"');
    header('Cache-Control: no-cache, must-revalidate');
    
    $output = fopen('php://output', 'w');
    fprintf($output, chr(0xEF).chr(0xBB).chr(0xBF));
    
    // Headers
    fputcsv($output, ['area_id', 'area_name', 'area_name_bn', 'type']);
    
    // Get areas from database
    try {
        $db = getDB();
        $stmt = $db->query("
            SELECT id, name, name_bn, type 
            FROM areas 
            WHERE is_active = 1 
            ORDER BY type, name
        ");
        $areas = $stmt->fetchAll();
        
        foreach ($areas as $area) {
            fputcsv($output, [
                $area['id'],
                $area['name'],
                $area['name_bn'],
                $area['type']
            ]);
        }
        
    } catch (Exception $e) {
        fputcsv($output, ['Error', 'Could not load areas', '', '']);
    }
    
    fclose($output);
    exit;
    
} else {
    // Invalid format
    header('HTTP/1.1 400 Bad Request');
    echo "Invalid format requested";
    exit;
}
?>
