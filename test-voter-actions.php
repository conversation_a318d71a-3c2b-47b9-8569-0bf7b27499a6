<?php
/**
 * Test Voter Actions
 * Quick test for edit/delete functionality
 */

require_once 'config/config.php';

// Check if user is admin
if (!isLoggedIn() || !isAdmin()) {
    echo "<h2>Access Denied</h2>";
    echo "<p>Please login as admin first: <a href='auth/login.php'>Login</a></p>";
    exit;
}

echo "<h2>🧪 Voter Actions Test</h2>";

try {
    $db = getDB();
    
    // Get sample voters
    $stmt = $db->query("SELECT id, voter_id, name, name_bn FROM voters WHERE is_active = 1 LIMIT 3");
    $voters = $stmt->fetchAll();
    
    if (empty($voters)) {
        echo "<div style='background: #f8d7da; padding: 15px; border-radius: 5px;'>";
        echo "<h4>❌ No Voters Found</h4>";
        echo "<p>Please create some voters first.</p>";
        echo "<a href='admin/voters.php' class='btn btn-primary'>Go to Voter Management</a>";
        echo "</div>";
        exit;
    }
    
    echo "<div style='background: #d4edda; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
    echo "<h4>✅ Test Environment Ready</h4>";
    echo "<p>Found " . count($voters) . " voters for testing.</p>";
    echo "</div>";
    
    // Test buttons
    echo "<h3>🎯 Test Action Buttons:</h3>";
    echo "<div style='background: #f8f9fa; padding: 20px; border-radius: 5px;'>";
    
    foreach ($voters as $voter) {
        echo "<div style='border: 1px solid #dee2e6; padding: 15px; margin: 10px 0; border-radius: 5px;'>";
        echo "<h5>" . htmlspecialchars($voter['name_bn'] ?: $voter['name']) . "</h5>";
        echo "<p><strong>Voter ID:</strong> " . htmlspecialchars($voter['voter_id']) . "</p>";
        
        echo "<div class='btn-group'>";
        echo "<button type='button' class='btn btn-sm btn-primary' onclick='testEdit(" . $voter['id'] . ")'>";
        echo "<i class='fas fa-edit'></i> Edit Test";
        echo "</button>";
        
        echo "<button type='button' class='btn btn-sm btn-danger' onclick='testDelete(" . $voter['id'] . ")'>";
        echo "<i class='fas fa-trash'></i> Delete Test";
        echo "</button>";
        echo "</div>";
        
        echo "</div>";
    }
    
    echo "</div>";
    
    // Test API
    echo "<h3>🔗 API Test:</h3>";
    $test_voter_id = $voters[0]['id'];
    echo "<div style='background: #e7f3ff; padding: 15px; border-radius: 5px;'>";
    echo "<p><strong>Test API URL:</strong> <a href='admin/get-voter.php?id=$test_voter_id' target='_blank'>admin/get-voter.php?id=$test_voter_id</a></p>";
    echo "<p>This should return JSON data for voter.</p>";
    echo "</div>";
    
} catch (Exception $e) {
    echo "<div style='background: #f8d7da; padding: 15px; border-radius: 5px;'>";
    echo "<h4>❌ Error: " . $e->getMessage() . "</h4>";
    echo "</div>";
}

echo "<br><h3>🔗 Quick Links:</h3>";
echo "<ul>";
echo "<li><a href='admin/voters.php'>Voter Management (Real)</a></li>";
echo "<li><a href='admin/'>Admin Dashboard</a></li>";
echo "</ul>";
?>

<!DOCTYPE html>
<html>
<head>
    <title>Voter Actions Test</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body>

<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
<script>
function testEdit(id) {
    console.log('Testing edit for voter ID:', id);
    
    // Test API call
    fetch('admin/get-voter.php?id=' + id)
        .then(response => response.json())
        .then(data => {
            console.log('API Response:', data);
            if (data.success) {
                alert('✅ Edit API Test Successful!\\n\\nVoter: ' + data.voter.name + '\\nNID: ' + data.voter.nid);
            } else {
                alert('❌ Edit API Test Failed: ' + data.message);
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('❌ Edit API Test Error: ' + error.message);
        });
}

function testDelete(id) {
    console.log('Testing delete for voter ID:', id);
    
    if (confirm('🧪 This is a DELETE TEST\\n\\nDo you want to proceed?\\n\\n(This will actually delete the voter!)')) {
        alert('✅ Delete Test: Confirmation dialog works!\\n\\nActual deletion cancelled for safety.');
    } else {
        alert('✅ Delete Test: Cancellation works!');
    }
}

console.log('Voter Actions Test Page Loaded');
</script>

</body>
</html>
