<?php
/**
 * Admin Login Fix Utility
 * Complete solution for admin login issues
 */

// Display all errors
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h2>🔧 Admin Login Fix Utility</h2>";

// Database settings (update these to match your setup)
$db_host = 'localhost';
$db_name = 'ovs_db';
$db_user = 'root';
$db_pass = '';

try {
    $pdo = new PDO("mysql:host=$db_host;dbname=$db_name;charset=utf8mb4", $db_user, $db_pass);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "<div style='background: #d4edda; padding: 10px; border-radius: 5px; margin: 10px 0;'>";
    echo "✓ Database connected successfully!";
    echo "</div>";
    
    // Check if users table exists
    $stmt = $pdo->query("SHOW TABLES LIKE 'users'");
    if (!$stmt->fetch()) {
        echo "<div style='background: #f8d7da; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
        echo "<h4>❌ Users table not found!</h4>";
        echo "<p>Please import the database schema first:</p>";
        echo "<ol>";
        echo "<li>Go to phpMyAdmin</li>";
        echo "<li>Select 'ovs_db' database</li>";
        echo "<li>Import 'database/schema.sql'</li>";
        echo "<li>Then run this script again</li>";
        echo "</ol>";
        echo "</div>";
        exit;
    }
    
    echo "<div style='background: #d4edda; padding: 10px; border-radius: 5px; margin: 10px 0;'>";
    echo "✓ Users table exists!";
    echo "</div>";
    
    // Check existing users
    $stmt = $pdo->query("SELECT id, username, email, role, is_active FROM users ORDER BY id");
    $users = $stmt->fetchAll();
    
    echo "<h3>📋 Current Users in Database:</h3>";
    if (empty($users)) {
        echo "<p style='color: orange;'>⚠️ No users found in database!</p>";
    } else {
        echo "<table border='1' style='border-collapse: collapse; width: 100%; margin: 10px 0;'>";
        echo "<tr style='background: #f8f9fa;'>";
        echo "<th style='padding: 8px;'>ID</th>";
        echo "<th style='padding: 8px;'>Username</th>";
        echo "<th style='padding: 8px;'>Email</th>";
        echo "<th style='padding: 8px;'>Role</th>";
        echo "<th style='padding: 8px;'>Active</th>";
        echo "</tr>";
        
        foreach ($users as $user) {
            $bg_color = $user['role'] === 'admin' ? '#e7f3ff' : '#ffffff';
            echo "<tr style='background: $bg_color;'>";
            echo "<td style='padding: 8px;'>{$user['id']}</td>";
            echo "<td style='padding: 8px;'>{$user['username']}</td>";
            echo "<td style='padding: 8px;'>{$user['email']}</td>";
            echo "<td style='padding: 8px;'><strong>{$user['role']}</strong></td>";
            echo "<td style='padding: 8px;'>" . ($user['is_active'] ? '✅' : '❌') . "</td>";
            echo "</tr>";
        }
        echo "</table>";
    }
    
    // Handle form submission
    if (isset($_POST['action'])) {
        $action = $_POST['action'];
        
        if ($action === 'create_admin') {
            // Create new admin user
            $username = 'admin';
            $email = '<EMAIL>';
            $password = 'admin123';
            $password_hash = password_hash($password, PASSWORD_DEFAULT);
            
            try {
                // Check if admin already exists
                $stmt = $pdo->prepare("SELECT id FROM users WHERE email = ?");
                $stmt->execute([$email]);
                
                if ($stmt->fetch()) {
                    // Update existing admin
                    $stmt = $pdo->prepare("
                        UPDATE users 
                        SET username = ?, password = ?, role = 'admin', is_active = 1, email_verified = 1 
                        WHERE email = ?
                    ");
                    $stmt->execute([$username, $password_hash, $email]);
                    
                    echo "<div style='background: #d4edda; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
                    echo "<h4>✅ Admin User Updated!</h4>";
                    echo "<p><strong>Email:</strong> $email</p>";
                    echo "<p><strong>Password:</strong> $password</p>";
                    echo "</div>";
                } else {
                    // Create new admin
                    $stmt = $pdo->prepare("
                        INSERT INTO users (username, email, password, first_name, last_name, role, is_active, email_verified) 
                        VALUES (?, ?, ?, ?, ?, ?, ?, ?)
                    ");
                    $stmt->execute([
                        $username, $email, $password_hash, 'System', 'Administrator', 'admin', 1, 1
                    ]);
                    
                    echo "<div style='background: #d4edda; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
                    echo "<h4>✅ New Admin User Created!</h4>";
                    echo "<p><strong>Email:</strong> $email</p>";
                    echo "<p><strong>Password:</strong> $password</p>";
                    echo "</div>";
                }
            } catch (Exception $e) {
                echo "<div style='background: #f8d7da; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
                echo "<h4>❌ Error creating admin: " . $e->getMessage() . "</h4>";
                echo "</div>";
            }
        }
        
        if ($action === 'test_login') {
            $test_email = $_POST['test_email'];
            $test_password = $_POST['test_password'];
            
            $stmt = $pdo->prepare("SELECT id, username, password, role, is_active FROM users WHERE email = ?");
            $stmt->execute([$test_email]);
            $user = $stmt->fetch();
            
            if (!$user) {
                echo "<div style='background: #f8d7da; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
                echo "<h4>❌ User not found with email: $test_email</h4>";
                echo "</div>";
            } elseif (!$user['is_active']) {
                echo "<div style='background: #f8d7da; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
                echo "<h4>❌ User account is deactivated</h4>";
                echo "</div>";
            } elseif (!password_verify($test_password, $user['password'])) {
                echo "<div style='background: #f8d7da; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
                echo "<h4>❌ Password verification failed</h4>";
                echo "<p>Stored hash: " . substr($user['password'], 0, 50) . "...</p>";
                echo "<p>Password length: " . strlen($test_password) . "</p>";
                echo "</div>";
            } else {
                echo "<div style='background: #d4edda; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
                echo "<h4>✅ Login Test Successful!</h4>";
                echo "<p><strong>User ID:</strong> {$user['id']}</p>";
                echo "<p><strong>Username:</strong> {$user['username']}</p>";
                echo "<p><strong>Role:</strong> {$user['role']}</p>";
                echo "<p>You can now login to the system!</p>";
                echo "</div>";
            }
        }
        
        // Refresh user list
        $stmt = $pdo->query("SELECT id, username, email, role, is_active FROM users ORDER BY id");
        $users = $stmt->fetchAll();
    }
    
    // Show action buttons
    echo "<h3>🛠️ Fix Options:</h3>";
    
    // Create/Update Admin Button
    echo "<form method='POST' style='display: inline-block; margin: 10px;'>";
    echo "<input type='hidden' name='action' value='create_admin'>";
    echo "<button type='submit' style='background: #28a745; color: white; padding: 15px 25px; border: none; border-radius: 5px; font-size: 16px; cursor: pointer;'>";
    echo "🔧 Create/Fix Admin User";
    echo "</button>";
    echo "</form>";
    
    echo "<br><br>";
    
    // Test Login Form
    echo "<h3>🧪 Test Login:</h3>";
    echo "<form method='POST' style='background: #f8f9fa; padding: 20px; border-radius: 5px; max-width: 400px;'>";
    echo "<input type='hidden' name='action' value='test_login'>";
    
    echo "<div style='margin-bottom: 15px;'>";
    echo "<label style='display: block; margin-bottom: 5px;'><strong>Email:</strong></label>";
    echo "<input type='email' name='test_email' value='<EMAIL>' style='width: 100%; padding: 8px; border: 1px solid #ddd; border-radius: 3px;'>";
    echo "</div>";
    
    echo "<div style='margin-bottom: 15px;'>";
    echo "<label style='display: block; margin-bottom: 5px;'><strong>Password:</strong></label>";
    echo "<input type='password' name='test_password' value='admin123' style='width: 100%; padding: 8px; border: 1px solid #ddd; border-radius: 3px;'>";
    echo "</div>";
    
    echo "<button type='submit' style='background: #007bff; color: white; padding: 10px 20px; border: none; border-radius: 3px; cursor: pointer;'>";
    echo "🧪 Test Login";
    echo "</button>";
    echo "</form>";
    
    // Quick password hashes for reference
    echo "<h3>📝 Password Hash Reference:</h3>";
    echo "<div style='background: #e9ecef; padding: 15px; border-radius: 5px; font-family: monospace; font-size: 12px;'>";
    echo "<strong>admin123:</strong> " . password_hash('admin123', PASSWORD_DEFAULT) . "<br>";
    echo "<strong>password:</strong> " . password_hash('password', PASSWORD_DEFAULT) . "<br>";
    echo "<strong>123456:</strong> " . password_hash('123456', PASSWORD_DEFAULT);
    echo "</div>";
    
} catch (PDOException $e) {
    echo "<div style='background: #f8d7da; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
    echo "<h4>❌ Database Connection Failed!</h4>";
    echo "<p><strong>Error:</strong> " . $e->getMessage() . "</p>";
    echo "<h5>Troubleshooting Steps:</h5>";
    echo "<ol>";
    echo "<li>Make sure XAMPP MySQL is running</li>";
    echo "<li>Check if database 'ovs_db' exists in phpMyAdmin</li>";
    echo "<li>Verify database credentials in this file (lines 10-13)</li>";
    echo "<li>Import database/schema.sql if not done already</li>";
    echo "</ol>";
    echo "</div>";
}

echo "<br><h3>🔗 Quick Links:</h3>";
echo "<ul>";
echo "<li><a href='auth/login.php' target='_blank'>🔐 Login Page</a></li>";
echo "<li><a href='admin/' target='_blank'>👨‍💼 Admin Dashboard</a></li>";
echo "<li><a href='index.php' target='_blank'>🏠 Main Site</a></li>";
echo "<li><a href='http://localhost/phpmyadmin' target='_blank'>🗄️ phpMyAdmin</a></li>";
echo "</ul>";

echo "<br><div style='background: #fff3cd; padding: 15px; border-radius: 5px;'>";
echo "<h4>⚠️ Security Note:</h4>";
echo "<p>Please delete this file (<code>fix-admin-login.php</code>) after fixing the admin login for security reasons.</p>";
echo "</div>";
?>
