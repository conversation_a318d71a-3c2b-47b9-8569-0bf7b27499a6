<?php
/**
 * Scrolling Sidebar Test Page
 * স্ক্রোলিং সাইডবার টেস্ট পেজ
 */

require_once 'config/config.php';

$page_title = '📜 স্ক্রোলিং সাইডবার টেস্ট - Scrolling Sidebar Test';
$page_subtitle = 'সাইডবার স্ক্রোলিং ফিচার পরীক্ষা করুন';
$show_top_nav = true;

// Start output buffering for content
ob_start();
?>

<div class="container-fluid py-4">
    <!-- Header Section -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card border-0" style="background: linear-gradient(135deg, #ff6b6b 0%, #feca57 100%); color: white;">
                <div class="card-body text-center py-5">
                    <h1 class="display-4 mb-3" style="text-shadow: 2px 2px 4px rgba(0,0,0,0.3);">
                        📜 স্ক্রোলিং সাইডবার
                    </h1>
                    <p class="lead mb-4" style="font-size: 1.3rem;">
                        কালারফুল কাস্টম স্ক্রোলবার সহ smooth scrolling experience
                    </p>
                    <div class="d-flex gap-3 justify-content-center flex-wrap">
                        <span class="badge px-4 py-2" style="background: linear-gradient(45deg, #48dbfb, #0abde3); font-size: 1rem;">
                            <i class="fas fa-scroll"></i> Custom Scrollbar
                        </span>
                        <span class="badge px-4 py-2" style="background: linear-gradient(45deg, #667eea, #764ba2); font-size: 1rem;">
                            <i class="fas fa-palette"></i> Gradient Colors
                        </span>
                        <span class="badge px-4 py-2" style="background: linear-gradient(45deg, #ff9ff3, #f368e0); font-size: 1rem;">
                            <i class="fas fa-magic"></i> Smooth Animation
                        </span>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Scrollbar Features -->
    <div class="row mb-4">
        <div class="col-md-4 mb-3">
            <div class="card h-100 border-0" style="background: linear-gradient(135deg, #48dbfb, #0abde3); color: white;">
                <div class="card-body text-center">
                    <div class="mb-3">
                        <i class="fas fa-scroll fa-4x" style="text-shadow: 2px 2px 4px rgba(0,0,0,0.3);"></i>
                    </div>
                    <h5 class="card-title">Custom Scrollbar</h5>
                    <p class="card-text">
                        Gradient colored scrollbar যা hover এ আরো উজ্জ্বল হয়
                    </p>
                    <ul class="list-unstyled text-start">
                        <li><i class="fas fa-check"></i> 8px width</li>
                        <li><i class="fas fa-check"></i> Rounded corners</li>
                        <li><i class="fas fa-check"></i> Gradient colors</li>
                        <li><i class="fas fa-check"></i> Hover effects</li>
                    </ul>
                </div>
            </div>
        </div>
        
        <div class="col-md-4 mb-3">
            <div class="card h-100 border-0" style="background: linear-gradient(135deg, #667eea, #764ba2); color: white;">
                <div class="card-body text-center">
                    <div class="mb-3">
                        <i class="fas fa-arrows-alt-v fa-4x" style="text-shadow: 2px 2px 4px rgba(0,0,0,0.3);"></i>
                    </div>
                    <h5 class="card-title">Smooth Scrolling</h5>
                    <p class="card-text">
                        Buttery smooth scrolling experience সব ব্রাউজারে
                    </p>
                    <ul class="list-unstyled text-start">
                        <li><i class="fas fa-check"></i> Webkit support</li>
                        <li><i class="fas fa-check"></i> Firefox support</li>
                        <li><i class="fas fa-check"></i> Mobile friendly</li>
                        <li><i class="fas fa-check"></i> Touch scrolling</li>
                    </ul>
                </div>
            </div>
        </div>
        
        <div class="col-md-4 mb-3">
            <div class="card h-100 border-0" style="background: linear-gradient(135deg, #ff9ff3, #f368e0); color: white;">
                <div class="card-body text-center">
                    <div class="mb-3">
                        <i class="fas fa-list fa-4x" style="text-shadow: 2px 2px 4px rgba(0,0,0,0.3);"></i>
                    </div>
                    <h5 class="card-title">Extended Menu</h5>
                    <p class="card-text">
                        অনেক menu items যা scrolling প্রয়োজন করে
                    </p>
                    <ul class="list-unstyled text-start">
                        <li><i class="fas fa-check"></i> 20+ menu items</li>
                        <li><i class="fas fa-check"></i> Categorized sections</li>
                        <li><i class="fas fa-check"></i> Admin tools</li>
                        <li><i class="fas fa-check"></i> Quick actions</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>

    <!-- Scrollbar CSS Info -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card border-0" style="background: rgba(255, 255, 255, 0.9); backdrop-filter: blur(10px);">
                <div class="card-header border-0" style="background: linear-gradient(135deg, #4ecdc4, #44a08d); color: white;">
                    <h5 class="mb-0">
                        <i class="fas fa-code"></i> স্ক্রোলবার CSS কোড
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <h6 style="color: #667eea;">🎨 Webkit Scrollbar:</h6>
                            <pre style="background: #f8f9fa; padding: 1rem; border-radius: 8px; font-size: 0.85rem;"><code>/* Scrollbar width */
::-webkit-scrollbar {
    width: 8px;
}

/* Track */
::-webkit-scrollbar-track {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 10px;
}

/* Thumb */
::-webkit-scrollbar-thumb {
    background: linear-gradient(180deg, 
        #ff6b6b, #feca57, #48dbfb);
    border-radius: 10px;
    border: 2px solid rgba(255, 255, 255, 0.1);
}</code></pre>
                        </div>
                        <div class="col-md-6">
                            <h6 style="color: #ff6b6b;">🦊 Firefox Scrollbar:</h6>
                            <pre style="background: #f8f9fa; padding: 1rem; border-radius: 8px; font-size: 0.85rem;"><code>/* Firefox scrollbar */
.sidebar-nav {
    scrollbar-width: thin;
    scrollbar-color: rgba(255, 107, 107, 0.7) 
                     rgba(255, 255, 255, 0.1);
}

/* Hover effects */
::-webkit-scrollbar-thumb:hover {
    background: linear-gradient(180deg, 
        #feca57, #ff6b6b, #48dbfb);
    border: 2px solid rgba(255, 255, 255, 0.3);
}</code></pre>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Test Instructions -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card border-0" style="background: rgba(255, 255, 255, 0.9); backdrop-filter: blur(10px);">
                <div class="card-header border-0" style="background: linear-gradient(135deg, #ff6b6b, #feca57); color: white;">
                    <h5 class="mb-0">
                        <i class="fas fa-clipboard-list"></i> স্ক্রোলিং টেস্ট করুন
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <h6 style="color: #667eea;">📋 টেস্ট স্টেপস:</h6>
                            <ol>
                                <li class="mb-2">
                                    <strong>সাইডবার দেখুন:</strong> বাম পাশের সাইডবারে অনেক menu items আছে
                                </li>
                                <li class="mb-2">
                                    <strong>স্ক্রোল করুন:</strong> Mouse wheel বা scrollbar দিয়ে scroll করুন
                                </li>
                                <li class="mb-2">
                                    <strong>Scrollbar দেখুন:</strong> Colorful gradient scrollbar লক্ষ্য করুন
                                </li>
                                <li class="mb-2">
                                    <strong>Hover করুন:</strong> Scrollbar এ hover করে color change দেখুন
                                </li>
                                <li class="mb-2">
                                    <strong>Mobile টেস্ট:</strong> Mobile device এ touch scrolling টেস্ট করুন
                                </li>
                            </ol>
                        </div>
                        <div class="col-md-6">
                            <h6 style="color: #ff6b6b;">✨ Expected Results:</h6>
                            <ul class="list-unstyled">
                                <li class="mb-2">
                                    <i class="fas fa-check text-success"></i>
                                    <strong>Smooth scrolling</strong> - কোন lag নেই
                                </li>
                                <li class="mb-2">
                                    <i class="fas fa-check text-success"></i>
                                    <strong>Colorful scrollbar</strong> - Gradient colors দেখা যাচ্ছে
                                </li>
                                <li class="mb-2">
                                    <i class="fas fa-check text-success"></i>
                                    <strong>Hover effects</strong> - Color change on hover
                                </li>
                                <li class="mb-2">
                                    <i class="fas fa-check text-success"></i>
                                    <strong>Responsive</strong> - Mobile এ touch scrolling
                                </li>
                                <li class="mb-2">
                                    <i class="fas fa-check text-success"></i>
                                    <strong>All menu visible</strong> - সব menu items accessible
                                </li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Interactive Test -->
    <div class="row mb-4">
        <div class="col-md-8">
            <div class="card border-0" style="background: rgba(255, 255, 255, 0.9); backdrop-filter: blur(10px);">
                <div class="card-header border-0" style="background: linear-gradient(135deg, #48dbfb, #0abde3); color: white;">
                    <h5 class="mb-0">
                        <i class="fas fa-play"></i> ইন্টারঅ্যাক্টিভ টেস্ট
                    </h5>
                </div>
                <div class="card-body">
                    <p>
                        এই পেজে সাইডবার স্ক্রোলিং টেস্ট করার জন্য অনেক menu items যোগ করা হয়েছে। 
                        আপনি দেখতে পাবেন যে সাইডবারে একটি কালারফুল custom scrollbar আছে।
                    </p>
                    
                    <h6 style="color: #667eea;">🎯 Menu Categories:</h6>
                    <div class="row">
                        <div class="col-md-6">
                            <ul>
                                <li>মূল নেভিগেশন (5 items)</li>
                                <li>অ্যাডমিন প্যানেল (8 items)</li>
                                <li>দ্রুত অ্যাকশন (10 items)</li>
                            </ul>
                        </div>
                        <div class="col-md-6">
                            <ul>
                                <li>অ্যাডমিন টুলস (7 items)</li>
                                <li>সেটিংস ও সাপোর্ট</li>
                                <li>মোট 30+ menu items</li>
                            </ul>
                        </div>
                    </div>
                    
                    <div class="alert alert-info mt-3" style="background: linear-gradient(135deg, #48dbfb, #0abde3); color: white; border: none;">
                        <strong>টিপ:</strong> সাইডবারের scrollbar এ hover করে color animation দেখুন!
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-md-4">
            <div class="card border-0" style="background: rgba(255, 255, 255, 0.9); backdrop-filter: blur(10px);">
                <div class="card-header border-0" style="background: linear-gradient(135deg, #ff9ff3, #f368e0); color: white;">
                    <h5 class="mb-0">
                        <i class="fas fa-info-circle"></i> স্ক্রোলবার তথ্য
                    </h5>
                </div>
                <div class="card-body">
                    <div class="mb-3">
                        <strong>Width:</strong> 8px<br>
                        <strong>Colors:</strong> Gradient<br>
                        <strong>Border:</strong> 2px transparent<br>
                        <strong>Radius:</strong> 10px
                    </div>
                    
                    <div class="mb-3">
                        <strong>Gradient Colors:</strong><br>
                        <small>
                            • #ff6b6b (Red)<br>
                            • #feca57 (Yellow)<br>
                            • #48dbfb (Blue)
                        </small>
                    </div>
                    
                    <div class="mb-3">
                        <strong>Browser Support:</strong><br>
                        <small>
                            ✅ Chrome/Safari (Webkit)<br>
                            ✅ Firefox (scrollbar-color)<br>
                            ✅ Edge (Webkit)<br>
                            ⚠️ IE (Basic scrollbar)
                        </small>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Success Message -->
    <div class="row">
        <div class="col-12">
            <div class="card border-0" style="background: linear-gradient(135deg, #4ecdc4, #44a08d); color: white;">
                <div class="card-body text-center py-4">
                    <h3 class="mb-3">
                        🎉 স্ক্রোলিং সাইডবার সফলভাবে কাজ করছে!
                    </h3>
                    <p class="mb-0">
                        Custom scrollbar, smooth scrolling এবং extended menu - সব কিছু perfect!
                    </p>
                </div>
            </div>
        </div>
    </div>
</div>

<?php
$content = ob_get_clean();

// Include the sidebar layout
include 'includes/header-with-sidebar.php';
?>
