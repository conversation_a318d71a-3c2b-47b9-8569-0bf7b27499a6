<?php
/**
 * Test Template Generator
 * Create minimal working CSV for testing
 */

require_once '../config/config.php';

// Check if user is admin
if (!isLoggedIn() || !isAdmin()) {
    redirect(APP_URL . '/auth/login.php');
}

echo "<h2>🧪 Test Template Generator</h2>";

try {
    $db = getDB();
    
    // Check database structure
    echo "<h3>📊 Database Check:</h3>";
    
    // Check voters table structure
    $stmt = $db->query("DESCRIBE voters");
    $voter_columns = $stmt->fetchAll();
    
    echo "<h4>Voters Table Columns:</h4>";
    echo "<ul>";
    foreach ($voter_columns as $col) {
        echo "<li><strong>" . $col['Field'] . "</strong> - " . $col['Type'] . " (" . $col['Null'] . ")</li>";
    }
    echo "</ul>";
    
    // Check areas
    $stmt = $db->query("SELECT COUNT(*) as count, type FROM areas WHERE is_active = 1 GROUP BY type");
    $area_counts = $stmt->fetchAll();
    
    echo "<h4>Available Areas:</h4>";
    echo "<ul>";
    foreach ($area_counts as $area) {
        echo "<li><strong>" . ucfirst($area['type']) . ":</strong> " . $area['count'] . "</li>";
    }
    echo "</ul>";
    
    // Get first available ward
    $stmt = $db->query("SELECT id, name, name_bn FROM areas WHERE type = 'ward' AND is_active = 1 LIMIT 1");
    $ward = $stmt->fetch();
    
    if (!$ward) {
        echo "<div style='background: #f8d7da; padding: 15px; border-radius: 5px;'>";
        echo "<h4>❌ No Wards Found!</h4>";
        echo "<p>You need to create at least one ward before uploading voters.</p>";
        echo "<a href='areas.php' class='btn btn-primary'>Create Areas First</a>";
        echo "</div>";
        exit;
    }
    
    echo "<div style='background: #d4edda; padding: 15px; border-radius: 5px;'>";
    echo "<h4>✅ Found Ward for Testing:</h4>";
    echo "<p><strong>ID:</strong> " . $ward['id'] . "</p>";
    echo "<p><strong>Name:</strong> " . htmlspecialchars($ward['name']) . "</p>";
    echo "<p><strong>Name (Bengali):</strong> " . htmlspecialchars($ward['name_bn']) . "</p>";
    echo "</div>";
    
    if (isset($_GET['download'])) {
        // Generate simple test CSV
        header('Content-Type: text/csv; charset=utf-8');
        header('Content-Disposition: attachment; filename="simple-test-template.csv"');
        header('Cache-Control: no-cache, must-revalidate');
        
        $output = fopen('php://output', 'w');
        fprintf($output, chr(0xEF).chr(0xBB).chr(0xBF)); // BOM
        
        // Simple headers
        $headers = ['voter_id', 'name', 'name_bn', 'nid', 'mobile', 'ward_id', 'address'];
        fputcsv($output, $headers);
        
        // One simple test row with empty voter_id (will be auto-generated)
        $test_row = [
            '', // Empty voter_id - will be auto-generated during upload
            'Test Voter',
            'টেস্ট ভোটার',
            '1234567890123', // Valid 13-digit NID
            '01712345678',   // Valid 11-digit mobile
            $ward['id'],
            'Test Address'
        ];
        fputcsv($output, $test_row);
        
        fclose($output);
        exit;
    }
    
    if (isset($_GET['download_full'])) {
        // Generate full hierarchical test CSV
        header('Content-Type: text/csv; charset=utf-8');
        header('Content-Disposition: attachment; filename="full-test-template.csv"');
        header('Cache-Control: no-cache, must-revalidate');
        
        $output = fopen('php://output', 'w');
        fprintf($output, chr(0xEF).chr(0xBB).chr(0xBF)); // BOM
        
        // Full headers
        $headers = [
            'voter_id', 'name', 'name_bn', 'father_name', 'mother_name', 
            'nid', 'mobile', 'email', 'date_of_birth', 'gender',
            'division_name', 'district_name', 'upazila_name', 'union_name', 
            'village_name', 'ward_name', 'ward_id', 'address'
        ];
        fputcsv($output, $headers);
        
        // One test row with all fields (empty voter_id for auto-generation)
        $test_row = [
            '', // Empty voter_id - will be auto-generated during upload
            'Full Test Voter',
            'সম্পূর্ণ টেস্ট ভোটার',
            'Test Father',
            'Test Mother',
            '9876543210987',
            '01987654321',
            '<EMAIL>',
            '1990-01-01',
            'male',
            'Test Division',
            'Test District',
            'Test Upazila',
            'Test Union',
            'Test Village',
            $ward['name_bn'] ?: $ward['name'],
            $ward['id'],
            'Full Test Address'
        ];
        fputcsv($output, $test_row);
        
        fclose($output);
        exit;
    }
    
} catch (Exception $e) {
    echo "<div style='background: #f8d7da; padding: 15px; border-radius: 5px;'>";
    echo "<h4>❌ Database Error:</h4>";
    echo "<p>" . $e->getMessage() . "</p>";
    echo "</div>";
}
?>

<!DOCTYPE html>
<html>
<head>
    <title>Test Template Generator</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
</head>
<body>

<div class="container mt-4">
    <div class="row">
        <div class="col-md-6">
            <div class="card">
                <div class="card-header">
                    <h5>🧪 Simple Test Template</h5>
                </div>
                <div class="card-body">
                    <p>Minimal CSV with only required fields for testing.</p>
                    <p><strong>Fields:</strong> voter_id, name, name_bn, nid, mobile, ward_id, address</p>
                    <a href="?download=1" class="btn btn-primary">
                        <i class="fas fa-download"></i> Download Simple Template
                    </a>
                </div>
            </div>
        </div>
        
        <div class="col-md-6">
            <div class="card">
                <div class="card-header">
                    <h5>📋 Full Test Template</h5>
                </div>
                <div class="card-body">
                    <p>Complete CSV with all hierarchical fields.</p>
                    <p><strong>Fields:</strong> All 18 columns including hierarchy</p>
                    <a href="?download_full=1" class="btn btn-success">
                        <i class="fas fa-download"></i> Download Full Template
                    </a>
                </div>
            </div>
        </div>
    </div>
    
    <div class="row mt-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5>🔧 Quick Actions</h5>
                </div>
                <div class="card-body">
                    <a href="debug-upload.php" class="btn btn-warning me-2">
                        <i class="fas fa-bug"></i> Debug Upload
                    </a>
                    <a href="voters.php" class="btn btn-secondary me-2">
                        <i class="fas fa-users"></i> Back to Voters
                    </a>
                    <a href="areas.php" class="btn btn-info">
                        <i class="fas fa-map"></i> Manage Areas
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>

</body>
</html>
