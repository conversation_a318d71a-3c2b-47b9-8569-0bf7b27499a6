<?php
/**
 * Debug CSV Upload
 * Test and debug voter upload functionality
 */

require_once '../config/config.php';
require_once '../includes/hierarchical-upload.php';

// Check if user is admin
if (!isLoggedIn() || !isAdmin()) {
    redirect(APP_URL . '/auth/login.php');
}

echo "<h2>🔍 CSV Upload Debug</h2>";

if (isset($_POST['debug_upload']) && isset($_FILES['test_file'])) {
    echo "<h3>📋 Upload Test Results:</h3>";
    
    $result = uploadVotersHierarchical($_FILES['test_file']);
    
    echo "<div style='background: " . ($result['success'] ? '#d4edda' : '#f8d7da') . "; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
    echo "<h4>" . ($result['success'] ? '✅' : '❌') . " " . $result['message'] . "</h4>";
    
    if (isset($result['details'])) {
        echo "<ul>";
        echo "<li><strong>Success:</strong> " . $result['details']['success'] . "</li>";
        echo "<li><strong>Errors:</strong> " . $result['details']['errors'] . "</li>";
        echo "<li><strong>Format:</strong> " . $result['details']['format_detected'] . "</li>";
        echo "</ul>";
        
        if (!empty($result['details']['error_list'])) {
            echo "<h5>Error Details:</h5>";
            echo "<ol>";
            foreach ($result['details']['error_list'] as $error) {
                echo "<li style='color: #721c24;'>" . htmlspecialchars($error) . "</li>";
            }
            echo "</ol>";
        }
    }
    echo "</div>";
}

// Test CSV content analysis
if (isset($_POST['analyze_csv']) && isset($_FILES['analyze_file'])) {
    echo "<h3>📊 CSV Content Analysis:</h3>";
    
    $file = $_FILES['analyze_file'];
    if ($file['error'] === UPLOAD_ERR_OK) {
        $handle = fopen($file['tmp_name'], 'r');
        
        echo "<div style='background: #e7f3ff; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
        
        // Read headers
        $headers = fgetcsv($handle);
        echo "<h5>Headers Found:</h5>";
        echo "<ol>";
        foreach ($headers as $index => $header) {
            echo "<li><strong>Column $index:</strong> " . htmlspecialchars($header) . "</li>";
        }
        echo "</ol>";
        
        // Read first few rows
        echo "<h5>Sample Data (First 3 rows):</h5>";
        echo "<table border='1' style='border-collapse: collapse; width: 100%; font-size: 12px;'>";
        echo "<tr style='background: #f8f9fa;'>";
        foreach ($headers as $header) {
            echo "<th style='padding: 5px;'>" . htmlspecialchars($header) . "</th>";
        }
        echo "</tr>";
        
        $row_count = 0;
        while (($row = fgetcsv($handle)) !== false && $row_count < 3) {
            echo "<tr>";
            foreach ($row as $cell) {
                echo "<td style='padding: 5px; border: 1px solid #dee2e6;'>" . htmlspecialchars($cell) . "</td>";
            }
            echo "</tr>";
            $row_count++;
        }
        echo "</table>";
        
        fclose($handle);
        echo "</div>";
    }
}

// Check areas in database
try {
    $db = getDB();
    
    echo "<h3>🏛️ Available Areas Check:</h3>";
    
    $stmt = $db->query("
        SELECT type, COUNT(*) as count 
        FROM areas 
        WHERE is_active = 1 
        GROUP BY type 
        ORDER BY 
            CASE type 
                WHEN 'division' THEN 1 
                WHEN 'district' THEN 2 
                WHEN 'upazila' THEN 3 
                WHEN 'municipality' THEN 4
                WHEN 'union' THEN 4
                WHEN 'village' THEN 5
                WHEN 'city' THEN 5
                WHEN 'ward' THEN 6 
            END
    ");
    $area_stats = $stmt->fetchAll();
    
    echo "<div style='background: #f8f9fa; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
    echo "<table border='1' style='border-collapse: collapse;'>";
    echo "<tr><th style='padding: 10px;'>Type</th><th style='padding: 10px;'>Count</th></tr>";
    foreach ($area_stats as $stat) {
        echo "<tr>";
        echo "<td style='padding: 10px;'>" . ucfirst($stat['type']) . "</td>";
        echo "<td style='padding: 10px;'>" . $stat['count'] . "</td>";
        echo "</tr>";
    }
    echo "</table>";
    echo "</div>";
    
    // Show sample ward IDs
    echo "<h4>Sample Ward IDs:</h4>";
    $stmt = $db->query("SELECT id, name, name_bn FROM areas WHERE type = 'ward' AND is_active = 1 LIMIT 10");
    $wards = $stmt->fetchAll();
    
    echo "<div style='background: #fff3cd; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
    echo "<table border='1' style='border-collapse: collapse;'>";
    echo "<tr><th style='padding: 5px;'>Ward ID</th><th style='padding: 5px;'>Name</th><th style='padding: 5px;'>Name (Bengali)</th></tr>";
    foreach ($wards as $ward) {
        echo "<tr>";
        echo "<td style='padding: 5px;'>" . $ward['id'] . "</td>";
        echo "<td style='padding: 5px;'>" . htmlspecialchars($ward['name']) . "</td>";
        echo "<td style='padding: 5px;'>" . htmlspecialchars($ward['name_bn']) . "</td>";
        echo "</tr>";
    }
    echo "</table>";
    echo "</div>";
    
} catch (Exception $e) {
    echo "<div style='background: #f8d7da; padding: 15px; border-radius: 5px;'>";
    echo "<h4>❌ Database Error: " . $e->getMessage() . "</h4>";
    echo "</div>";
}
?>

<!DOCTYPE html>
<html>
<head>
    <title>CSV Upload Debug</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
</head>
<body>

<div class="container mt-4">
    <div class="row">
        <div class="col-md-6">
            <div class="card">
                <div class="card-header">
                    <h5>🧪 Test Upload</h5>
                </div>
                <div class="card-body">
                    <form method="POST" enctype="multipart/form-data">
                        <div class="mb-3">
                            <label for="test_file" class="form-label">Select CSV File</label>
                            <input type="file" class="form-control" name="test_file" accept=".csv" required>
                        </div>
                        <button type="submit" name="debug_upload" class="btn btn-primary">
                            <i class="fas fa-bug"></i> Debug Upload
                        </button>
                    </form>
                </div>
            </div>
        </div>
        
        <div class="col-md-6">
            <div class="card">
                <div class="card-header">
                    <h5>📊 Analyze CSV</h5>
                </div>
                <div class="card-body">
                    <form method="POST" enctype="multipart/form-data">
                        <div class="mb-3">
                            <label for="analyze_file" class="form-label">Select CSV File</label>
                            <input type="file" class="form-control" name="analyze_file" accept=".csv" required>
                        </div>
                        <button type="submit" name="analyze_csv" class="btn btn-info">
                            <i class="fas fa-search"></i> Analyze Content
                        </button>
                    </form>
                </div>
            </div>
        </div>
    </div>
    
    <div class="row mt-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5>📥 Quick Actions</h5>
                </div>
                <div class="card-body">
                    <a href="download-template.php?format=csv" class="btn btn-success me-2">
                        <i class="fas fa-download"></i> Download Template
                    </a>
                    <a href="voters.php" class="btn btn-secondary me-2">
                        <i class="fas fa-users"></i> Back to Voters
                    </a>
                    <a href="../setup-voter-photos.php" class="btn btn-warning">
                        <i class="fas fa-database"></i> Setup Database
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>

<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/js/all.min.js"></script>

</body>
</html>
