<?php
/**
 * Admin Password Reset Utility
 * Use this file to reset admin password
 */

// Display all errors
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h2>Admin Password Reset Utility</h2>";

// Database settings (update these to match your setup)
$db_host = 'localhost';
$db_name = 'ovs_db';
$db_user = 'root';
$db_pass = '';

try {
    $pdo = new PDO("mysql:host=$db_host;dbname=$db_name;charset=utf8mb4", $db_user, $db_pass);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "<span style='color: green;'>✓ Database connected successfully!</span><br><br>";
    
    // Check if admin user exists
    $stmt = $pdo->prepare("SELECT id, username, email FROM users WHERE role = 'admin' OR email = '<EMAIL>'");
    $stmt->execute();
    $admins = $stmt->fetchAll();
    
    if (empty($admins)) {
        echo "<h3>Creating New Admin User</h3>";
        
        // Create new admin user
        $password_hash = password_hash('admin123', PASSWORD_DEFAULT);
        
        $stmt = $pdo->prepare("
            INSERT INTO users (username, email, password, first_name, last_name, role, is_active, email_verified) 
            VALUES (?, ?, ?, ?, ?, ?, ?, ?)
        ");
        
        $stmt->execute([
            'admin',
            '<EMAIL>',
            $password_hash,
            'System',
            'Administrator',
            'admin',
            1,
            1
        ]);
        
        echo "<div style='background: #d4edda; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
        echo "<h4 style='color: #155724;'>✓ New Admin User Created!</h4>";
        echo "<p><strong>Email:</strong> <EMAIL></p>";
        echo "<p><strong>Password:</strong> admin123</p>";
        echo "</div>";
        
    } else {
        echo "<h3>Existing Admin Users Found</h3>";
        
        foreach ($admins as $admin) {
            echo "<div style='background: #f8f9fa; padding: 10px; margin: 5px 0; border-radius: 5px;'>";
            echo "<strong>ID:</strong> {$admin['id']} | ";
            echo "<strong>Username:</strong> {$admin['username']} | ";
            echo "<strong>Email:</strong> {$admin['email']}";
            echo "</div>";
        }
        
        echo "<h4>Reset <NAME_EMAIL></h4>";
        
        // Reset <NAME_EMAIL>
        $password_hash = password_hash('admin123', PASSWORD_DEFAULT);
        
        $stmt = $pdo->prepare("UPDATE users SET password = ? WHERE email = '<EMAIL>'");
        $result = $stmt->execute([$password_hash]);
        
        if ($result) {
            echo "<div style='background: #d4edda; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
            echo "<h4 style='color: #155724;'>✓ Password Reset Successful!</h4>";
            echo "<p><strong>Email:</strong> <EMAIL></p>";
            echo "<p><strong>New Password:</strong> admin123</p>";
            echo "</div>";
        } else {
            echo "<div style='background: #f8d7da; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
            echo "<h4 style='color: #721c24;'>✗ Password Reset Failed!</h4>";
            echo "</div>";
        }
    }
    
    // Test login
    echo "<h3>Test Login</h3>";
    echo "<form method='POST' style='background: #f8f9fa; padding: 20px; border-radius: 5px;'>";
    echo "<div style='margin-bottom: 10px;'>";
    echo "<label>Email:</label><br>";
    echo "<input type='email' name='test_email' value='<EMAIL>' style='width: 300px; padding: 5px;'>";
    echo "</div>";
    echo "<div style='margin-bottom: 10px;'>";
    echo "<label>Password:</label><br>";
    echo "<input type='password' name='test_password' value='admin123' style='width: 300px; padding: 5px;'>";
    echo "</div>";
    echo "<button type='submit' name='test_login' style='background: #007bff; color: white; padding: 10px 20px; border: none; border-radius: 3px;'>Test Login</button>";
    echo "</form>";
    
    if (isset($_POST['test_login'])) {
        $test_email = $_POST['test_email'];
        $test_password = $_POST['test_password'];
        
        $stmt = $pdo->prepare("SELECT id, password FROM users WHERE email = ?");
        $stmt->execute([$test_email]);
        $user = $stmt->fetch();
        
        if ($user && password_verify($test_password, $user['password'])) {
            echo "<div style='background: #d4edda; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
            echo "<h4 style='color: #155724;'>✓ Login Test Successful!</h4>";
            echo "<p>You can now login to the admin panel.</p>";
            echo "</div>";
        } else {
            echo "<div style='background: #f8d7da; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
            echo "<h4 style='color: #721c24;'>✗ Login Test Failed!</h4>";
            echo "<p>Please check the credentials.</p>";
            echo "</div>";
        }
    }
    
} catch (PDOException $e) {
    echo "<div style='background: #f8d7da; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
    echo "<h4 style='color: #721c24;'>✗ Database Error!</h4>";
    echo "<p>Error: " . $e->getMessage() . "</p>";
    echo "<h5>Troubleshooting:</h5>";
    echo "<ul>";
    echo "<li>Make sure MySQL is running in XAMPP</li>";
    echo "<li>Check if database 'ovs_db' exists</li>";
    echo "<li>Verify database credentials in this file</li>";
    echo "</ul>";
    echo "</div>";
}

echo "<br><h3>Next Steps</h3>";
echo "<ol>";
echo "<li>After successful password reset, visit: <a href='index.php'>Main Site</a></li>";
echo "<li>Login with the credentials shown above</li>";
echo "<li>Go to Admin Panel: <a href='admin/'>Admin Dashboard</a></li>";
echo "<li><strong>Delete this file (reset-admin.php) for security</strong></li>";
echo "</ol>";

echo "<br><p style='background: #fff3cd; padding: 10px; border-radius: 5px;'>";
echo "<strong>Security Note:</strong> Please delete this file after use for security reasons.";
echo "</p>";
?>
