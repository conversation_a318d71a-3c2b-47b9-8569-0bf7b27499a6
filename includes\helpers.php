<?php
/**
 * Global Helper Functions
 * Common functions used across the application
 */

// Ensure session is started
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

/**
 * Flash Message Functions
 */
if (!function_exists('setFlashMessage')) {
    function setFlashMessage($type, $message) {
        $_SESSION['flash_' . $type] = $message;
    }
}

if (!function_exists('getFlashMessage')) {
    function getFlashMessage($type) {
        $message = $_SESSION['flash_' . $type] ?? null;
        unset($_SESSION['flash_' . $type]);
        return $message;
    }
}

if (!function_exists('getFlashMessages')) {
    function getFlashMessages() {
        $messages = $_SESSION['flash'] ?? [];
        unset($_SESSION['flash']);
        return $messages;
    }
}

/**
 * User Authentication Functions
 */
if (!function_exists('getCurrentUser')) {
    function getCurrentUser() {
        return $_SESSION['user'] ?? ['id' => 1, 'username' => 'admin', 'role' => 'admin'];
    }
}

if (!function_exists('isLoggedIn')) {
    function isLoggedIn() {
        return isset($_SESSION['user']) && !empty($_SESSION['user']['id']);
    }
}

if (!function_exists('isAdmin')) {
    function isAdmin() {
        $user = getCurrentUser();
        return isset($user['role']) && $user['role'] === 'admin';
    }
}

/**
 * CSRF Protection Functions
 */
if (!function_exists('generateCSRFToken')) {
    function generateCSRFToken() {
        if (!isset($_SESSION['csrf_token'])) {
            $_SESSION['csrf_token'] = bin2hex(random_bytes(32));
        }
        return $_SESSION['csrf_token'];
    }
}

if (!function_exists('validateCSRFToken')) {
    function validateCSRFToken($token) {
        return isset($_SESSION['csrf_token']) && hash_equals($_SESSION['csrf_token'], $token);
    }
}

/**
 * Input Sanitization Functions
 */
if (!function_exists('sanitizeInput')) {
    function sanitizeInput($input) {
        if (is_array($input)) {
            return array_map('sanitizeInput', $input);
        }
        return htmlspecialchars(trim($input), ENT_QUOTES, 'UTF-8');
    }
}

if (!function_exists('sanitizeFilename')) {
    function sanitizeFilename($filename) {
        // Remove any path traversal attempts
        $filename = basename($filename);
        // Remove special characters except dots, hyphens, and underscores
        $filename = preg_replace('/[^a-zA-Z0-9._-]/', '', $filename);
        return $filename;
    }
}

/**
 * File Upload Helper Functions
 */
if (!function_exists('validateImageUpload')) {
    function validateImageUpload($file, $maxSize = 5242880, $allowedTypes = ['image/jpeg', 'image/png', 'image/gif']) {
        if (!isset($file['error']) || $file['error'] !== UPLOAD_ERR_OK) {
            return ['success' => false, 'message' => 'File upload error'];
        }
        
        if ($file['size'] > $maxSize) {
            return ['success' => false, 'message' => 'File too large. Maximum ' . ($maxSize / 1024 / 1024) . 'MB allowed'];
        }
        
        if (!in_array($file['type'], $allowedTypes)) {
            return ['success' => false, 'message' => 'Invalid file type. Only ' . implode(', ', $allowedTypes) . ' allowed'];
        }
        
        return ['success' => true];
    }
}

/**
 * Redirect Function
 */
if (!function_exists('redirect')) {
    function redirect($url, $status = 302) {
        header("Location: $url", true, $status);
        exit();
    }
}

/**
 * Database Helper Functions
 */
if (!function_exists('getDB')) {
    function getDB() {
        try {
            $dsn = "mysql:host=" . DB_HOST . ";dbname=" . DB_NAME . ";charset=utf8mb4";
            $pdo = new PDO($dsn, DB_USER, DB_PASS, [
                PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
                PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
                PDO::ATTR_EMULATE_PREPARES => false
            ]);
            return $pdo;
        } catch (PDOException $e) {
            throw new Exception("Database connection failed: " . $e->getMessage());
        }
    }
}

/**
 * Election Level Helper Functions
 */
if (!function_exists('getElectionLevels')) {
    function getElectionLevels() {
        return [
            'ward' => 'ওয়ার্ড পর্যায়',
            'village' => 'গ্রাম/ইউনিয়ন পর্যায়', 
            'upazila' => 'উপজেলা পর্যায়',
            'district' => 'জেলা পর্যায়',
            'national' => 'জাতীয় পর্যায়'
        ];
    }
}

if (!function_exists('getElectionLevelColor')) {
    function getElectionLevelColor($level) {
        $colors = [
            'ward' => 'primary',
            'village' => 'success', 
            'upazila' => 'info',
            'district' => 'warning',
            'national' => 'danger'
        ];
        return $colors[$level] ?? 'secondary';
    }
}

/**
 * Area Type Helper Functions
 */
if (!function_exists('getAreaTypes')) {
    function getAreaTypes() {
        return [
            'ward' => 'ওয়ার্ড',
            'village' => 'গ্রাম',
            'union' => 'ইউনিয়ন',
            'municipality' => 'পৌরসভা',
            'upazila' => 'উপজেলা',
            'district' => 'জেলা',
            'division' => 'বিভাগ',
            'national' => 'জাতীয়'
        ];
    }
}

/**
 * Pagination Helper Functions
 */
if (!function_exists('generatePagination')) {
    function generatePagination($currentPage, $totalPages, $baseUrl, $params = []) {
        if ($totalPages <= 1) return '';
        
        $html = '<nav aria-label="Page navigation"><ul class="pagination justify-content-center">';
        
        // Previous button
        if ($currentPage > 1) {
            $prevUrl = $baseUrl . '?' . http_build_query(array_merge($params, ['page' => $currentPage - 1]));
            $html .= '<li class="page-item"><a class="page-link" href="' . $prevUrl . '">পূর্ববর্তী</a></li>';
        }
        
        // Page numbers
        $start = max(1, $currentPage - 2);
        $end = min($totalPages, $currentPage + 2);
        
        for ($i = $start; $i <= $end; $i++) {
            $pageUrl = $baseUrl . '?' . http_build_query(array_merge($params, ['page' => $i]));
            $active = $i === $currentPage ? 'active' : '';
            $html .= '<li class="page-item ' . $active . '"><a class="page-link" href="' . $pageUrl . '">' . $i . '</a></li>';
        }
        
        // Next button
        if ($currentPage < $totalPages) {
            $nextUrl = $baseUrl . '?' . http_build_query(array_merge($params, ['page' => $currentPage + 1]));
            $html .= '<li class="page-item"><a class="page-link" href="' . $nextUrl . '">পরবর্তী</a></li>';
        }
        
        $html .= '</ul></nav>';
        return $html;
    }
}

/**
 * Date/Time Helper Functions
 */
if (!function_exists('formatBanglaDate')) {
    function formatBanglaDate($date, $format = 'd/m/Y') {
        if (!$date) return '';
        
        $timestamp = is_string($date) ? strtotime($date) : $date;
        $formatted = date($format, $timestamp);
        
        // Convert to Bangla numerals
        $english = ['0', '1', '2', '3', '4', '5', '6', '7', '8', '9'];
        $bangla = ['০', '১', '২', '৩', '৪', '৫', '৬', '৭', '৮', '৯'];
        
        return str_replace($english, $bangla, $formatted);
    }
}

/**
 * Debug Helper Functions
 */
if (!function_exists('dd')) {
    function dd(...$vars) {
        echo '<pre style="background: #f8f9fa; padding: 20px; border: 1px solid #dee2e6; border-radius: 5px; margin: 20px 0;">';
        foreach ($vars as $var) {
            var_dump($var);
        }
        echo '</pre>';
        die();
    }
}

if (!function_exists('logError')) {
    function logError($message, $context = []) {
        $logMessage = date('Y-m-d H:i:s') . ' - ' . $message;
        if (!empty($context)) {
            $logMessage .= ' - Context: ' . json_encode($context);
        }
        error_log($logMessage);
    }
}
?>
