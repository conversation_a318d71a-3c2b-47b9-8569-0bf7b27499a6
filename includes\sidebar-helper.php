<?php
/**
 * Sidebar Helper Functions
 * সাইডবার হেল্পার ফাংশন
 */

/**
 * Render page with sidebar layout
 * সাইডবার লেআউট সহ পেজ রেন্ডার করুন
 */
function renderWithSidebar($content, $options = []) {
    // Set default options
    $defaults = [
        'page_title' => 'Dashboard',
        'page_subtitle' => '',
        'show_top_nav' => true,
        'additional_head' => '',
        'additional_scripts' => '',
        'inline_js' => ''
    ];
    
    $options = array_merge($defaults, $options);
    
    // Extract options to variables
    extract($options);
    
    // Include the sidebar layout
    include __DIR__ . '/header-with-sidebar.php';
}

/**
 * Start sidebar page
 * সাইডবার পেজ শুরু করুন
 */
function startSidebarPage($page_title = 'Dashboard', $page_subtitle = '', $show_top_nav = true) {
    global $SIDEBAR_PAGE_TITLE, $SIDEBAR_PAGE_SUBTITLE, $SIDEBAR_SHOW_TOP_NAV;
    
    $SIDEBAR_PAGE_TITLE = $page_title;
    $SIDEBAR_PAGE_SUBTITLE = $page_subtitle;
    $SIDEBAR_SHOW_TOP_NAV = $show_top_nav;
    
    ob_start();
}

/**
 * End sidebar page
 * সাইডবার পেজ শেষ করুন
 */
function endSidebarPage($additional_options = []) {
    global $SIDEBAR_PAGE_TITLE, $SIDEBAR_PAGE_SUBTITLE, $SIDEBAR_SHOW_TOP_NAV;
    
    $content = ob_get_clean();
    
    $options = array_merge([
        'page_title' => $SIDEBAR_PAGE_TITLE ?? 'Dashboard',
        'page_subtitle' => $SIDEBAR_PAGE_SUBTITLE ?? '',
        'show_top_nav' => $SIDEBAR_SHOW_TOP_NAV ?? true
    ], $additional_options);
    
    renderWithSidebar($content, $options);
}

/**
 * Convert existing page to sidebar layout
 * বিদ্যমান পেজকে সাইডবার লেআউটে রূপান্তর করুন
 */
function convertToSidebarLayout($file_path) {
    if (!file_exists($file_path)) {
        return false;
    }
    
    $content = file_get_contents($file_path);
    
    // Find page title
    $title_pattern = '/\$page_title\s*=\s*[\'"]([^\'"]*)[\'"];/';
    preg_match($title_pattern, $content, $title_matches);
    $page_title = $title_matches[1] ?? 'Dashboard';
    
    // Find include header
    $header_pattern = '/include\s+[\'"][^\'"]*header\.php[\'"];/';
    
    // Replace header include with sidebar setup
    $new_content = preg_replace($header_pattern, 
        "\$page_title = '$page_title';\n" .
        "\$show_top_nav = true;\n" .
        "ob_start();", 
        $content
    );
    
    // Find footer include
    $footer_pattern = '/include\s+[\'"][^\'"]*footer\.php[\'"];/';
    
    // Replace footer include with sidebar end
    $new_content = preg_replace($footer_pattern,
        "\$content = ob_get_clean();\n" .
        "include 'includes/header-with-sidebar.php';",
        $new_content
    );
    
    return file_put_contents($file_path, $new_content);
}

/**
 * Add scrolling indicator to sidebar
 * সাইডবারে স্ক্রোলিং ইন্ডিকেটর যোগ করুন
 */
function addScrollingIndicator() {
    return '
    <script>
    $(document).ready(function() {
        const sidebar = $(".modern-sidebar");
        const nav = $(".sidebar-nav");
        
        // Add scroll indicator
        function updateScrollIndicator() {
            const scrollTop = nav.scrollTop();
            const scrollHeight = nav[0].scrollHeight;
            const clientHeight = nav.height();
            const scrollPercent = (scrollTop / (scrollHeight - clientHeight)) * 100;
            
            // Update scroll indicator
            if (!$("#scroll-indicator").length) {
                sidebar.append(`
                    <div id="scroll-indicator" style="
                        position: absolute;
                        top: 0;
                        right: 0;
                        width: 3px;
                        height: 100%;
                        background: rgba(255,255,255,0.1);
                        border-radius: 2px;
                    ">
                        <div id="scroll-thumb" style="
                            width: 100%;
                            background: linear-gradient(180deg, #ff6b6b, #feca57);
                            border-radius: 2px;
                            transition: height 0.3s ease;
                            height: ${Math.max(10, (clientHeight / scrollHeight) * 100)}%;
                            transform: translateY(${scrollPercent}%);
                        "></div>
                    </div>
                `);
            } else {
                $("#scroll-thumb").css({
                    "transform": `translateY(${scrollPercent * ((100 - (clientHeight / scrollHeight) * 100) / 100)}%)`
                });
            }
        }
        
        nav.on("scroll", updateScrollIndicator);
        updateScrollIndicator();
    });
    </script>';
}

/**
 * Get sidebar menu count
 * সাইডবার মেনু সংখ্যা পান
 */
function getSidebarMenuCount() {
    // This would be calculated based on user role
    $count = 0;
    
    if (isLoggedIn()) {
        $count += 5; // Basic user menu
        
        if (isAdmin()) {
            $count += 15; // Admin menu items
        }
        
        $count += 10; // Quick actions
    } else {
        $count += 4; // Guest menu
    }
    
    return $count;
}

/**
 * Check if sidebar scrolling is needed
 * সাইডবার স্ক্রোলিং প্রয়োজন কিনা চেক করুন
 */
function isSidebarScrollingNeeded() {
    $menu_count = getSidebarMenuCount();
    $estimated_height = $menu_count * 60; // Approximate height per menu item
    $available_height = 800; // Approximate sidebar height
    
    return $estimated_height > $available_height;
}

/**
 * Generate sidebar CSS for current page
 * বর্তমান পেজের জন্য সাইডবার CSS তৈরি করুন
 */
function generateSidebarCSS() {
    $css = '';
    
    if (isSidebarScrollingNeeded()) {
        $css .= '
        <style>
        .sidebar-nav {
            max-height: calc(100vh - 250px);
            overflow-y: auto;
        }
        
        .modern-sidebar::-webkit-scrollbar {
            width: 8px;
        }
        
        .modern-sidebar::-webkit-scrollbar-thumb {
            background: linear-gradient(180deg, #ff6b6b, #feca57, #48dbfb);
            border-radius: 10px;
        }
        </style>';
    }
    
    return $css;
}

/**
 * Auto-convert all pages in a directory
 * একটি ডিরেক্টরির সব পেজ অটো-কনভার্ট করুন
 */
function autoConvertDirectory($directory) {
    $files = glob($directory . '/*.php');
    $converted = 0;
    
    foreach ($files as $file) {
        if (strpos(file_get_contents($file), 'header.php') !== false) {
            if (convertToSidebarLayout($file)) {
                $converted++;
            }
        }
    }
    
    return $converted;
}
?>
