<?php
/**
 * Positions Management
 * Manage election positions/posts
 */

require_once '../config/config.php';

// Check if user is logged in and is admin
if (!isLoggedIn() || !isAdmin()) {
    redirect(APP_URL . '/auth/login.php');
}

$action = $_GET['action'] ?? 'list';
$message = '';
$error = '';

// Handle form submissions
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (!validateCSRFToken($_POST['csrf_token'] ?? '')) {
        $error = 'Invalid CSRF token';
    } else {
        switch ($action) {
            case 'add':
                $result = addPosition($_POST);
                if ($result['success']) {
                    setFlashMessage('success', $result['message']);
                    redirect($_SERVER['PHP_SELF']);
                } else {
                    $error = $result['message'];
                }
                break;
                
            case 'edit':
                $result = updatePosition($_POST);
                if ($result['success']) {
                    setFlashMessage('success', $result['message']);
                    redirect($_SERVER['PHP_SELF']);
                } else {
                    $error = $result['message'];
                }
                break;
                
            case 'delete':
                $result = deletePosition($_POST['position_id']);
                if ($result['success']) {
                    setFlashMessage('success', $result['message']);
                } else {
                    setFlashMessage('error', $result['message']);
                }
                redirect($_SERVER['PHP_SELF']);
                break;
        }
    }
}

// Get positions list
try {
    $db = getDB();
    
    $search = $_GET['search'] ?? '';
    $level_filter = $_GET['level'] ?? '';
    
    $where_conditions = ['is_active = 1'];
    $params = [];
    
    if (!empty($search)) {
        $where_conditions[] = "(title LIKE ? OR title_bn LIKE ? OR position_code LIKE ?)";
        $search_term = "%$search%";
        $params[] = $search_term;
        $params[] = $search_term;
        $params[] = $search_term;
    }
    
    if (!empty($level_filter)) {
        $where_conditions[] = "election_level = ?";
        $params[] = $level_filter;
    }
    
    $where_clause = implode(' AND ', $where_conditions);
    
    $sql = "SELECT * FROM positions WHERE $where_clause ORDER BY election_level, title";
    $stmt = $db->prepare($sql);
    $stmt->execute($params);
    $positions = $stmt->fetchAll();
    
} catch (Exception $e) {
    $error = 'Database error: ' . $e->getMessage();
    $positions = [];
}

// Election levels
$election_levels = [
    'ward' => 'ওয়ার্ড পর্যায়',
    'village' => 'গ্রাম/ইউনিয়ন পর্যায়', 
    'upazila' => 'উপজেলা পর্যায়',
    'district' => 'জেলা পর্যায়',
    'national' => 'জাতীয় পর্যায়'
];

$area_types = [
    'ward' => 'ওয়ার্ড',
    'village' => 'গ্রাম',
    'union' => 'ইউনিয়ন',
    'municipality' => 'পৌরসভা',
    'upazila' => 'উপজেলা',
    'district' => 'জেলা',
    'division' => 'বিভাগ',
    'national' => 'জাতীয়'
];

// Functions
function addPosition($data) {
    try {
        $db = getDB();
        
        // Check if position code already exists
        $stmt = $db->prepare("SELECT id FROM positions WHERE position_code = ?");
        $stmt->execute([sanitizeInput($data['position_code'])]);
        if ($stmt->fetch()) {
            return ['success' => false, 'message' => 'পদের কোড ইতিমধ্যে বিদ্যমান'];
        }
        
        $stmt = $db->prepare("
            INSERT INTO positions (
                position_code, title, title_bn, description, description_bn,
                election_level, area_type
            ) VALUES (?, ?, ?, ?, ?, ?, ?)
        ");
        
        $stmt->execute([
            strtoupper(sanitizeInput($data['position_code'])),
            sanitizeInput($data['title']),
            sanitizeInput($data['title_bn']) ?: null,
            sanitizeInput($data['description']) ?: null,
            sanitizeInput($data['description_bn']) ?: null,
            sanitizeInput($data['election_level']),
            sanitizeInput($data['area_type']) ?: null
        ]);
        
        return ['success' => true, 'message' => 'পদ সফলভাবে যোগ করা হয়েছে'];
        
    } catch (Exception $e) {
        return ['success' => false, 'message' => 'Error: ' . $e->getMessage()];
    }
}

function updatePosition($data) {
    try {
        $db = getDB();
        
        $stmt = $db->prepare("
            UPDATE positions SET 
                title = ?, title_bn = ?, description = ?, description_bn = ?,
                election_level = ?, area_type = ?
            WHERE id = ?
        ");
        
        $stmt->execute([
            sanitizeInput($data['title']),
            sanitizeInput($data['title_bn']) ?: null,
            sanitizeInput($data['description']) ?: null,
            sanitizeInput($data['description_bn']) ?: null,
            sanitizeInput($data['election_level']),
            sanitizeInput($data['area_type']) ?: null,
            intval($data['position_id'])
        ]);
        
        return ['success' => true, 'message' => 'পদ সফলভাবে আপডেট করা হয়েছে'];
        
    } catch (Exception $e) {
        return ['success' => false, 'message' => 'Error: ' . $e->getMessage()];
    }
}

function deletePosition($position_id) {
    try {
        $db = getDB();
        
        // Check if position is being used by any candidate
        $stmt = $db->prepare("SELECT COUNT(*) FROM candidates WHERE position_id = ? AND is_active = 1");
        $stmt->execute([$position_id]);
        $candidate_count = $stmt->fetchColumn();
        
        if ($candidate_count > 0) {
            return ['success' => false, 'message' => "এই পদটি $candidate_count জন প্রার্থী ব্যবহার করছেন। প্রথমে তাদের পদ পরিবর্তন করুন।"];
        }
        
        $stmt = $db->prepare("UPDATE positions SET is_active = 0 WHERE id = ?");
        $stmt->execute([$position_id]);
        
        return ['success' => true, 'message' => 'পদ সফলভাবে মুছে ফেলা হয়েছে'];
        
    } catch (Exception $e) {
        return ['success' => false, 'message' => 'Error: ' . $e->getMessage()];
    }
}

function getElectionLevelColor($level) {
    $colors = [
        'ward' => 'primary',
        'village' => 'success',
        'upazila' => 'info',
        'district' => 'warning',
        'national' => 'danger'
    ];
    return $colors[$level] ?? 'secondary';
}

// Helper functions for positions.php
if (!function_exists('getCurrentUser')) {
    function getCurrentUser() {
        return $_SESSION['user'] ?? ['id' => 1, 'username' => 'admin'];
    }
}

if (!function_exists('generateCSRFToken')) {
    function generateCSRFToken() {
        if (!isset($_SESSION['csrf_token'])) {
            $_SESSION['csrf_token'] = bin2hex(random_bytes(32));
        }
        return $_SESSION['csrf_token'];
    }
}

if (!function_exists('validateCSRFToken')) {
    function validateCSRFToken($token) {
        return isset($_SESSION['csrf_token']) && hash_equals($_SESSION['csrf_token'], $token);
    }
}
?>

<!DOCTYPE html>
<html lang="bn">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>পদ ব্যবস্থাপনা - OVS</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body>

<?php include '../includes/admin-nav.php'; ?>

<div class="container-fluid mt-4">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h2><i class="fas fa-briefcase"></i> পদ ব্যবস্থাপনা</h2>
                <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addPositionModal">
                    <i class="fas fa-plus"></i> নতুন পদ
                </button>
            </div>

            <!-- Flash Messages -->
            <?php if (isset($_SESSION['flash_success'])): ?>
                <div class="alert alert-success alert-dismissible fade show">
                    <?php echo $_SESSION['flash_success']; unset($_SESSION['flash_success']); ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            <?php endif; ?>

            <?php if ($error): ?>
                <div class="alert alert-danger alert-dismissible fade show">
                    <?php echo $error; ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            <?php endif; ?>

            <!-- Filters -->
            <div class="card mb-4">
                <div class="card-body">
                    <form method="GET" class="row g-3">
                        <div class="col-md-6">
                            <input type="text" class="form-control" name="search" placeholder="পদের নাম বা কোড দিয়ে খুঁজুন" value="<?php echo htmlspecialchars($search); ?>">
                        </div>
                        <div class="col-md-4">
                            <select class="form-select" name="level">
                                <option value="">সব ধরনের নির্বাচন</option>
                                <?php foreach ($election_levels as $level => $label): ?>
                                    <option value="<?php echo $level; ?>" <?php echo $level_filter === $level ? 'selected' : ''; ?>>
                                        <?php echo $label; ?>
                                    </option>
                                <?php endforeach; ?>
                            </select>
                        </div>
                        <div class="col-md-2">
                            <button type="submit" class="btn btn-outline-primary w-100">
                                <i class="fas fa-search"></i> খুঁজুন
                            </button>
                        </div>
                    </form>
                </div>
            </div>

            <!-- Positions List -->
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-list"></i> পদের তালিকা 
                        <span class="badge bg-primary"><?php echo count($positions); ?></span>
                    </h5>
                </div>
                <div class="card-body">
                    <?php if (empty($positions)): ?>
                        <div class="text-center py-5">
                            <i class="fas fa-briefcase fa-3x text-muted mb-3"></i>
                            <h5 class="text-muted">কোন পদ পাওয়া যায়নি</h5>
                            <p class="text-muted">নতুন পদ যোগ করতে উপরের বাটনে ক্লিক করুন</p>
                        </div>
                    <?php else: ?>
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead>
                                    <tr>
                                        <th>পদের কোড</th>
                                        <th>পদের নাম</th>
                                        <th>নির্বাচনের ধরন</th>
                                        <th>এলাকার ধরন</th>
                                        <th>বিবরণ</th>
                                        <th>অ্যাকশন</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($positions as $position): ?>
                                        <tr>
                                            <td>
                                                <code class="bg-light px-2 py-1 rounded"><?php echo $position['position_code']; ?></code>
                                            </td>
                                            <td>
                                                <strong><?php echo htmlspecialchars($position['title']); ?></strong>
                                                <?php if ($position['title_bn']): ?>
                                                    <br><small class="text-muted"><?php echo htmlspecialchars($position['title_bn']); ?></small>
                                                <?php endif; ?>
                                            </td>
                                            <td>
                                                <span class="badge bg-<?php echo getElectionLevelColor($position['election_level']); ?>">
                                                    <?php echo $election_levels[$position['election_level']] ?? $position['election_level']; ?>
                                                </span>
                                            </td>
                                            <td>
                                                <?php if ($position['area_type']): ?>
                                                    <span class="badge bg-secondary">
                                                        <?php echo $area_types[$position['area_type']] ?? $position['area_type']; ?>
                                                    </span>
                                                <?php else: ?>
                                                    <small class="text-muted">সব ধরনের</small>
                                                <?php endif; ?>
                                            </td>
                                            <td>
                                                <?php if ($position['description'] || $position['description_bn']): ?>
                                                    <small><?php echo htmlspecialchars($position['description_bn'] ?: $position['description']); ?></small>
                                                <?php else: ?>
                                                    <small class="text-muted">কোন বিবরণ নেই</small>
                                                <?php endif; ?>
                                            </td>
                                            <td>
                                                <div class="btn-group" role="group">
                                                    <button type="button" class="btn btn-sm btn-outline-warning" 
                                                            onclick="editPosition(<?php echo $position['id']; ?>)">
                                                        <i class="fas fa-edit"></i>
                                                    </button>
                                                    <button type="button" class="btn btn-sm btn-outline-danger" 
                                                            onclick="deletePosition(<?php echo $position['id']; ?>, '<?php echo htmlspecialchars($position['title']); ?>')">
                                                        <i class="fas fa-trash"></i>
                                                    </button>
                                                </div>
                                            </td>
                                        </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Add Position Modal -->
<div class="modal fade" id="addPositionModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-plus"></i> নতুন পদ যোগ করুন
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form method="POST" action="?action=add">
                <div class="modal-body">
                    <input type="hidden" name="csrf_token" value="<?php echo generateCSRFToken(); ?>">
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-floating mb-3">
                                <input type="text" class="form-control" name="position_code" required style="text-transform: uppercase;">
                                <label>পদের কোড *</label>
                                <small class="text-muted">যেমন: MP, UC, WC</small>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-floating mb-3">
                                <select class="form-select" name="election_level" required>
                                    <option value="">নির্বাচনের ধরন</option>
                                    <?php foreach ($election_levels as $level => $label): ?>
                                        <option value="<?php echo $level; ?>"><?php echo $label; ?></option>
                                    <?php endforeach; ?>
                                </select>
                                <label>নির্বাচনের ধরন *</label>
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-floating mb-3">
                                <input type="text" class="form-control" name="title" required>
                                <label>পদের নাম (ইংরেজি) *</label>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-floating mb-3">
                                <input type="text" class="form-control" name="title_bn">
                                <label>পদের নাম (বাংলা)</label>
                            </div>
                        </div>
                    </div>
                    
                    <div class="form-floating mb-3">
                        <select class="form-select" name="area_type">
                            <option value="">সব ধরনের এলাকা</option>
                            <?php foreach ($area_types as $type => $label): ?>
                                <option value="<?php echo $type; ?>"><?php echo $label; ?></option>
                            <?php endforeach; ?>
                        </select>
                        <label>এলাকার ধরন</label>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-floating mb-3">
                                <textarea class="form-control" name="description" style="height: 80px"></textarea>
                                <label>বিবরণ (ইংরেজি)</label>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-floating mb-3">
                                <textarea class="form-control" name="description_bn" style="height: 80px"></textarea>
                                <label>বিবরণ (বাংলা)</label>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                        <i class="fas fa-times"></i> বাতিল
                    </button>
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-save"></i> সংরক্ষণ করুন
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
function editPosition(positionId) {
    alert('Edit position: ' + positionId);
}

function deletePosition(positionId, positionTitle) {
    if (confirm('আপনি কি নিশ্চিত যে "' + positionTitle + '" পদটি মুছে ফেলতে চান?')) {
        const form = document.createElement('form');
        form.method = 'POST';
        form.action = '?action=delete';
        
        const csrfInput = document.createElement('input');
        csrfInput.type = 'hidden';
        csrfInput.name = 'csrf_token';
        csrfInput.value = '<?php echo generateCSRFToken(); ?>';
        
        const positionInput = document.createElement('input');
        positionInput.type = 'hidden';
        positionInput.name = 'position_id';
        positionInput.value = positionId;
        
        form.appendChild(csrfInput);
        form.appendChild(positionInput);
        document.body.appendChild(form);
        form.submit();
    }
}
</script>

<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
