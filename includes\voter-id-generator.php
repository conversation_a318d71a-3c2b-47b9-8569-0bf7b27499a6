<?php
/**
 * Voter ID Generator
 * Generate unique random voter IDs
 */

/**
 * Generate unique voter ID
 * @param PDO $db Database connection
 * @param string $format Format pattern (default: 'V{YYYY}{MM}{RRRRR}')
 * @return string Generated voter ID
 */
function generateVoterID($db, $format = 'V{YYYY}{MM}{RRRRR}') {
    $max_attempts = 1000; // Increased attempts for better uniqueness
    $attempt = 0;

    do {
        $voter_id = generateVoterIDPattern($format);
        $attempt++;

        // Check if ID already exists
        $stmt = $db->prepare("SELECT id FROM voters WHERE voter_id = ?");
        $stmt->execute([$voter_id]);

        if (!$stmt->fetch()) {
            error_log("Generated unique voter ID: $voter_id (attempt: $attempt)");
            return $voter_id; // Unique ID found
        }

    } while ($attempt < $max_attempts);

    // Fallback: Use timestamp-based ID with microseconds for guaranteed uniqueness
    $fallback_id = 'V' . date('YmdHis') . substr(microtime(), 2, 6);
    error_log("Using fallback voter ID: $fallback_id after $max_attempts attempts");
    return $fallback_id;
}

/**
 * Generate voter ID based on pattern
 * @param string $pattern Pattern with placeholders
 * @return string Generated ID
 */
function generateVoterIDPattern($pattern) {
    $replacements = [
        '{YYYY}' => date('Y'),           // Current year (2024)
        '{YY}' => date('y'),             // Short year (24)
        '{MM}' => date('m'),             // Current month (01-12)
        '{DD}' => date('d'),             // Current day (01-31)
        '{RRRRR}' => str_pad(rand(1, 99999), 5, '0', STR_PAD_LEFT),    // 5-digit random
        '{RRRR}' => str_pad(rand(1, 9999), 4, '0', STR_PAD_LEFT),      // 4-digit random
        '{RRR}' => str_pad(rand(1, 999), 3, '0', STR_PAD_LEFT),        // 3-digit random
        '{RR}' => str_pad(rand(1, 99), 2, '0', STR_PAD_LEFT),          // 2-digit random
        '{SSSS}' => generateSequentialNumber(),                         // Sequential number
        '{AREA}' => '',                  // Area code (to be filled by context)
        '{WARD}' => '',                  // Ward code (to be filled by context)
    ];
    
    return str_replace(array_keys($replacements), array_values($replacements), $pattern);
}

/**
 * Generate voter ID with area context
 * @param PDO $db Database connection
 * @param int $area_id Area/Ward ID
 * @param string $format Format pattern
 * @return string Generated voter ID
 */
function generateVoterIDWithArea($db, $area_id, $format = 'V{WARD_CODE}{YYYY}{RRRRR}') {
    // Get ward code (consistent for same ward)
    $ward_code = getWardCode($db, $area_id);

    // Replace ward-specific placeholders
    $format = str_replace('{WARD_CODE}', $ward_code, $format);
    $format = str_replace('{WARD_ID}', str_pad($area_id, 2, '0', STR_PAD_LEFT), $format);
    $format = str_replace('{WARD}', str_pad($area_id, 2, '0', STR_PAD_LEFT), $format);

    return generateVoterID($db, $format);
}

/**
 * Get consistent ward code for area
 * @param PDO $db Database connection
 * @param int $area_id Area/Ward ID
 * @return string Ward code
 */
function getWardCode($db, $area_id) {
    try {
        // First check if ward already has a code stored
        $stmt = $db->prepare("SELECT code FROM areas WHERE id = ? AND code IS NOT NULL AND code != ''");
        $stmt->execute([$area_id]);
        $existing_code = $stmt->fetchColumn();

        if ($existing_code) {
            return strtoupper($existing_code);
        }

        // Get ward hierarchy for code generation
        $stmt = $db->prepare("
            SELECT
                w.id as ward_id,
                w.name as ward_name,
                w.name_bn as ward_name_bn,
                u.name as union_name,
                u.name_bn as union_name_bn,
                up.name as upazila_name,
                up.name_bn as upazila_name_bn,
                d.name as district_name,
                d.name_bn as district_name_bn
            FROM areas w
            LEFT JOIN areas u ON w.parent_id = u.id AND u.type IN ('union', 'municipality')
            LEFT JOIN areas up ON u.parent_id = up.id AND up.type = 'upazila'
            LEFT JOIN areas d ON up.parent_id = d.id AND d.type = 'district'
            WHERE w.id = ? AND w.type = 'ward'
        ");
        $stmt->execute([$area_id]);
        $hierarchy = $stmt->fetch();

        if (!$hierarchy) {
            // Fallback: Use ward ID
            return 'W' . str_pad($area_id, 2, '0', STR_PAD_LEFT);
        }

        // Generate code based on hierarchy
        $code = generateHierarchicalWardCode($hierarchy);

        // Store the generated code for future use
        $stmt = $db->prepare("UPDATE areas SET code = ? WHERE id = ?");
        $stmt->execute([$code, $area_id]);

        return $code;

    } catch (Exception $e) {
        error_log("Error generating ward code: " . $e->getMessage());
        return 'W' . str_pad($area_id, 2, '0', STR_PAD_LEFT);
    }
}

/**
 * Generate hierarchical ward code
 * @param array $hierarchy Ward hierarchy data
 * @return string Generated code
 */
function generateHierarchicalWardCode($hierarchy) {
    $code_parts = [];

    // District code (first 2 letters)
    if (!empty($hierarchy['district_name'])) {
        $district_code = generateCodeFromName($hierarchy['district_name']);
        $code_parts[] = substr($district_code, 0, 2);
    }

    // Upazila code (1 letter)
    if (!empty($hierarchy['upazila_name'])) {
        $upazila_code = generateCodeFromName($hierarchy['upazila_name']);
        $code_parts[] = substr($upazila_code, 0, 1);
    }

    // Ward number (2 digits)
    $ward_id = str_pad($hierarchy['ward_id'], 2, '0', STR_PAD_LEFT);
    $code_parts[] = $ward_id;

    // Combine parts
    $code = implode('', $code_parts);

    // Ensure minimum length
    if (strlen($code) < 3) {
        $code = 'W' . str_pad($hierarchy['ward_id'], 2, '0', STR_PAD_LEFT);
    }

    return strtoupper($code);
}

/**
 * Generate code from name
 * @param string $name Area name
 * @return string Generated code
 */
function generateCodeFromName($name) {
    // Remove common words
    $clean_name = preg_replace('/\b(ward|union|upazila|district|division|পৌরসভা|ইউনিয়ন|উপজেলা|জেলা|বিভাগ|ওয়ার্ড)\b/i', '', $name);

    // Get first letters of words
    $words = preg_split('/\s+/', trim($clean_name));
    $code = '';

    foreach ($words as $word) {
        if (strlen($word) > 0) {
            $code .= strtoupper(substr($word, 0, 1));
        }
        if (strlen($code) >= 3) break;
    }

    // If not enough letters, use full name
    if (strlen($code) < 2) {
        $code = strtoupper(substr(preg_replace('/[^a-zA-Z]/', '', $name), 0, 3));
    }

    return $code ?: 'AR';
}

/**
 * Generate area code from area name
 * @param string $area_name Area name
 * @return string Generated area code
 */
function generateAreaCode($area_name) {
    // Remove common words and get initials
    $clean_name = preg_replace('/\b(ward|union|upazila|district|division|পৌরসভা|ইউনিয়ন|উপজেলা|জেলা|বিভাগ|ওয়ার্ড)\b/i', '', $area_name);
    $words = preg_split('/\s+/', trim($clean_name));
    
    $code = '';
    foreach ($words as $word) {
        if (strlen($word) > 0) {
            $code .= strtoupper(substr($word, 0, 1));
        }
        if (strlen($code) >= 3) break;
    }
    
    // Ensure minimum 2 characters
    if (strlen($code) < 2) {
        $code = strtoupper(substr(preg_replace('/[^a-zA-Z]/', '', $area_name), 0, 3));
    }
    
    return $code ?: 'AR';
}

/**
 * Generate sequential number (simple implementation)
 * @return string 4-digit sequential number
 */
function generateSequentialNumber() {
    // This is a simple implementation
    // In production, you might want to use a database sequence
    static $counter = null;
    
    if ($counter === null) {
        $counter = rand(1000, 9999);
    }
    
    return str_pad($counter++, 4, '0', STR_PAD_LEFT);
}

/**
 * Get available voter ID formats
 * @return array Available formats with descriptions
 */
function getVoterIDFormats() {
    return [
        'V{WARD_CODE}{YYYY}{RRRRR}' => [
            'name' => 'Ward-Year-Random (Recommended)',
            'example' => 'VDH01' . date('Y') . '12345',
            'description' => 'V + Ward Code + Year + 5-digit random (VDH0120241234) - Same ward = same code'
        ],
        'V{WARD_CODE}{YY}{RRRR}' => [
            'name' => 'Ward-ShortYear-Random',
            'example' => 'VDH01' . date('y') . '1234',
            'description' => 'V + Ward Code + Short Year + 4-digit random (VDH01241234)'
        ],
        'V{WARD_ID}{YYYY}{RRRRR}' => [
            'name' => 'Ward ID-Year-Random',
            'example' => 'V01' . date('Y') . '12345',
            'description' => 'V + Ward ID + Year + 5-digit random (V0120241234)'
        ],
        'V{YYYY}{MM}{RRRRR}' => [
            'name' => 'Year-Month-Random',
            'example' => 'V' . date('Ym') . '12345',
            'description' => 'V + Year + Month + 5-digit random (V202412345) - No ward grouping'
        ],
        '{WARD_CODE}V{YYYY}{RRRR}' => [
            'name' => 'Ward Code Prefix',
            'example' => 'DH01V' . date('Y') . '1234',
            'description' => 'Ward Code + V + Year + 4-digit random (DH01V20241234)'
        ],
        'V{WARD_CODE}-{YYYY}-{RRRRR}' => [
            'name' => 'Ward-Year-Random (Formatted)',
            'example' => 'VDH01-' . date('Y') . '-12345',
            'description' => 'V + Ward Code + Year + Random with dashes (VDH01-2024-12345)'
        ],
        'V{YYYY}{SSSS}' => [
            'name' => 'Year-Sequential',
            'example' => 'V' . date('Y') . '0001',
            'description' => 'V + Year + Sequential number (V20240001) - No ward grouping'
        ],
        'VTR{WARD_CODE}{YY}{RRR}' => [
            'name' => 'Voter Registration Format',
            'example' => 'VTRDH01' . date('y') . '123',
            'description' => 'VTR + Ward Code + Short Year + 3-digit random (VTRDH01241234)'
        ]
    ];
}

/**
 * Validate voter ID format
 * @param string $voter_id Voter ID to validate
 * @return array Validation result
 */
function validateVoterID($voter_id) {
    $result = [
        'valid' => false,
        'length' => strlen($voter_id),
        'format' => 'Unknown',
        'issues' => []
    ];
    
    // Basic length check
    if (strlen($voter_id) < 3) {
        $result['issues'][] = 'Too short (minimum 3 characters)';
        return $result;
    }
    
    if (strlen($voter_id) > 20) {
        $result['issues'][] = 'Too long (maximum 20 characters)';
        return $result;
    }
    
    // Check for valid characters
    if (!preg_match('/^[A-Z0-9\-]+$/', $voter_id)) {
        $result['issues'][] = 'Contains invalid characters (use only A-Z, 0-9, -)';
        return $result;
    }
    
    // Detect format
    if (preg_match('/^V\d{4}\d{2}\d{5}$/', $voter_id)) {
        $result['format'] = 'Year-Month-Random';
    } elseif (preg_match('/^V\d{2}[A-Z]{2,3}\d{4}$/', $voter_id)) {
        $result['format'] = 'Year-Area-Random';
    } elseif (preg_match('/^V\d+$/', $voter_id)) {
        $result['format'] = 'Simple Numeric';
    } else {
        $result['format'] = 'Custom Format';
    }
    
    $result['valid'] = empty($result['issues']);
    return $result;
}

/**
 * Bulk generate voter IDs
 * @param PDO $db Database connection
 * @param int $count Number of IDs to generate
 * @param string $format Format pattern
 * @return array Generated voter IDs
 */
function bulkGenerateVoterIDs($db, $count, $format = 'V{YYYY}{MM}{RRRRR}') {
    $ids = [];
    
    for ($i = 0; $i < $count; $i++) {
        $ids[] = generateVoterID($db, $format);
    }
    
    return $ids;
}
?>
