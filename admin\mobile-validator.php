<?php
/**
 * Mobile Number Validator Tool
 * Test and validate mobile number formats
 */

require_once '../config/config.php';

// Check if user is admin
if (!isLoggedIn() || !isAdmin()) {
    redirect(APP_URL . '/auth/login.php');
}

echo "<h2>📱 Mobile Number Validator Tool</h2>";

if (isset($_POST['test_mobile'])) {
    $test_mobile = $_POST['mobile_value'];
    
    echo "<h3>📋 Mobile Validation Results:</h3>";
    echo "<div style='background: #f8f9fa; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
    
    echo "<p><strong>Original Mobile:</strong> '$test_mobile'</p>";
    
    // Clean mobile
    $clean_mobile = preg_replace('/[^0-9]/', '', $test_mobile);
    echo "<p><strong>Cleaned Mobile:</strong> '$clean_mobile'</p>";
    echo "<p><strong>Length:</strong> " . strlen($clean_mobile) . "</p>";
    
    // Validation checks
    $length_valid = strlen($clean_mobile) == 11;
    $prefix_valid = preg_match('/^01/', $clean_mobile);
    $overall_valid = $length_valid && $prefix_valid;
    
    echo "<p><strong>Length Check (11 digits):</strong> " . ($length_valid ? '✅ Valid' : '❌ Invalid') . "</p>";
    echo "<p><strong>Prefix Check (starts with 01):</strong> " . ($prefix_valid ? '✅ Valid' : '❌ Invalid') . "</p>";
    echo "<p><strong>Overall Validation:</strong> " . ($overall_valid ? '✅ Valid' : '❌ Invalid') . "</p>";
    
    // Operator detection
    if (strlen($clean_mobile) >= 4) {
        $prefix = substr($clean_mobile, 0, 4);
        $operator = getOperatorName($prefix);
        echo "<p><strong>Detected Operator:</strong> $operator</p>";
    }
    
    echo "</div>";
}

// Test common mobile formats
$test_mobiles = [
    '01712345678',       // Grameenphone
    '01812345678',       // Robi
    '01912345678',       // Banglalink
    '01512345678',       // Teletalk
    '01612345678',       // Airtel
    '+8801712345678',    // With country code
    '8801712345678',     // With country code (no +)
    '017-1234-5678',     // With dashes
    '017 1234 5678',     // With spaces
    '০১৭১২৩৪৫৬৭৮',        // Bengali digits
    '1712345678',        // Without 0
    '017123456789',      // 12 digits
    '0171234567',        // 10 digits
    '02123456789',       // Landline format
];

echo "<h3>🧪 Common Mobile Format Tests:</h3>";
echo "<div style='background: #e7f3ff; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
echo "<tr style='background: #f8f9fa;'>";
echo "<th style='padding: 10px;'>Mobile Number</th>";
echo "<th style='padding: 10px;'>Cleaned</th>";
echo "<th style='padding: 10px;'>Length</th>";
echo "<th style='padding: 10px;'>Prefix</th>";
echo "<th style='padding: 10px;'>Valid</th>";
echo "<th style='padding: 10px;'>Operator</th>";
echo "</tr>";

foreach ($test_mobiles as $mobile) {
    $clean_mobile = preg_replace('/[^0-9]/', '', $mobile);
    $length_valid = strlen($clean_mobile) == 11;
    $prefix_valid = preg_match('/^01/', $clean_mobile);
    $overall_valid = $length_valid && $prefix_valid;
    $operator = strlen($clean_mobile) >= 4 ? getOperatorName(substr($clean_mobile, 0, 4)) : 'Unknown';
    
    echo "<tr>";
    echo "<td style='padding: 5px;'>" . htmlspecialchars($mobile) . "</td>";
    echo "<td style='padding: 5px;'>" . htmlspecialchars($clean_mobile) . "</td>";
    echo "<td style='padding: 5px; color: " . ($length_valid ? 'green' : 'red') . ";'>" . strlen($clean_mobile) . "</td>";
    echo "<td style='padding: 5px; color: " . ($prefix_valid ? 'green' : 'red') . ";'>" . substr($clean_mobile, 0, 2) . "</td>";
    echo "<td style='padding: 5px; color: " . ($overall_valid ? 'green' : 'red') . ";'>" . ($overall_valid ? '✅' : '❌') . "</td>";
    echo "<td style='padding: 5px;'>" . $operator . "</td>";
    echo "</tr>";
}

echo "</table>";
echo "</div>";

function getOperatorName($prefix) {
    $operators = [
        '0171' => 'Grameenphone',
        '0172' => 'Grameenphone', 
        '0173' => 'Grameenphone',
        '0174' => 'Grameenphone',
        '0175' => 'Grameenphone',
        '0176' => 'Grameenphone',
        '0177' => 'Grameenphone',
        '0178' => 'Grameenphone',
        '0179' => 'Grameenphone',
        '0181' => 'Robi',
        '0182' => 'Robi',
        '0183' => 'Robi',
        '0184' => 'Robi',
        '0185' => 'Robi',
        '0186' => 'Robi',
        '0187' => 'Robi',
        '0188' => 'Robi',
        '0189' => 'Robi',
        '0191' => 'Banglalink',
        '0192' => 'Banglalink',
        '0193' => 'Banglalink',
        '0194' => 'Banglalink',
        '0195' => 'Banglalink',
        '0196' => 'Banglalink',
        '0197' => 'Banglalink',
        '0198' => 'Banglalink',
        '0199' => 'Banglalink',
        '0151' => 'Teletalk',
        '0152' => 'Teletalk',
        '0153' => 'Teletalk',
        '0154' => 'Teletalk',
        '0155' => 'Teletalk',
        '0156' => 'Teletalk',
        '0157' => 'Teletalk',
        '0158' => 'Teletalk',
        '0159' => 'Teletalk',
        '0161' => 'Airtel',
        '0162' => 'Airtel',
        '0163' => 'Airtel',
        '0164' => 'Airtel',
        '0165' => 'Airtel',
        '0166' => 'Airtel',
        '0167' => 'Airtel',
        '0168' => 'Airtel',
        '0169' => 'Airtel',
    ];
    
    return $operators[$prefix] ?? 'Unknown Operator';
}

echo "<h3>📋 Mobile Number Format Guide:</h3>";
echo "<div style='background: #d4edda; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
echo "<h4>✅ Accepted Formats:</h4>";
echo "<ul>";
echo "<li><strong>Standard:</strong> 01712345678 (11 digits starting with 01)</li>";
echo "<li><strong>With Country Code:</strong> +8801712345678 or 8801712345678</li>";
echo "<li><strong>With Spaces:</strong> 017 1234 5678</li>";
echo "<li><strong>With Dashes:</strong> 017-1234-5678</li>";
echo "<li><strong>Bengali Digits:</strong> ০১৭১২৩৪৫৬৭৮</li>";
echo "</ul>";

echo "<h4>❌ Not Accepted:</h4>";
echo "<ul>";
echo "<li><strong>Without 0:</strong> 1712345678 (10 digits)</li>";
echo "<li><strong>Too Long:</strong> 017123456789 (12+ digits)</li>";
echo "<li><strong>Wrong Prefix:</strong> 02123456789 (landline)</li>";
echo "<li><strong>Too Short:</strong> 0171234567 (10 digits)</li>";
echo "</ul>";

echo "<h4>📱 Operator Prefixes:</h4>";
echo "<ul>";
echo "<li><strong>Grameenphone:</strong> 017x-xxxx-xxx</li>";
echo "<li><strong>Robi:</strong> 018x-xxxx-xxx</li>";
echo "<li><strong>Banglalink:</strong> 019x-xxxx-xxx</li>";
echo "<li><strong>Teletalk:</strong> 015x-xxxx-xxx</li>";
echo "<li><strong>Airtel:</strong> 016x-xxxx-xxx</li>";
echo "</ul>";
echo "</div>";
?>

<!DOCTYPE html>
<html>
<head>
    <title>Mobile Validator</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
</head>
<body>

<div class="container mt-4">
    <div class="row">
        <div class="col-md-6">
            <div class="card">
                <div class="card-header">
                    <h5>🧪 Test Your Mobile Number</h5>
                </div>
                <div class="card-body">
                    <form method="POST">
                        <div class="mb-3">
                            <label for="mobile_value" class="form-label">Enter Mobile Number to Test</label>
                            <input type="text" class="form-control" name="mobile_value" placeholder="Enter mobile number" required>
                            <div class="form-text">Try: 01712345678, +8801712345678, 017-1234-5678</div>
                        </div>
                        <button type="submit" name="test_mobile" class="btn btn-primary">
                            <i class="fas fa-check"></i> Validate Mobile
                        </button>
                    </form>
                </div>
            </div>
        </div>
        
        <div class="col-md-6">
            <div class="card">
                <div class="card-header">
                    <h5>🔧 Quick Actions</h5>
                </div>
                <div class="card-body">
                    <a href="nid-validator.php" class="btn btn-info me-2">
                        <i class="fas fa-id-card"></i> NID Validator
                    </a>
                    <a href="test-template.php" class="btn btn-success me-2">
                        <i class="fas fa-download"></i> Test Template
                    </a>
                    <a href="voters.php" class="btn btn-secondary">
                        <i class="fas fa-users"></i> Back to Voters
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>

</body>
</html>
