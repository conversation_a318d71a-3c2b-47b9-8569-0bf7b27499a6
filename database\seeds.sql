-- Sample Data for Dynamic Realtime Online Voting System
-- Run this after importing schema.sql

-- Insert demo users (passwords are hashed versions of 'password123')
INSERT INTO `users` (`username`, `email`, `password`, `first_name`, `last_name`, `role`, `is_active`, `email_verified`) VALUES
('admin', '<EMAIL>', '$2y$12$LQv3c1yqBWVHxkd0LHAkCOYz6TtxMQJqhN8/LewdBPj6hsxq9S7jG', 'System', 'Administrator', 'admin', 1, 1),
('john_doe', '<EMAIL>', '$2y$12$LQv3c1yqBWVHxkd0LHAkCOYz6TtxMQJqhN8/LewdBPj6hsxq9S7jG', 'John', 'Doe', 'user', 1, 1),
('jane_smith', '<EMAIL>', '$2y$12$LQv3c1yqBWVHxkd0LHAkCOYz6TtxMQJqhN8/LewdBPj6hsxq9S7jG', 'Jane', 'Smith', 'user', 1, 1),
('moderator', '<EMAIL>', '$2y$12$LQv3c1yqBWVHxkd0LHAkCOYz6TtxMQJqhN8/LewdBPj6hsxq9S7jG', 'Site', 'Moderator', 'moderator', 1, 1),
('demo_user', '<EMAIL>', '$2y$12$LQv3c1yqBWVHxkd0LHAkCOYz6TtxMQJqhN8/LewdBPj6hsxq9S7jG', 'Demo', 'User', 'user', 1, 1);

-- Insert sample polls
INSERT INTO `polls` (`title`, `description`, `type`, `settings`, `created_by`, `is_active`, `is_public`, `created_at`) VALUES
('What is your favorite programming language?', 'Help us understand the community preferences for programming languages in 2024.', 'single_choice', '{"allowAnonymous": true, "requireAuth": false, "allowMultipleVotes": false, "showResults": "after_vote", "ipRestriction": false, "customization": {"theme": "default", "backgroundColor": "#ffffff", "textColor": "#333333", "accentColor": "#0d6efd"}}', 2, 1, 1, NOW() - INTERVAL 2 DAY),

('Which features should we prioritize?', 'Vote for the features you would like to see implemented first in our next update.', 'multiple_choice', '{"allowAnonymous": true, "requireAuth": false, "allowMultipleVotes": false, "showResults": "after_vote", "ipRestriction": false, "customization": {"theme": "default", "backgroundColor": "#ffffff", "textColor": "#333333", "accentColor": "#28a745"}}', 3, 1, 1, NOW() - INTERVAL 1 DAY),

('Rate our customer service', 'Please rate your experience with our customer service team.', 'rating_scale', '{"allowAnonymous": true, "requireAuth": false, "allowMultipleVotes": false, "showResults": "after_end", "ipRestriction": false, "customization": {"theme": "default", "backgroundColor": "#ffffff", "textColor": "#333333", "accentColor": "#ffc107"}}', 1, 1, 1, NOW() - INTERVAL 3 HOUR),

('Best time for team meetings?', 'When would be the most convenient time for our weekly team meetings?', 'single_choice', '{"allowAnonymous": false, "requireAuth": true, "allowMultipleVotes": false, "showResults": "always", "ipRestriction": false, "customization": {"theme": "default", "backgroundColor": "#ffffff", "textColor": "#333333", "accentColor": "#6f42c1"}}', 4, 1, 1, NOW() - INTERVAL 6 HOUR),

('Feedback on new website design', 'What do you think about our new website design? Please share your thoughts.', 'text_response', '{"allowAnonymous": true, "requireAuth": false, "allowMultipleVotes": false, "showResults": "never", "ipRestriction": false, "customization": {"theme": "default", "backgroundColor": "#ffffff", "textColor": "#333333", "accentColor": "#dc3545"}}', 2, 1, 1, NOW() - INTERVAL 12 HOUR);

-- Insert poll options for choice-based polls
INSERT INTO `poll_options` (`poll_id`, `option_text`, `option_order`) VALUES
-- Programming language poll options
(1, 'JavaScript', 1),
(1, 'Python', 2),
(1, 'Java', 3),
(1, 'C#', 4),
(1, 'PHP', 5),
(1, 'Go', 6),
(1, 'Rust', 7),
(1, 'TypeScript', 8),

-- Feature prioritization poll options
(2, 'Dark mode theme', 1),
(2, 'Mobile app', 2),
(2, 'Advanced analytics', 3),
(2, 'Real-time collaboration', 4),
(2, 'API integration', 5),
(2, 'Custom branding', 6),

-- Team meeting time poll options
(4, 'Monday 9:00 AM', 1),
(4, 'Tuesday 2:00 PM', 2),
(4, 'Wednesday 10:00 AM', 3),
(4, 'Thursday 3:00 PM', 4),
(4, 'Friday 11:00 AM', 5);

-- Insert sample votes
INSERT INTO `votes` (`poll_id`, `user_id`, `session_id`, `ip_address`, `selected_options`, `text_response`, `rating`, `created_at`) VALUES
-- Votes for programming language poll
(1, 2, NULL, '*************', '[1]', NULL, NULL, NOW() - INTERVAL 1 DAY),
(1, 3, NULL, '*************', '[2]', NULL, NULL, NOW() - INTERVAL 1 DAY),
(1, 4, NULL, '*************', '[1]', NULL, NULL, NOW() - INTERVAL 23 HOUR),
(1, 5, NULL, '*************', '[3]', NULL, NULL, NOW() - INTERVAL 22 HOUR),
(1, NULL, 'anon_1234567890', '*************', '[2]', NULL, NULL, NOW() - INTERVAL 20 HOUR),
(1, NULL, 'anon_1234567891', '*************', '[1]', NULL, NULL, NOW() - INTERVAL 18 HOUR),
(1, NULL, 'anon_1234567892', '*************', '[5]', NULL, NULL, NOW() - INTERVAL 16 HOUR),

-- Votes for feature prioritization poll
(2, 2, NULL, '*************', '[1, 3]', NULL, NULL, NOW() - INTERVAL 20 HOUR),
(2, 3, NULL, '*************', '[2, 4, 6]', NULL, NULL, NOW() - INTERVAL 18 HOUR),
(2, 4, NULL, '*************', '[1, 2]', NULL, NULL, NOW() - INTERVAL 16 HOUR),
(2, NULL, 'anon_2234567890', '***********07', '[3, 5]', NULL, NULL, NOW() - INTERVAL 14 HOUR),

-- Votes for customer service rating poll
(3, 2, NULL, '*************', NULL, NULL, 8, NOW() - INTERVAL 2 HOUR),
(3, 3, NULL, '*************', NULL, NULL, 9, NOW() - INTERVAL 1 HOUR),
(3, NULL, 'anon_3234567890', '***********08', NULL, NULL, 7, NOW() - INTERVAL 30 MINUTE),

-- Votes for team meeting poll
(4, 2, NULL, '*************', '[1]', NULL, NULL, NOW() - INTERVAL 5 HOUR),
(4, 3, NULL, '*************', '[3]', NULL, NULL, NOW() - INTERVAL 4 HOUR),
(4, 4, NULL, '*************', '[1]', NULL, NULL, NOW() - INTERVAL 3 HOUR),

-- Text responses for feedback poll
(5, 2, NULL, '*************', NULL, 'The new design looks great! I love the modern layout and improved navigation.', NULL, NOW() - INTERVAL 10 HOUR),
(5, 3, NULL, '*************', NULL, 'Good overall, but the color scheme could be improved. Maybe add more contrast?', NULL, NOW() - INTERVAL 8 HOUR),
(5, NULL, 'anon_5234567890', '*************', NULL, 'Very user-friendly and responsive. Great job on the mobile version!', NULL, NOW() - INTERVAL 6 HOUR);

-- Initialize analytics for all polls
INSERT INTO `poll_analytics` (`poll_id`, `views`, `unique_voters`, `completion_rate`, `average_time_to_vote`) VALUES
(1, 45, 7, 85.5, 45),
(2, 32, 4, 92.3, 62),
(3, 28, 3, 78.9, 38),
(4, 18, 3, 100.0, 25),
(5, 25, 3, 88.2, 95);

-- Insert some activity logs
INSERT INTO `activity_logs` (`user_id`, `action`, `resource`, `resource_id`, `ip_address`, `created_at`) VALUES
(1, 'LOGIN', 'user', 1, '***********', NOW() - INTERVAL 1 HOUR),
(2, 'CREATE', 'poll', 1, '*************', NOW() - INTERVAL 2 DAY),
(2, 'VOTE', 'poll', 1, '*************', NOW() - INTERVAL 1 DAY),
(3, 'CREATE', 'poll', 2, '*************', NOW() - INTERVAL 1 DAY),
(3, 'VOTE', 'poll', 2, '*************', NOW() - INTERVAL 18 HOUR),
(4, 'LOGIN', 'user', 4, '*************', NOW() - INTERVAL 30 MINUTE);

-- Insert sample notifications
INSERT INTO `notifications` (`user_id`, `type`, `title`, `message`, `data`, `created_at`) VALUES
(2, 'vote_received', 'New Vote on Your Poll', 'Someone voted on "What is your favorite programming language?"', '{"poll_id": 1, "poll_title": "What is your favorite programming language?"}', NOW() - INTERVAL 2 HOUR),
(3, 'vote_received', 'New Vote on Your Poll', 'Someone voted on "Which features should we prioritize?"', '{"poll_id": 2, "poll_title": "Which features should we prioritize?"}', NOW() - INTERVAL 1 HOUR),
(1, 'system_announcement', 'Welcome to OVS', 'Welcome to the Online Voting System! Start creating polls and gathering opinions.', '{}', NOW() - INTERVAL 1 DAY);

COMMIT;
