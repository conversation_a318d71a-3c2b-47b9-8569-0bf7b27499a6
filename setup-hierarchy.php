<?php
/**
 * Administrative Hierarchy Setup
 * Sets up Bangladesh administrative structure
 */

require_once 'config/config.php';

// Check if user is admin
if (!isLoggedIn() || !isAdmin()) {
    echo "<h2>Access Denied</h2>";
    echo "<p>Please login as admin first: <a href='auth/login.php'>Login</a></p>";
    exit;
}

echo "<h2>🏛️ প্রশাসনিক হায়ারার্কি সেটআপ</h2>";

try {
    $db = getDB();
    
    if (isset($_POST['setup_hierarchy'])) {
        echo "<h3>🔧 Setting up administrative hierarchy...</h3>";
        
        // Update areas table enum
        $db->exec("ALTER TABLE `areas` MODIFY COLUMN `type` enum('division','district','upazila','municipality','union','village','city','ward') NOT NULL DEFAULT 'ward'");
        echo "<div style='background: #d4edda; padding: 10px; margin: 5px 0; border-radius: 3px;'>✅ Updated areas table structure</div>";
        
        // Clear existing data
        $db->exec("DELETE FROM `areas`");
        echo "<div style='background: #fff3cd; padding: 10px; margin: 5px 0; border-radius: 3px;'>⚠️ Cleared existing areas data</div>";
        
        // Insert divisions
        $divisions = [
            ['Dhaka Division', 'ঢাকা বিভাগ', 'DIV-01'],
            ['Chittagong Division', 'চট্টগ্রাম বিভাগ', 'DIV-02'],
            ['Rajshahi Division', 'রাজশাহী বিভাগ', 'DIV-03'],
            ['Khulna Division', 'খুলনা বিভাগ', 'DIV-04']
        ];
        
        $stmt = $db->prepare("INSERT INTO areas (name, name_bn, type, code, description) VALUES (?, ?, 'division', ?, ?)");
        foreach ($divisions as $div) {
            $stmt->execute([$div[0], $div[1], $div[2], $div[1]]);
        }
        echo "<div style='background: #d4edda; padding: 10px; margin: 5px 0; border-radius: 3px;'>✅ Created " . count($divisions) . " divisions</div>";
        
        // Insert districts under Dhaka Division
        $districts = [
            ['Dhaka District', 'ঢাকা জেলা', 'DIS-01'],
            ['Gazipur District', 'গাজীপুর জেলা', 'DIS-02'],
            ['Narayanganj District', 'নারায়ণগঞ্জ জেলা', 'DIS-03']
        ];
        
        $stmt = $db->prepare("INSERT INTO areas (name, name_bn, type, parent_id, code, description) VALUES (?, ?, 'district', 1, ?, ?)");
        foreach ($districts as $dist) {
            $stmt->execute([$dist[0], $dist[1], $dist[2], $dist[1]]);
        }
        echo "<div style='background: #d4edda; padding: 10px; margin: 5px 0; border-radius: 3px;'>✅ Created " . count($districts) . " districts</div>";
        
        // Insert upazilas under Dhaka District
        $upazilas = [
            ['Dhanmondi Upazila', 'ধানমন্ডি উপজেলা', 'UPZ-01'],
            ['Gulshan Upazila', 'গুলশান উপজেলা', 'UPZ-02'],
            ['Ramna Upazila', 'রমনা উপজেলা', 'UPZ-03']
        ];
        
        $stmt = $db->prepare("INSERT INTO areas (name, name_bn, type, parent_id, code, description) VALUES (?, ?, 'upazila', 5, ?, ?)");
        foreach ($upazilas as $upz) {
            $stmt->execute([$upz[0], $upz[1], $upz[2], $upz[1]]);
        }
        echo "<div style='background: #d4edda; padding: 10px; margin: 5px 0; border-radius: 3px;'>✅ Created " . count($upazilas) . " upazilas</div>";
        
        // Insert municipality under Dhanmondi Upazila
        $db->exec("INSERT INTO areas (name, name_bn, type, parent_id, code, description) VALUES ('Dhanmondi Municipality', 'ধানমন্ডি পৌরসভা', 'municipality', 8, 'MUN-01', 'ধানমন্ডি পৌরসভা')");
        
        // Insert unions under Gulshan Upazila
        $unions = [
            ['Gulshan Union', 'গুলশান ইউনিয়ন', 'UNI-01'],
            ['Banani Union', 'বনানী ইউনিয়ন', 'UNI-02']
        ];
        
        $stmt = $db->prepare("INSERT INTO areas (name, name_bn, type, parent_id, code, description) VALUES (?, ?, 'union', 9, ?, ?)");
        foreach ($unions as $union) {
            $stmt->execute([$union[0], $union[1], $union[2], $union[1]]);
        }
        echo "<div style='background: #d4edda; padding: 10px; margin: 5px 0; border-radius: 3px;'>✅ Created municipality and unions</div>";
        
        // Insert villages
        $villages = [
            ['Gulshan Village', 'গুলশান গ্রাম', 'village', 12, 'VIL-01'],
            ['Banani Village', 'বনানী গ্রাম', 'village', 13, 'VIL-02']
        ];
        
        $stmt = $db->prepare("INSERT INTO areas (name, name_bn, type, parent_id, code, description) VALUES (?, ?, ?, ?, ?, ?)");
        foreach ($villages as $village) {
            $stmt->execute([$village[0], $village[1], $village[2], $village[3], $village[4], $village[1]]);
        }
        
        // Insert city under Ramna Upazila
        $db->exec("INSERT INTO areas (name, name_bn, type, parent_id, code, description) VALUES ('Ramna City', 'রমনা শহর', 'city', 10, 'CTY-01', 'রমনা শহর এলাকা')");
        echo "<div style='background: #d4edda; padding: 10px; margin: 5px 0; border-radius: 3px;'>✅ Created villages and city</div>";
        
        // Insert wards
        $wards = [
            // Dhanmondi Municipality wards
            ['Dhanmondi Ward 01', 'ধানমন্ডি ওয়ার্ড ০১', 11, 'WAR-01'],
            ['Dhanmondi Ward 02', 'ধানমন্ডি ওয়ার্ড ০২', 11, 'WAR-02'],
            ['Dhanmondi Ward 03', 'ধানমন্ডি ওয়ার্ড ০৩', 11, 'WAR-03'],
            // Gulshan Village wards
            ['Gulshan Ward 01', 'গুলশান ওয়ার্ড ০১', 14, 'WAR-04'],
            ['Gulshan Ward 02', 'গুলশান ওয়ার্ড ০২', 14, 'WAR-05'],
            // Banani Village wards
            ['Banani Ward 01', 'বনানী ওয়ার্ড ০১', 15, 'WAR-06'],
            ['Banani Ward 02', 'বনানী ওয়ার্ড ০২', 15, 'WAR-07'],
            // Ramna City wards
            ['Ramna Ward 01', 'রমনা ওয়ার্ড ০১', 16, 'WAR-08'],
            ['Ramna Ward 02', 'রমনা ওয়ার্ড ০২', 16, 'WAR-09']
        ];
        
        $stmt = $db->prepare("INSERT INTO areas (name, name_bn, type, parent_id, code, description) VALUES (?, ?, 'ward', ?, ?, ?)");
        foreach ($wards as $ward) {
            $stmt->execute([$ward[0], $ward[1], $ward[2], $ward[3], $ward[1]]);
        }
        echo "<div style='background: #d4edda; padding: 10px; margin: 5px 0; border-radius: 3px;'>✅ Created " . count($wards) . " wards</div>";
        
        echo "<div style='background: #d4edda; padding: 15px; border-radius: 5px; margin: 15px 0;'>";
        echo "<h4>🎉 Administrative Hierarchy Setup Complete!</h4>";
        echo "<p>বাংলাদেশের প্রশাসনিক কাঠামো অনুযায়ী সম্পূর্ণ হায়ারার্কি তৈরি হয়েছে।</p>";
        echo "</div>";
    }
    
    // Show current status
    $stmt = $db->query("
        SELECT type, COUNT(*) as count 
        FROM areas 
        WHERE is_active = 1 
        GROUP BY type 
        ORDER BY 
            CASE type 
                WHEN 'division' THEN 1 
                WHEN 'district' THEN 2 
                WHEN 'upazila' THEN 3 
                WHEN 'municipality' THEN 4
                WHEN 'union' THEN 4
                WHEN 'village' THEN 5
                WHEN 'city' THEN 5
                WHEN 'ward' THEN 6 
            END
    ");
    $current_stats = $stmt->fetchAll();
    
    echo "<h3>📊 Current Administrative Structure:</h3>";
    if (!empty($current_stats)) {
        $type_labels = [
            'division' => 'বিভাগ',
            'district' => 'জেলা',
            'upazila' => 'উপজেলা',
            'municipality' => 'পৌরসভা',
            'union' => 'ইউনিয়ন',
            'village' => 'গ্রাম',
            'city' => 'শহর',
            'ward' => 'ওয়ার্ড'
        ];
        
        echo "<table border='1' style='border-collapse: collapse; width: 100%; margin: 10px 0;'>";
        echo "<tr style='background: #f8f9fa;'>";
        echo "<th style='padding: 8px;'>Type</th>";
        echo "<th style='padding: 8px;'>Count</th>";
        echo "</tr>";
        
        foreach ($current_stats as $stat) {
            $label = $type_labels[$stat['type']] ?? ucfirst($stat['type']);
            echo "<tr>";
            echo "<td style='padding: 8px;'>$label ({$stat['type']})</td>";
            echo "<td style='padding: 8px; text-align: center;'><strong>{$stat['count']}</strong></td>";
            echo "</tr>";
        }
        echo "</table>";
    } else {
        echo "<p style='color: orange;'>⚠️ No administrative areas found!</p>";
    }
    
    // Show setup button if needed
    if (empty($current_stats) || count($current_stats) < 6) {
        echo "<form method='POST'>";
        echo "<button type='submit' name='setup_hierarchy' style='background: #28a745; color: white; padding: 15px 30px; border: none; border-radius: 5px; font-size: 16px; cursor: pointer;'>";
        echo "🏛️ Setup Administrative Hierarchy";
        echo "</button>";
        echo "</form>";
        
        echo "<div style='background: #e7f3ff; padding: 15px; border-radius: 5px; margin: 15px 0;'>";
        echo "<h4>📋 What will be created:</h4>";
        echo "<ul>";
        echo "<li>৪টি বিভাগ (Divisions)</li>";
        echo "<li>৩টি জেলা (Districts)</li>";
        echo "<li>৩টি উপজেলা (Upazilas)</li>";
        echo "<li>১টি পৌরসভা + ২টি ইউনিয়ন</li>";
        echo "<li>২টি গ্রাম + ১টি শহর</li>";
        echo "<li>৯টি ওয়ার্ড (Wards)</li>";
        echo "</ul>";
        echo "<p><strong>Total:</strong> Complete Bangladesh administrative structure sample</p>";
        echo "</div>";
    }
    
} catch (Exception $e) {
    echo "<div style='background: #f8d7da; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
    echo "<h4>❌ Database Error!</h4>";
    echo "<p>Error: " . $e->getMessage() . "</p>";
    echo "</div>";
}

echo "<br><h3>🔗 Next Steps:</h3>";
echo "<ol>";
echo "<li><a href='admin/areas.php'>View Administrative Areas</a></li>";
echo "<li><a href='admin/voters.php'>Create Voters</a></li>";
echo "<li><a href='polls/create.php'>Create Area-Based Polls</a></li>";
echo "<li><a href='admin/'>Admin Dashboard</a></li>";
echo "</ol>";

echo "<br><div style='background: #e2e3e5; padding: 10px; border-radius: 5px;'>";
echo "<strong>Note:</strong> Delete this file after setup for security.";
echo "</div>";
?>
