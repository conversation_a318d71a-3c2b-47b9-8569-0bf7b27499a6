<?php
/**
 * Get Areas API
 * Fetch areas based on parent selection for dependent dropdowns
 */

header('Content-Type: application/json');
require_once '../config/config.php';

// Check if user is logged in
if (!isLoggedIn()) {
    http_response_code(401);
    echo json_encode(['success' => false, 'message' => 'Unauthorized']);
    exit;
}

$action = $_GET['action'] ?? '';
$parent_id = intval($_GET['parent_id'] ?? 0);
$type = $_GET['type'] ?? '';

try {
    $db = getDB();
    
    switch ($action) {
        case 'get_children':
            // Get child areas based on parent_id
            if ($parent_id > 0) {
                $stmt = $db->prepare("
                    SELECT id, name, name_bn, type 
                    FROM areas 
                    WHERE parent_id = ? AND is_active = 1 
                    ORDER BY name
                ");
                $stmt->execute([$parent_id]);
            } else {
                // Get root level areas (divisions)
                $stmt = $db->prepare("
                    SELECT id, name, name_bn, type 
                    FROM areas 
                    WHERE parent_id IS NULL AND is_active = 1 
                    ORDER BY name
                ");
                $stmt->execute();
            }
            
            $areas = $stmt->fetchAll();
            echo json_encode([
                'success' => true,
                'areas' => $areas
            ]);
            break;
            
        case 'get_by_type':
            // Get areas by type
            if (!empty($type)) {
                $stmt = $db->prepare("
                    SELECT id, name, name_bn, type, parent_id
                    FROM areas 
                    WHERE type = ? AND is_active = 1 
                    ORDER BY name
                ");
                $stmt->execute([$type]);
                
                $areas = $stmt->fetchAll();
                echo json_encode([
                    'success' => true,
                    'areas' => $areas
                ]);
            } else {
                echo json_encode(['success' => false, 'message' => 'Type required']);
            }
            break;
            
        case 'get_hierarchy':
            // Get full hierarchy for a specific area
            $area_id = intval($_GET['area_id'] ?? 0);
            if ($area_id > 0) {
                $hierarchy = getAreaHierarchy($db, $area_id);
                echo json_encode([
                    'success' => true,
                    'hierarchy' => $hierarchy
                ]);
            } else {
                echo json_encode(['success' => false, 'message' => 'Area ID required']);
            }
            break;
            
        case 'get_all_divisions':
            // Get all divisions
            $stmt = $db->prepare("
                SELECT id, name, name_bn 
                FROM areas 
                WHERE type = 'division' AND is_active = 1 
                ORDER BY name
            ");
            $stmt->execute();
            
            $divisions = $stmt->fetchAll();
            echo json_encode([
                'success' => true,
                'divisions' => $divisions
            ]);
            break;
            
        default:
            http_response_code(400);
            echo json_encode(['success' => false, 'message' => 'Invalid action']);
            break;
    }
    
} catch (Exception $e) {
    error_log("Areas API error: " . $e->getMessage());
    http_response_code(500);
    echo json_encode(['success' => false, 'message' => 'Server error']);
}

/**
 * Get complete hierarchy for an area
 */
function getAreaHierarchy($db, $area_id) {
    $hierarchy = [];
    $current_id = $area_id;
    
    while ($current_id) {
        $stmt = $db->prepare("
            SELECT id, name, name_bn, type, parent_id 
            FROM areas 
            WHERE id = ?
        ");
        $stmt->execute([$current_id]);
        $area = $stmt->fetch();
        
        if ($area) {
            array_unshift($hierarchy, $area);
            $current_id = $area['parent_id'];
        } else {
            break;
        }
    }
    
    return $hierarchy;
}
?>
