<?php
/**
 * Extended Sidebar Demo Page
 * বর্ধিত সাইডবার ডেমো পেজ
 */

require_once 'config/config.php';

$page_title = '🚀 বর্ধিত সাইডবার ডেমো - Extended Sidebar Demo';
$page_subtitle = 'সাবমেনু, ব্যাজ এবং আরো অনেক ফিচার সহ';
$show_top_nav = true;

// Start output buffering for content
ob_start();
?>

<div class="container-fluid py-4">
    <!-- Header Section -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card border-0" style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white;">
                <div class="card-body text-center py-5">
                    <h1 class="display-3 mb-3" style="text-shadow: 2px 2px 4px rgba(0,0,0,0.3);">
                        🚀 বর্ধিত সাইডবার
                    </h1>
                    <p class="lead mb-4" style="font-size: 1.3rem;">
                        সাবমেনু, ব্যাজ, নোটিফিকেশন এবং আরো অনেক advanced ফিচার
                    </p>
                    <div class="d-flex gap-3 justify-content-center flex-wrap">
                        <span class="badge px-4 py-2" style="background: linear-gradient(45deg, #ff6b6b, #feca57); font-size: 1rem;">
                            <i class="fas fa-sitemap"></i> Submenu Support
                        </span>
                        <span class="badge px-4 py-2" style="background: linear-gradient(45deg, #48dbfb, #0abde3); font-size: 1rem;">
                            <i class="fas fa-bell"></i> Badge Notifications
                        </span>
                        <span class="badge px-4 py-2" style="background: linear-gradient(45deg, #ff9ff3, #f368e0); font-size: 1rem;">
                            <i class="fas fa-layer-group"></i> Categorized Menu
                        </span>
                        <span class="badge px-4 py-2" style="background: linear-gradient(45deg, #4ecdc4, #44a08d); font-size: 1rem;">
                            <i class="fas fa-tools"></i> 50+ Options
                        </span>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Feature Overview -->
    <div class="row mb-4">
        <div class="col-md-3 mb-3">
            <div class="card h-100 border-0" style="background: linear-gradient(135deg, #ff6b6b, #feca57); color: white;">
                <div class="card-body text-center">
                    <div class="mb-3">
                        <i class="fas fa-sitemap fa-4x" style="text-shadow: 2px 2px 4px rgba(0,0,0,0.3);"></i>
                    </div>
                    <h5 class="card-title">Submenu System</h5>
                    <p class="card-text">
                        Collapsible submenus যা organize করে related options
                    </p>
                    <ul class="list-unstyled text-start">
                        <li><i class="fas fa-check"></i> Expandable menus</li>
                        <li><i class="fas fa-check"></i> Auto-collapse</li>
                        <li><i class="fas fa-check"></i> Smooth animations</li>
                        <li><i class="fas fa-check"></i> Deep nesting support</li>
                    </ul>
                </div>
            </div>
        </div>
        
        <div class="col-md-3 mb-3">
            <div class="card h-100 border-0" style="background: linear-gradient(135deg, #48dbfb, #0abde3); color: white;">
                <div class="card-body text-center">
                    <div class="mb-3">
                        <i class="fas fa-bell fa-4x" style="text-shadow: 2px 2px 4px rgba(0,0,0,0.3);"></i>
                    </div>
                    <h5 class="card-title">Badge Notifications</h5>
                    <p class="card-text">
                        Real-time notification badges সহ menu items
                    </p>
                    <ul class="list-unstyled text-start">
                        <li><i class="fas fa-check"></i> Colorful badges</li>
                        <li><i class="fas fa-check"></i> Dynamic counts</li>
                        <li><i class="fas fa-check"></i> Multiple colors</li>
                        <li><i class="fas fa-check"></i> Auto-update</li>
                    </ul>
                </div>
            </div>
        </div>
        
        <div class="col-md-3 mb-3">
            <div class="card h-100 border-0" style="background: linear-gradient(135deg, #667eea, #764ba2); color: white;">
                <div class="card-body text-center">
                    <div class="mb-3">
                        <i class="fas fa-layer-group fa-4x" style="text-shadow: 2px 2px 4px rgba(0,0,0,0.3);"></i>
                    </div>
                    <h5 class="card-title">Categorized Menu</h5>
                    <p class="card-text">
                        Organized categories সহ logical grouping
                    </p>
                    <ul class="list-unstyled text-start">
                        <li><i class="fas fa-check"></i> 6 main categories</li>
                        <li><i class="fas fa-check"></i> Role-based items</li>
                        <li><i class="fas fa-check"></i> Visual dividers</li>
                        <li><i class="fas fa-check"></i> Smart organization</li>
                    </ul>
                </div>
            </div>
        </div>
        
        <div class="col-md-3 mb-3">
            <div class="card h-100 border-0" style="background: linear-gradient(135deg, #ff9ff3, #f368e0); color: white;">
                <div class="card-body text-center">
                    <div class="mb-3">
                        <i class="fas fa-tools fa-4x" style="text-shadow: 2px 2px 4px rgba(0,0,0,0.3);"></i>
                    </div>
                    <h5 class="card-title">50+ Options</h5>
                    <p class="card-text">
                        বিস্তৃত functionality সহ comprehensive menu
                    </p>
                    <ul class="list-unstyled text-start">
                        <li><i class="fas fa-check"></i> Main navigation</li>
                        <li><i class="fas fa-check"></i> Admin tools</li>
                        <li><i class="fas fa-check"></i> Communication</li>
                        <li><i class="fas fa-check"></i> Support system</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>

    <!-- Menu Categories -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card border-0" style="background: rgba(255, 255, 255, 0.9); backdrop-filter: blur(10px);">
                <div class="card-header border-0" style="background: linear-gradient(135deg, #4ecdc4, #44a08d); color: white;">
                    <h5 class="mb-0">
                        <i class="fas fa-list"></i> মেনু ক্যাটেগরিসমূহ
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <h6 style="color: #667eea;">📋 মূল ক্যাটেগরি:</h6>
                            <div class="row">
                                <div class="col-md-6">
                                    <ul class="list-unstyled">
                                        <li class="mb-2">
                                            <i class="fas fa-home text-primary"></i>
                                            <strong>মূল নেভিগেশন</strong> (6 items)
                                            <br><small class="text-muted">Dashboard, Polls, Profile</small>
                                        </li>
                                        <li class="mb-2">
                                            <i class="fas fa-crown text-warning"></i>
                                            <strong>অ্যাডমিন প্যানেল</strong> (7 items)
                                            <br><small class="text-muted">User, Poll, Area Management</small>
                                        </li>
                                        <li class="mb-2">
                                            <i class="fas fa-bell text-info"></i>
                                            <strong>যোগাযোগ ও নোটিফিকেশন</strong> (3 items)
                                            <br><small class="text-muted">Messages, Notifications</small>
                                        </li>
                                    </ul>
                                </div>
                                <div class="col-md-6">
                                    <ul class="list-unstyled">
                                        <li class="mb-2">
                                            <i class="fas fa-tools text-success"></i>
                                            <strong>টুলস ও ইউটিলিটি</strong> (5 items)
                                            <br><small class="text-muted">Files, Calendar, Tasks</small>
                                        </li>
                                        <li class="mb-2">
                                            <i class="fas fa-question-circle text-danger"></i>
                                            <strong>সাহায্য ও ডকুমেন্টেশন</strong> (4 items)
                                            <br><small class="text-muted">Help, Docs, Contact</small>
                                        </li>
                                        <li class="mb-2">
                                            <i class="fas fa-cog text-secondary"></i>
                                            <strong>অ্যাডমিন টুলস</strong> (7 items)
                                            <br><small class="text-muted">Database, Logs, Security</small>
                                        </li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <h6 style="color: #ff6b6b;">🎯 বিশেষ ফিচার:</h6>
                            <div class="row">
                                <div class="col-md-6">
                                    <ul class="list-unstyled">
                                        <li class="mb-2">
                                            <span class="badge bg-danger">5</span>
                                            <strong>নোটিফিকেশন ব্যাজ</strong>
                                            <br><small class="text-muted">Real-time counts</small>
                                        </li>
                                        <li class="mb-2">
                                            <span class="badge bg-success">নতুন</span>
                                            <strong>স্ট্যাটাস ব্যাজ</strong>
                                            <br><small class="text-muted">Feature highlights</small>
                                        </li>
                                        <li class="mb-2">
                                            <i class="fas fa-chevron-down text-primary"></i>
                                            <strong>সাবমেনু আইকন</strong>
                                            <br><small class="text-muted">Expandable indicators</small>
                                        </li>
                                    </ul>
                                </div>
                                <div class="col-md-6">
                                    <ul class="list-unstyled">
                                        <li class="mb-2">
                                            <i class="fas fa-user-shield text-warning"></i>
                                            <strong>Role-based Menu</strong>
                                            <br><small class="text-muted">Admin vs User items</small>
                                        </li>
                                        <li class="mb-2">
                                            <i class="fas fa-mobile-alt text-info"></i>
                                            <strong>Mobile Responsive</strong>
                                            <br><small class="text-muted">Touch-friendly</small>
                                        </li>
                                        <li class="mb-2">
                                            <i class="fas fa-search text-success"></i>
                                            <strong>Smart Search</strong>
                                            <br><small class="text-muted">Quick navigation</small>
                                        </li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Interactive Demo -->
    <div class="row mb-4">
        <div class="col-md-8">
            <div class="card border-0" style="background: rgba(255, 255, 255, 0.9); backdrop-filter: blur(10px);">
                <div class="card-header border-0" style="background: linear-gradient(135deg, #ff6b6b, #feca57); color: white;">
                    <h5 class="mb-0">
                        <i class="fas fa-play"></i> ইন্টারঅ্যাক্টিভ ডেমো
                    </h5>
                </div>
                <div class="card-body">
                    <p>
                        বাম পাশের সাইডবারে নতুন ফিচারগুলো টেস্ট করুন:
                    </p>
                    
                    <h6 style="color: #667eea;">🎯 টেস্ট করার জন্য:</h6>
                    <div class="row">
                        <div class="col-md-6">
                            <ol>
                                <li class="mb-2">
                                    <strong>সাবমেনু:</strong> "পোল ব্রাউজ করুন" বা "নতুন পোল তৈরি" ক্লিক করুন
                                </li>
                                <li class="mb-2">
                                    <strong>ব্যাজ:</strong> "নোটিফিকেশন" এ লাল ব্যাজ দেখুন
                                </li>
                                <li class="mb-2">
                                    <strong>ক্যাটেগরি:</strong> বিভিন্ন divider sections লক্ষ্য করুন
                                </li>
                            </ol>
                        </div>
                        <div class="col-md-6">
                            <ol start="4">
                                <li class="mb-2">
                                    <strong>অ্যানিমেশন:</strong> Menu items এ hover করুন
                                </li>
                                <li class="mb-2">
                                    <strong>স্ক্রোলিং:</strong> সাইডবার scroll করে সব options দেখুন
                                </li>
                                <li class="mb-2">
                                    <strong>Mobile:</strong> Browser width কমিয়ে mobile mode টেস্ট করুন
                                </li>
                            </ol>
                        </div>
                    </div>
                    
                    <div class="alert alert-info mt-3" style="background: linear-gradient(135deg, #48dbfb, #0abde3); color: white; border: none;">
                        <strong>টিপ:</strong> সাবমেনু expand করতে main menu item এ ক্লিক করুন!
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-md-4">
            <div class="card border-0" style="background: rgba(255, 255, 255, 0.9); backdrop-filter: blur(10px);">
                <div class="card-header border-0" style="background: linear-gradient(135deg, #ff9ff3, #f368e0); color: white;">
                    <h5 class="mb-0">
                        <i class="fas fa-chart-bar"></i> পরিসংখ্যান
                    </h5>
                </div>
                <div class="card-body">
                    <div class="mb-3">
                        <div class="d-flex justify-content-between">
                            <strong>মোট মেনু আইটেম:</strong>
                            <span class="badge bg-primary">50+</span>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <div class="d-flex justify-content-between">
                            <strong>সাবমেনু সহ:</strong>
                            <span class="badge bg-success">15</span>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <div class="d-flex justify-content-between">
                            <strong>ব্যাজ সহ:</strong>
                            <span class="badge bg-warning">8</span>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <div class="d-flex justify-content-between">
                            <strong>ক্যাটেগরি:</strong>
                            <span class="badge bg-info">6</span>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <div class="d-flex justify-content-between">
                            <strong>অ্যাডমিন আইটেম:</strong>
                            <span class="badge bg-danger">25</span>
                        </div>
                    </div>
                    
                    <hr>
                    
                    <div class="text-center">
                        <button class="btn btn-sm" style="background: linear-gradient(135deg, #4ecdc4, #44a08d); color: white; border: none;" onclick="countMenuItems()">
                            <i class="fas fa-calculator"></i> Count Items
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Success Message -->
    <div class="row">
        <div class="col-12">
            <div class="card border-0" style="background: linear-gradient(135deg, #4ecdc4, #44a08d); color: white;">
                <div class="card-body text-center py-4">
                    <h3 class="mb-3">
                        🎉 বর্ধিত সাইডবার সফলভাবে কাজ করছে!
                    </h3>
                    <p class="mb-0">
                        সাবমেনু, ব্যাজ, ক্যাটেগরি এবং 50+ অপশন সহ comprehensive navigation system!
                    </p>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
function countMenuItems() {
    const totalItems = $('.nav-item').length;
    const submenuItems = $('.has-submenu').length;
    const badgeItems = $('.nav-link .badge').length;
    const categories = $('.nav-divider').length;
    
    alert(`সাইডবার পরিসংখ্যান:\n\n` +
          `মোট মেনু আইটেম: ${totalItems}\n` +
          `সাবমেনু সহ: ${submenuItems}\n` +
          `ব্যাজ সহ: ${badgeItems}\n` +
          `ক্যাটেগরি: ${categories}\n\n` +
          `সাবমেনু আইটেম: ${$('.submenu-item').length}`);
}

// Auto-demo submenu
$(document).ready(function() {
    setTimeout(() => {
        // Auto-expand first submenu for demo
        $('.has-submenu').first().find('.submenu-toggle').click();
        
        // Show notification
        APP.showAlert('সাবমেনু auto-expanded! 🎯', 'info', 3000);
    }, 2000);
});
</script>

<?php
$content = ob_get_clean();

// Include the sidebar layout
include 'includes/header-with-sidebar.php';
?>
