<?php
/**
 * User Dashboard with Modern Sidebar
 * মডার্ন সাইডবার সহ ব্যবহারকারী ড্যাশবোর্ড
 */

require_once 'config/config.php';

// Check if user is logged in
if (!isLoggedIn()) {
    redirect(APP_URL . '/auth/login.php');
}

// Get user stats
try {
    $db = getDB();
    $user = getCurrentUser();
    
    // Get user's polls
    $stmt = $db->prepare("
        SELECT COUNT(*) as total_polls,
               SUM(CASE WHEN is_active = 1 THEN 1 ELSE 0 END) as active_polls,
               SUM(CASE WHEN end_date < NOW() THEN 1 ELSE 0 END) as ended_polls
        FROM polls 
        WHERE created_by = ?
    ");
    $stmt->execute([$user['id']]);
    $poll_stats = $stmt->fetch();
    
    // Get total votes received
    $stmt = $db->prepare("
        SELECT COUNT(*) as total_votes
        FROM votes v
        JOIN polls p ON v.poll_id = p.id
        WHERE p.created_by = ?
    ");
    $stmt->execute([$user['id']]);
    $vote_stats = $stmt->fetch();
    
    // Get recent polls
    $stmt = $db->prepare("
        SELECT p.*, 
               (SELECT COUNT(*) FROM votes WHERE poll_id = p.id) as vote_count
        FROM polls p 
        WHERE p.created_by = ? 
        ORDER BY p.created_at DESC 
        LIMIT 5
    ");
    $stmt->execute([$user['id']]);
    $recent_polls = $stmt->fetchAll();
    
} catch (Exception $e) {
    $poll_stats = ['total_polls' => 0, 'active_polls' => 0, 'ended_polls' => 0];
    $vote_stats = ['total_votes' => 0];
    $recent_polls = [];
    error_log("Dashboard error: " . $e->getMessage());
}

$page_title = 'ড্যাশবোর্ড - Dashboard';
$show_top_nav = true;

// Start output buffering for content
ob_start();
?>

<div class="container-fluid py-4">
    <!-- Welcome Section -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card bg-gradient-primary text-white">
                <div class="card-body">
                    <div class="row align-items-center">
                        <div class="col-md-8">
                            <h2 class="mb-2">স্বাগতম, <?php echo htmlspecialchars($user['username']); ?>! 🎉</h2>
                            <p class="mb-0 opacity-75">
                                আপনার ভোটিং সিস্টেম ড্যাশবোর্ডে স্বাগতম। এখানে আপনি আপনার সকল পোল এবং কার্যক্রম দেখতে পাবেন।
                            </p>
                        </div>
                        <div class="col-md-4 text-md-end">
                            <div class="d-flex gap-2 justify-content-md-end">
                                <a href="<?php echo APP_URL; ?>/polls/create.php" class="btn btn-light">
                                    <i class="fas fa-plus"></i> নতুন পোল
                                </a>
                                <a href="<?php echo APP_URL; ?>/polls/manage.php" class="btn btn-outline-light">
                                    <i class="fas fa-cog"></i> পোল ব্যবস্থাপনা
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Stats Cards -->
    <div class="row mb-4">
        <div class="col-md-3 mb-3">
            <div class="card border-start-primary">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div class="flex-grow-1">
                            <h5 class="card-title text-primary mb-1">মোট পোল</h5>
                            <h3 class="mb-0"><?php echo number_format($poll_stats['total_polls']); ?></h3>
                        </div>
                        <div class="text-primary">
                            <i class="fas fa-poll fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-md-3 mb-3">
            <div class="card border-start-success">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div class="flex-grow-1">
                            <h5 class="card-title text-success mb-1">সক্রিয় পোল</h5>
                            <h3 class="mb-0"><?php echo number_format($poll_stats['active_polls']); ?></h3>
                        </div>
                        <div class="text-success">
                            <i class="fas fa-play-circle fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-md-3 mb-3">
            <div class="card border-start-warning">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div class="flex-grow-1">
                            <h5 class="card-title text-warning mb-1">সমাপ্ত পোল</h5>
                            <h3 class="mb-0"><?php echo number_format($poll_stats['ended_polls']); ?></h3>
                        </div>
                        <div class="text-warning">
                            <i class="fas fa-stop-circle fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-md-3 mb-3">
            <div class="card border-start-info">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div class="flex-grow-1">
                            <h5 class="card-title text-info mb-1">মোট ভোট</h5>
                            <h3 class="mb-0"><?php echo number_format($vote_stats['total_votes']); ?></h3>
                        </div>
                        <div class="text-info">
                            <i class="fas fa-vote-yea fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Recent Polls -->
    <div class="row">
        <div class="col-md-8 mb-4">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">
                        <i class="fas fa-clock"></i> সাম্প্রতিক পোল
                    </h5>
                    <a href="<?php echo APP_URL; ?>/polls/manage.php" class="btn btn-sm btn-outline-primary">
                        সব দেখুন <i class="fas fa-arrow-right"></i>
                    </a>
                </div>
                <div class="card-body">
                    <?php if (!empty($recent_polls)): ?>
                        <div class="list-group list-group-flush">
                            <?php foreach ($recent_polls as $poll): ?>
                            <div class="list-group-item border-0 px-0">
                                <div class="d-flex justify-content-between align-items-start">
                                    <div class="flex-grow-1">
                                        <h6 class="mb-1">
                                            <a href="<?php echo APP_URL; ?>/polls/view.php?id=<?php echo $poll['id']; ?>" 
                                               class="text-decoration-none">
                                                <?php echo htmlspecialchars($poll['title']); ?>
                                            </a>
                                        </h6>
                                        <p class="mb-1 text-muted small">
                                            <?php echo htmlspecialchars(substr($poll['description'], 0, 100)); ?>
                                            <?php if (strlen($poll['description']) > 100): ?>...<?php endif; ?>
                                        </p>
                                        <small class="text-muted">
                                            <i class="fas fa-calendar"></i> 
                                            <?php echo date('d M Y, h:i A', strtotime($poll['created_at'])); ?>
                                        </small>
                                    </div>
                                    <div class="text-end">
                                        <span class="badge bg-<?php echo $poll['is_active'] ? 'success' : 'secondary'; ?>">
                                            <?php echo $poll['is_active'] ? 'সক্রিয়' : 'নিষ্ক্রিয়'; ?>
                                        </span>
                                        <div class="mt-1">
                                            <small class="text-muted">
                                                <i class="fas fa-vote-yea"></i> <?php echo $poll['vote_count']; ?> ভোট
                                            </small>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <?php endforeach; ?>
                        </div>
                    <?php else: ?>
                        <div class="text-center py-4">
                            <i class="fas fa-poll fa-3x text-muted mb-3"></i>
                            <h5 class="text-muted">কোন পোল পাওয়া যায়নি</h5>
                            <p class="text-muted">আপনার প্রথম পোল তৈরি করুন!</p>
                            <a href="<?php echo APP_URL; ?>/polls/create.php" class="btn btn-primary">
                                <i class="fas fa-plus"></i> পোল তৈরি করুন
                            </a>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>

        <!-- Quick Actions -->
        <div class="col-md-4 mb-4">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-bolt"></i> দ্রুত অ্যাকশন
                    </h5>
                </div>
                <div class="card-body">
                    <div class="d-grid gap-2">
                        <a href="<?php echo APP_URL; ?>/polls/create.php" class="btn btn-primary">
                            <i class="fas fa-plus"></i> নতুন পোল তৈরি
                        </a>
                        <a href="<?php echo APP_URL; ?>/polls/" class="btn btn-outline-secondary">
                            <i class="fas fa-list"></i> পোল ব্রাউজ করুন
                        </a>
                        <a href="<?php echo APP_URL; ?>/profile.php" class="btn btn-outline-secondary">
                            <i class="fas fa-user"></i> প্রোফাইল আপডেট
                        </a>
                        <?php if (isAdmin()): ?>
                        <hr>
                        <a href="<?php echo APP_URL; ?>/admin/" class="btn btn-warning">
                            <i class="fas fa-cogs"></i> অ্যাডমিন প্যানেল
                        </a>
                        <?php endif; ?>
                    </div>
                </div>
            </div>

            <!-- Tips Card -->
            <div class="card mt-3">
                <div class="card-header bg-info text-white">
                    <h6 class="mb-0">
                        <i class="fas fa-lightbulb"></i> টিপস
                    </h6>
                </div>
                <div class="card-body">
                    <ul class="list-unstyled mb-0">
                        <li class="mb-2">
                            <i class="fas fa-check text-success"></i>
                            স্পষ্ট এবং সংক্ষিপ্ত প্রশ্ন লিখুন
                        </li>
                        <li class="mb-2">
                            <i class="fas fa-check text-success"></i>
                            উপযুক্ত বিকল্প প্রদান করুন
                        </li>
                        <li class="mb-0">
                            <i class="fas fa-check text-success"></i>
                            নিয়মিত ফলাফল পর্যবেক্ষণ করুন
                        </li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
</div>

<?php
$content = ob_get_clean();

// Include the sidebar layout
include 'includes/sidebar-layout.php';
?>
