<?php
/**
 * Session Management
 * Dynamic Realtime Online Voting System
 */

// Session configuration - only set if session is not active
if (session_status() === PHP_SESSION_NONE) {
    ini_set('session.cookie_httponly', 1);
    ini_set('session.use_only_cookies', 1);
    ini_set('session.cookie_secure', 0); // Set to 1 for HTTPS
    ini_set('session.gc_maxlifetime', SESSION_LIFETIME);
}

// Start session with custom settings
function startSecureSession() {
    if (session_status() === PHP_SESSION_NONE) {
        session_start();
        
        // Regenerate session ID periodically for security
        if (!isset($_SESSION['created'])) {
            $_SESSION['created'] = time();
        } else if (time() - $_SESSION['created'] > 1800) { // 30 minutes
            session_regenerate_id(true);
            $_SESSION['created'] = time();
        }
        
        // Check session timeout
        if (isset($_SESSION['last_activity']) && 
            (time() - $_SESSION['last_activity'] > SESSION_LIFETIME)) {
            destroySession();
            return false;
        }
        
        $_SESSION['last_activity'] = time();
        return true;
    }
    return true;
}

// Destroy session
function destroySession() {
    if (session_status() === PHP_SESSION_ACTIVE) {
        $_SESSION = [];
        
        if (ini_get("session.use_cookies")) {
            $params = session_get_cookie_params();
            setcookie(session_name(), '', time() - 42000,
                $params["path"], $params["domain"],
                $params["secure"], $params["httponly"]
            );
        }
        
        session_destroy();
    }
}

// Login user
function loginUser($user) {
    session_regenerate_id(true);
    $_SESSION['user'] = [
        'id' => $user['id'],
        'username' => $user['username'],
        'email' => $user['email'],
        'first_name' => $user['first_name'],
        'last_name' => $user['last_name'],
        'role' => $user['role'],
        'avatar' => $user['avatar']
    ];
    $_SESSION['login_time'] = time();
    
    // Update last login in database
    try {
        $db = getDB();
        $stmt = $db->prepare("UPDATE users SET last_login = NOW() WHERE id = ?");
        $stmt->execute([$user['id']]);
    } catch (Exception $e) {
        error_log("Failed to update last login: " . $e->getMessage());
    }
    
    logActivity('LOGIN', 'user', $user['id']);
}

// Logout user
function logoutUser() {
    $user = getCurrentUser();
    if ($user) {
        logActivity('LOGOUT', 'user', $user['id']);
    }
    destroySession();
}

// Check if session is valid
function isValidSession() {
    return isset($_SESSION['user']) && 
           isset($_SESSION['login_time']) && 
           (time() - $_SESSION['login_time'] < SESSION_LIFETIME);
}

// Get session info
function getSessionInfo() {
    return [
        'user' => getCurrentUser(),
        'login_time' => $_SESSION['login_time'] ?? null,
        'last_activity' => $_SESSION['last_activity'] ?? null,
        'session_id' => session_id(),
        'is_valid' => isValidSession()
    ];
}

// Anonymous session for voting
function getAnonymousSessionId() {
    if (!isset($_SESSION['anonymous_id'])) {
        $_SESSION['anonymous_id'] = 'anon_' . uniqid() . '_' . time();
    }
    return $_SESSION['anonymous_id'];
}

// Rate limiting for login attempts
function checkLoginAttempts($identifier) {
    if (!isset($_SESSION['login_attempts'])) {
        $_SESSION['login_attempts'] = [];
    }
    
    $attempts = $_SESSION['login_attempts'][$identifier] ?? [];
    $recent_attempts = array_filter($attempts, function($time) {
        return (time() - $time) < LOGIN_LOCKOUT_TIME;
    });
    
    return count($recent_attempts) < MAX_LOGIN_ATTEMPTS;
}

function recordLoginAttempt($identifier, $success = false) {
    if (!isset($_SESSION['login_attempts'])) {
        $_SESSION['login_attempts'] = [];
    }
    
    if ($success) {
        // Clear attempts on successful login
        unset($_SESSION['login_attempts'][$identifier]);
    } else {
        // Record failed attempt
        if (!isset($_SESSION['login_attempts'][$identifier])) {
            $_SESSION['login_attempts'][$identifier] = [];
        }
        $_SESSION['login_attempts'][$identifier][] = time();
    }
}

// Initialize session
startSecureSession();
?>
