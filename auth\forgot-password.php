<?php
/**
 * Forgot Password Page
 * Dynamic Realtime Online Voting System
 */

require_once '../config/config.php';

// Redirect if already logged in
if (isLoggedIn()) {
    redirect(APP_URL);
}

$error = '';
$success = '';
$email = '';
$step = $_GET['step'] ?? 'request';
$token = $_GET['token'] ?? '';

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    // Validate CSRF token
    if (!validateCSRFToken($_POST['csrf_token'] ?? '')) {
        $error = 'Invalid request. Please try again.';
    } else {
        if ($step === 'request') {
            // Handle password reset request
            $email = sanitizeInput($_POST['email'] ?? '');
            
            if (empty($email)) {
                $error = 'Please enter your email address.';
            } elseif (!isValidEmail($email)) {
                $error = 'Please enter a valid email address.';
            } else {
                $result = requestPasswordReset($email);
                if ($result['success']) {
                    $success = $result['message'];
                } else {
                    $error = $result['message'];
                }
            }
        } elseif ($step === 'reset') {
            // Handle password reset
            $new_password = $_POST['new_password'] ?? '';
            $confirm_password = $_POST['confirm_password'] ?? '';
            
            if (empty($new_password) || empty($confirm_password)) {
                $error = 'Please fill in all fields.';
            } elseif (!validatePassword($new_password)) {
                $error = 'Password must be at least ' . PASSWORD_MIN_LENGTH . ' characters long.';
            } elseif ($new_password !== $confirm_password) {
                $error = 'Passwords do not match.';
            } else {
                $result = resetPassword($token, $new_password);
                if ($result['success']) {
                    $success = $result['message'];
                    $step = 'complete';
                } else {
                    $error = $result['message'];
                }
            }
        }
    }
}

// Validate reset token if on reset step
if ($step === 'reset' && !empty($token)) {
    $token_valid = validateResetToken($token);
    if (!$token_valid) {
        $error = 'Invalid or expired reset token.';
        $step = 'request';
    }
}

$page_title = 'Forgot Password';
include '../includes/header.php';
?>

<div class="container mt-5">
    <div class="row justify-content-center">
        <div class="col-md-6 col-lg-4">
            <div class="card shadow">
                <div class="card-body p-4">
                    <?php if ($step === 'request'): ?>
                    <!-- Password Reset Request -->
                    <div class="text-center mb-4">
                        <h2 class="card-title">Forgot Password</h2>
                        <p class="text-muted">Enter your email to reset password</p>
                    </div>
                    
                    <?php if ($error): ?>
                    <div class="alert alert-danger" role="alert">
                        <i class="fas fa-exclamation-triangle"></i> <?php echo htmlspecialchars($error); ?>
                    </div>
                    <?php endif; ?>
                    
                    <?php if ($success): ?>
                    <div class="alert alert-success" role="alert">
                        <i class="fas fa-check-circle"></i> <?php echo htmlspecialchars($success); ?>
                    </div>
                    <?php endif; ?>
                    
                    <form method="POST" action="">
                        <input type="hidden" name="csrf_token" value="<?php echo generateCSRFToken(); ?>">
                        
                        <div class="form-floating mb-3">
                            <input type="email" class="form-control" id="email" name="email" 
                                   value="<?php echo htmlspecialchars($email); ?>" required>
                            <label for="email">Email Address</label>
                        </div>
                        
                        <button type="submit" class="btn btn-primary w-100 mb-3">
                            <i class="fas fa-paper-plane"></i> Send Reset Link
                        </button>
                    </form>
                    
                    <?php elseif ($step === 'reset'): ?>
                    <!-- Password Reset Form -->
                    <div class="text-center mb-4">
                        <h2 class="card-title">Reset Password</h2>
                        <p class="text-muted">Enter your new password</p>
                    </div>
                    
                    <?php if ($error): ?>
                    <div class="alert alert-danger" role="alert">
                        <i class="fas fa-exclamation-triangle"></i> <?php echo htmlspecialchars($error); ?>
                    </div>
                    <?php endif; ?>
                    
                    <form method="POST" action="?step=reset&token=<?php echo htmlspecialchars($token); ?>">
                        <input type="hidden" name="csrf_token" value="<?php echo generateCSRFToken(); ?>">
                        
                        <div class="form-floating mb-3">
                            <input type="password" class="form-control" id="new_password" name="new_password" required>
                            <label for="new_password">New Password</label>
                        </div>
                        
                        <div class="form-floating mb-3">
                            <input type="password" class="form-control" id="confirm_password" name="confirm_password" required>
                            <label for="confirm_password">Confirm Password</label>
                        </div>
                        
                        <button type="submit" class="btn btn-primary w-100 mb-3">
                            <i class="fas fa-key"></i> Reset Password
                        </button>
                    </form>
                    
                    <?php elseif ($step === 'complete'): ?>
                    <!-- Password Reset Complete -->
                    <div class="text-center mb-4">
                        <i class="fas fa-check-circle fa-3x text-success mb-3"></i>
                        <h2 class="card-title">Password Reset Complete</h2>
                        <p class="text-muted">Your password has been successfully reset</p>
                    </div>
                    
                    <div class="alert alert-success" role="alert">
                        <i class="fas fa-check-circle"></i> <?php echo htmlspecialchars($success); ?>
                    </div>
                    
                    <a href="login.php" class="btn btn-primary w-100">
                        <i class="fas fa-sign-in-alt"></i> Login Now
                    </a>
                    
                    <?php endif; ?>
                    
                    <hr class="my-4">
                    
                    <div class="text-center">
                        <p class="mb-0">Remember your password?</p>
                        <a href="login.php" class="btn btn-outline-primary w-100 mt-2">
                            <i class="fas fa-sign-in-alt"></i> Back to Login
                        </a>
                    </div>
                </div>
            </div>
            
            <!-- Demo Reset (Development Only) -->
            <?php if (APP_DEBUG): ?>
            <div class="card mt-3 border-warning">
                <div class="card-body">
                    <h6 class="card-title text-warning">
                        <i class="fas fa-tools"></i> Development Mode
                    </h6>
                    <p class="card-text small mb-2">
                        For testing, you can use the admin reset utility:
                    </p>
                    <a href="../reset-admin.php" class="btn btn-sm btn-outline-warning">
                        Reset Admin Password
                    </a>
                </div>
            </div>
            <?php endif; ?>
        </div>
    </div>
</div>

<?php 
$inline_js = "
// Form validation
document.addEventListener('DOMContentLoaded', function() {
    const step = '" . $step . "';
    
    if (step === 'reset') {
        const form = document.querySelector('form');
        const newPassword = document.getElementById('new_password');
        const confirmPassword = document.getElementById('confirm_password');
        
        // Real-time password confirmation
        confirmPassword.addEventListener('input', function() {
            if (this.value && newPassword.value !== this.value) {
                this.setCustomValidity('Passwords do not match');
                this.classList.add('is-invalid');
            } else {
                this.setCustomValidity('');
                this.classList.remove('is-invalid');
            }
        });
        
        // Form submission validation
        form.addEventListener('submit', function(e) {
            if (newPassword.value !== confirmPassword.value) {
                e.preventDefault();
                OVS.showAlert('Passwords do not match.', 'danger');
                return false;
            }
            
            if (newPassword.value.length < " . PASSWORD_MIN_LENGTH . ") {
                e.preventDefault();
                OVS.showAlert('Password must be at least " . PASSWORD_MIN_LENGTH . " characters long.', 'danger');
                return false;
            }
        });
    }
    
    // Auto-focus email field on request step
    if (step === 'request') {
        document.getElementById('email').focus();
    }
});
";

include '../includes/footer.php'; 
?>

<?php
// Helper functions for password reset

function requestPasswordReset($email) {
    try {
        $db = getDB();
        
        // Check if user exists
        $stmt = $db->prepare("SELECT id, username FROM users WHERE email = ? AND is_active = 1");
        $stmt->execute([$email]);
        $user = $stmt->fetch();
        
        if (!$user) {
            // Don't reveal if email exists or not for security
            return [
                'success' => true, 
                'message' => 'If an account with this email exists, you will receive a password reset link.'
            ];
        }
        
        // Generate reset token
        $token = generateRandomString(64);
        $expires = date('Y-m-d H:i:s', time() + 3600); // 1 hour
        
        // Save reset token
        $stmt = $db->prepare("
            UPDATE users 
            SET password_reset_token = ?, password_reset_expires = ? 
            WHERE id = ?
        ");
        $stmt->execute([$token, $expires, $user['id']]);
        
        // In a real application, you would send an email here
        // For demo purposes, we'll show the reset link
        $reset_link = APP_URL . "/auth/forgot-password.php?step=reset&token=" . $token;
        
        logActivity('PASSWORD_RESET_REQUEST', 'user', $user['id']);
        
        if (APP_DEBUG) {
            return [
                'success' => true,
                'message' => "Password reset requested. Demo link: <a href='$reset_link' target='_blank'>Reset Password</a>"
            ];
        } else {
            return [
                'success' => true,
                'message' => 'If an account with this email exists, you will receive a password reset link.'
            ];
        }
        
    } catch (Exception $e) {
        error_log("Password reset request failed: " . $e->getMessage());
        return ['success' => false, 'message' => 'Failed to process password reset request.'];
    }
}

function validateResetToken($token) {
    try {
        $db = getDB();
        
        $stmt = $db->prepare("
            SELECT id FROM users 
            WHERE password_reset_token = ? 
            AND password_reset_expires > NOW() 
            AND is_active = 1
        ");
        $stmt->execute([$token]);
        
        return $stmt->fetch() !== false;
        
    } catch (Exception $e) {
        error_log("Token validation failed: " . $e->getMessage());
        return false;
    }
}

function resetPassword($token, $new_password) {
    try {
        $db = getDB();
        
        // Validate token again
        $stmt = $db->prepare("
            SELECT id, username FROM users 
            WHERE password_reset_token = ? 
            AND password_reset_expires > NOW() 
            AND is_active = 1
        ");
        $stmt->execute([$token]);
        $user = $stmt->fetch();
        
        if (!$user) {
            return ['success' => false, 'message' => 'Invalid or expired reset token.'];
        }
        
        // Hash new password
        $password_hash = password_hash($new_password, PASSWORD_DEFAULT);
        
        // Update password and clear reset token
        $stmt = $db->prepare("
            UPDATE users 
            SET password = ?, password_reset_token = NULL, password_reset_expires = NULL 
            WHERE id = ?
        ");
        $stmt->execute([$password_hash, $user['id']]);
        
        logActivity('PASSWORD_RESET_COMPLETE', 'user', $user['id']);
        
        return [
            'success' => true,
            'message' => 'Your password has been successfully reset. You can now login with your new password.'
        ];
        
    } catch (Exception $e) {
        error_log("Password reset failed: " . $e->getMessage());
        return ['success' => false, 'message' => 'Failed to reset password. Please try again.'];
    }
}
?>
