<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo isset($page_title) ? htmlspecialchars($page_title) . ' - ' . APP_NAME : APP_NAME; ?></title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    
    <!-- Chart.js -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    
    <!-- Custom CSS -->
    <link href="<?php echo APP_URL; ?>/assets/css/style.css" rel="stylesheet">
    
    <!-- Meta tags -->
    <meta name="description" content="<?php echo isset($page_description) ? htmlspecialchars($page_description) : 'Dynamic Realtime Online Voting System'; ?>">
    <meta name="keywords" content="voting, polls, surveys, online voting, real-time results">
    <meta name="author" content="<?php echo APP_NAME; ?>">
    
    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="<?php echo APP_URL; ?>/assets/images/favicon.ico">
    
    <!-- CSRF Token for AJAX requests -->
    <meta name="csrf-token" content="<?php echo generateCSRFToken(); ?>">
    
    <!-- App Configuration for JavaScript -->
    <script>
        window.APP_CONFIG = {
            url: '<?php echo APP_URL; ?>',
            ajaxPollInterval: <?php echo AJAX_POLL_INTERVAL; ?>,
            enableRealTime: <?php echo ENABLE_REAL_TIME ? 'true' : 'false'; ?>,
            csrfToken: '<?php echo generateCSRFToken(); ?>',
            isLoggedIn: <?php echo isLoggedIn() ? 'true' : 'false'; ?>,
            currentUser: <?php echo isLoggedIn() ? json_encode(getCurrentUser()) : 'null'; ?>
        };
    </script>
</head>
<body>
    <?php include 'navbar.php'; ?>
    
    <!-- Flash Messages -->
    <?php 
    $flash_messages = getFlashMessages();
    if (!empty($flash_messages)): 
    ?>
    <div class="container mt-3">
        <?php foreach ($flash_messages as $message): ?>
        <div class="alert alert-<?php echo $message['type']; ?> alert-dismissible fade show" role="alert">
            <?php echo htmlspecialchars($message['message']); ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
        <?php endforeach; ?>
    </div>
    <?php endif; ?>
    
    <main>
