<?php
/**
 * Ultra Simple Test Page
 * No includes, no complex code
 */

// Minimal PHP
$timestamp = date('Y-m-d H:i:s');
$random = rand(1000, 9999);
?>
<!DOCTYPE html>
<html>
<head>
    <title>Simple Test</title>
    <meta charset="UTF-8">
    <!-- NO META REFRESH -->
</head>
<body>
    <h1>🧪 Ultra Simple Test</h1>
    
    <div style="border: 2px solid blue; padding: 20px; margin: 20px;">
        <h2>Static Content Test</h2>
        <p><strong>Page loaded at:</strong> <?php echo $timestamp; ?></p>
        <p><strong>Random number:</strong> <?php echo $random; ?></p>
        <p><strong>If these numbers change automatically, the page is refreshing!</strong></p>
    </div>
    
    <div style="border: 2px solid green; padding: 20px; margin: 20px;">
        <h2>Instructions:</h2>
        <ol>
            <li>Watch this page for 1 minute</li>
            <li>Note down the timestamp and random number</li>
            <li>If they change, the page is auto-refreshing</li>
            <li>Check browser console (F12) for errors</li>
        </ol>
    </div>
    
    <div style="border: 2px solid red; padding: 20px; margin: 20px;">
        <h2>Links to Test:</h2>
        <p><a href="areas.php">Original Areas Page</a></p>
        <p><a href="../index.php">Home Page</a></p>
        <p><a href="index.php">Admin Dashboard</a></p>
    </div>

    <!-- ABSOLUTELY NO JAVASCRIPT -->
    <script>
        console.log('Simple test page loaded at:', new Date());
        console.log('Timestamp:', '<?php echo $timestamp; ?>');
        console.log('Random:', '<?php echo $random; ?>');
        
        // Monitor for any unexpected reloads
        let loadCount = 0;
        window.addEventListener('load', function() {
            loadCount++;
            console.log('Load event #' + loadCount);
        });
        
        window.addEventListener('beforeunload', function() {
            console.log('Page is unloading - this should only happen when you click a link or refresh manually');
        });
    </script>
</body>
</html>
