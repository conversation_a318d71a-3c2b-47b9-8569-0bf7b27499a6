<?php
/**
 * Simple Photo Upload (No GD Required)
 * Basic voter photo upload without resizing
 */

// Photo upload settings
define('SIMPLE_PHOTO_MAX_SIZE', 5 * 1024 * 1024); // 5MB
define('SIMPLE_PHOTO_ALLOWED_TYPES', ['jpg', 'jpeg', 'png', 'gif', 'webp']);
define('SIMPLE_PHOTO_UPLOAD_DIR', __DIR__ . '/../uploads/voters/');
define('SIMPLE_PHOTO_URL_BASE', APP_URL . '/uploads/voters/');

/**
 * Simple upload voter photo (no resize)
 */
function simpleUploadVoterPhoto($file, $voter_id, $uploaded_by) {
    try {
        // Validate file
        $validation = simpleValidatePhotoUpload($file);
        if (!$validation['success']) {
            return $validation;
        }
        
        // Create upload directory if not exists
        if (!file_exists(SIMPLE_PHOTO_UPLOAD_DIR)) {
            mkdir(SIMPLE_PHOTO_UPLOAD_DIR, 0755, true);
        }
        
        // Generate unique filename
        $extension = strtolower(pathinfo($file['name'], PATHINFO_EXTENSION));
        $filename = 'voter_' . $voter_id . '_' . time() . '_' . uniqid() . '.' . $extension;
        $filepath = SIMPLE_PHOTO_UPLOAD_DIR . $filename;
        
        // Move uploaded file
        if (!move_uploaded_file($file['tmp_name'], $filepath)) {
            return ['success' => false, 'message' => 'ফাইল আপলোড করতে ব্যর্থ'];
        }
        
        // Update database
        $db = getDB();
        
        // Remove old photo if exists
        $stmt = $db->prepare("SELECT photo FROM voters WHERE id = ?");
        $stmt->execute([$voter_id]);
        $old_photo = $stmt->fetchColumn();
        
        if ($old_photo && file_exists(SIMPLE_PHOTO_UPLOAD_DIR . $old_photo)) {
            unlink(SIMPLE_PHOTO_UPLOAD_DIR . $old_photo);
        }
        
        // Update voter photo
        $stmt = $db->prepare("UPDATE voters SET photo = ? WHERE id = ?");
        $stmt->execute([$filename, $voter_id]);
        
        // Log upload (if table exists)
        try {
            $stmt = $db->prepare("
                INSERT INTO voter_uploads (voter_id, original_filename, stored_filename, file_size, mime_type, upload_type, uploaded_by) 
                VALUES (?, ?, ?, ?, ?, 'photo', ?)
            ");
            $stmt->execute([
                $voter_id,
                $file['name'],
                $filename,
                $file['size'],
                $file['type'],
                $uploaded_by
            ]);
        } catch (Exception $e) {
            // Ignore if voter_uploads table doesn't exist
            error_log("Could not log upload: " . $e->getMessage());
        }
        
        logActivity('UPLOAD_PHOTO', 'voter', $voter_id);
        
        return [
            'success' => true,
            'message' => 'ছবি সফলভাবে আপলোড হয়েছে',
            'filename' => $filename,
            'url' => SIMPLE_PHOTO_URL_BASE . $filename
        ];
        
    } catch (Exception $e) {
        error_log("Simple photo upload failed: " . $e->getMessage());
        return ['success' => false, 'message' => 'ছবি আপলোড করতে ব্যর্থ: ' . $e->getMessage()];
    }
}

/**
 * Simple validate photo upload
 */
function simpleValidatePhotoUpload($file) {
    // Check if file was uploaded
    if (!isset($file['tmp_name']) || !is_uploaded_file($file['tmp_name'])) {
        return ['success' => false, 'message' => 'কোন ফাইল আপলোড করা হয়নি'];
    }
    
    // Check file size
    if ($file['size'] > SIMPLE_PHOTO_MAX_SIZE) {
        $max_mb = SIMPLE_PHOTO_MAX_SIZE / (1024 * 1024);
        return ['success' => false, 'message' => "ফাইল সাইজ {$max_mb}MB এর বেশি হতে পারবে না"];
    }
    
    // Check file type
    $extension = strtolower(pathinfo($file['name'], PATHINFO_EXTENSION));
    if (!in_array($extension, SIMPLE_PHOTO_ALLOWED_TYPES)) {
        $allowed = implode(', ', SIMPLE_PHOTO_ALLOWED_TYPES);
        return ['success' => false, 'message' => "শুধুমাত্র এই ফরম্যাট অনুমোদিত: $allowed"];
    }
    
    // Check MIME type
    $finfo = finfo_open(FILEINFO_MIME_TYPE);
    $mime_type = finfo_file($finfo, $file['tmp_name']);
    finfo_close($finfo);
    
    $allowed_mimes = [
        'image/jpeg',
        'image/jpg', 
        'image/png',
        'image/gif',
        'image/webp'
    ];
    
    if (!in_array($mime_type, $allowed_mimes)) {
        return ['success' => false, 'message' => 'অবৈধ ফাইল টাইপ'];
    }
    
    return ['success' => true];
}

/**
 * Get voter photo URL (simple version)
 */
function simpleGetVoterPhotoUrl($photo_filename) {
    if (empty($photo_filename)) {
        return APP_URL . '/assets/images/default-avatar.png';
    }
    
    $filepath = SIMPLE_PHOTO_UPLOAD_DIR . $photo_filename;
    if (!file_exists($filepath)) {
        return APP_URL . '/assets/images/default-avatar.png';
    }
    
    return SIMPLE_PHOTO_URL_BASE . $photo_filename;
}

/**
 * Delete voter photo (simple version)
 */
function simpleDeleteVoterPhoto($voter_id) {
    try {
        $db = getDB();
        
        // Get current photo
        $stmt = $db->prepare("SELECT photo FROM voters WHERE id = ?");
        $stmt->execute([$voter_id]);
        $photo = $stmt->fetchColumn();
        
        if ($photo) {
            // Delete file
            $filepath = SIMPLE_PHOTO_UPLOAD_DIR . $photo;
            if (file_exists($filepath)) {
                unlink($filepath);
            }
            
            // Update database
            $stmt = $db->prepare("UPDATE voters SET photo = NULL WHERE id = ?");
            $stmt->execute([$voter_id]);
            
            logActivity('DELETE_PHOTO', 'voter', $voter_id);
        }
        
        return ['success' => true, 'message' => 'ছবি মুছে ফেলা হয়েছে'];
        
    } catch (Exception $e) {
        error_log("Simple photo deletion failed: " . $e->getMessage());
        return ['success' => false, 'message' => 'ছবি মুছতে ব্যর্থ'];
    }
}
?>
