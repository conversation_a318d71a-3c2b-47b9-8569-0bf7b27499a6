<?php
/**
 * Login Page
 * Dynamic Realtime Online Voting System
 */

require_once '../config/config.php';

// Redirect if already logged in
if (isLoggedIn()) {
    redirect(APP_URL);
}

$error = '';
$email = '';

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    // Validate CSRF token
    if (!validateCSRFToken($_POST['csrf_token'] ?? '')) {
        $error = 'Invalid request. Please try again.';
    } else {
        $email = sanitizeInput($_POST['email'] ?? '');
        $password = $_POST['password'] ?? '';
        
        // Validate input
        if (empty($email) || empty($password)) {
            $error = 'Please fill in all fields.';
        } elseif (!isValidEmail($email)) {
            $error = 'Please enter a valid email address.';
        } else {
            // Check rate limiting
            if (!checkLoginAttempts($email)) {
                $error = 'Too many login attempts. Please try again later.';
            } else {
                // Attempt authentication
                $result = authenticateUser($email, $password);
                
                if ($result['success']) {
                    recordLoginAttempt($email, true);
                    loginUser($result['user']);
                    
                    // Redirect to intended page or dashboard
                    $redirect_url = $_SESSION['redirect_after_login'] ?? APP_URL;
                    unset($_SESSION['redirect_after_login']);
                    redirect($redirect_url);
                } else {
                    recordLoginAttempt($email, false);
                    $error = $result['message'];
                }
            }
        }
    }
}

$page_title = 'Login';
include '../includes/header.php';
?>

<div class="container mt-5">
    <div class="row justify-content-center">
        <div class="col-md-6 col-lg-4">
            <div class="card shadow">
                <div class="card-body p-4">
                    <div class="text-center mb-4">
                        <h2 class="card-title">Welcome Back</h2>
                        <p class="text-muted">Sign in to your account</p>
                    </div>
                    
                    <?php if ($error): ?>
                    <div class="alert alert-danger" role="alert">
                        <i class="fas fa-exclamation-triangle"></i> <?php echo htmlspecialchars($error); ?>
                    </div>
                    <?php endif; ?>
                    
                    <form method="POST" action="">
                        <input type="hidden" name="csrf_token" value="<?php echo generateCSRFToken(); ?>">
                        
                        <div class="form-floating mb-3">
                            <input type="email" class="form-control" id="email" name="email" 
                                   value="<?php echo htmlspecialchars($email); ?>" required>
                            <label for="email">Email Address</label>
                        </div>
                        
                        <div class="form-floating mb-3">
                            <input type="password" class="form-control" id="password" name="password" required>
                            <label for="password">Password</label>
                        </div>
                        
                        <div class="form-check mb-3">
                            <input class="form-check-input" type="checkbox" id="remember" name="remember">
                            <label class="form-check-label" for="remember">
                                Remember me
                            </label>
                        </div>
                        
                        <button type="submit" class="btn btn-primary w-100 mb-3">
                            <i class="fas fa-sign-in-alt"></i> Sign In
                        </button>
                    </form>
                    
                    <div class="text-center">
                        <a href="forgot-password.php" class="text-decoration-none">
                            Forgot your password?
                        </a>
                    </div>
                    
                    <hr class="my-4">
                    
                    <div class="text-center">
                        <p class="mb-0">Don't have an account?</p>
                        <a href="register.php" class="btn btn-outline-primary w-100 mt-2">
                            <i class="fas fa-user-plus"></i> Create Account
                        </a>
                    </div>
                </div>
            </div>
            
            <!-- Demo Credentials -->
            <?php if (APP_DEBUG): ?>
            <div class="card mt-3 border-info">
                <div class="card-body">
                    <h6 class="card-title text-info">
                        <i class="fas fa-info-circle"></i> Demo Credentials
                    </h6>
                    <p class="card-text small mb-2">
                        <strong>Admin:</strong> <EMAIL> / admin123<br>
                        <strong>User:</strong> <EMAIL> / user123
                    </p>
                    <button type="button" class="btn btn-sm btn-outline-info" onclick="fillDemoCredentials('admin')">
                        Use Admin
                    </button>
                    <button type="button" class="btn btn-sm btn-outline-info" onclick="fillDemoCredentials('user')">
                        Use User
                    </button>
                </div>
            </div>
            <?php endif; ?>
        </div>
    </div>
</div>

<?php 
$inline_js = "
function fillDemoCredentials(type) {
    if (type === 'admin') {
        document.getElementById('email').value = '<EMAIL>';
        document.getElementById('password').value = 'admin123';
    } else {
        document.getElementById('email').value = '<EMAIL>';
        document.getElementById('password').value = 'user123';
    }
}

// Auto-focus email field
document.getElementById('email').focus();
";

include '../includes/footer.php'; 
?>
