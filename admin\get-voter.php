<?php
/**
 * Get Voter API
 * Fetch voter data for editing
 */

header('Content-Type: application/json');
require_once '../config/config.php';

// Check if user is logged in and is admin
if (!isLoggedIn() || !isAdmin()) {
    http_response_code(401);
    echo json_encode(['success' => false, 'message' => 'Unauthorized']);
    exit;
}

$voter_id = intval($_GET['id'] ?? 0);

if ($voter_id <= 0) {
    http_response_code(400);
    echo json_encode(['success' => false, 'message' => 'Invalid voter ID']);
    exit;
}

try {
    $db = getDB();
    
    // Get voter data
    $stmt = $db->prepare("
        SELECT v.*, a.name as area_name, a.name_bn as area_name_bn
        FROM voters v
        LEFT JOIN areas a ON v.area_id = a.id
        WHERE v.id = ? AND v.is_active = 1
    ");
    $stmt->execute([$voter_id]);
    $voter = $stmt->fetch();
    
    if (!$voter) {
        http_response_code(404);
        echo json_encode(['success' => false, 'message' => 'Voter not found']);
        exit;
    }
    
    // Return voter data
    echo json_encode([
        'success' => true,
        'voter' => [
            'id' => $voter['id'],
            'voter_id' => $voter['voter_id'],
            'name' => $voter['name'],
            'name_bn' => $voter['name_bn'],
            'nid' => $voter['nid'],
            'mobile' => $voter['mobile'],
            'area_id' => $voter['area_id'],
            'address' => $voter['address'],
            'photo' => $voter['photo'],
            'area_name' => $voter['area_name'],
            'area_name_bn' => $voter['area_name_bn']
        ]
    ]);
    
} catch (Exception $e) {
    error_log("Failed to get voter: " . $e->getMessage());
    http_response_code(500);
    echo json_encode(['success' => false, 'message' => 'Server error']);
}
?>
