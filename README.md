# Dynamic Realtime Online Voting System

A modern, customizable online voting platform with real-time results and comprehensive administrative features.

## Features

### Core Voting Features
- **Real-time Voting**: Live vote counting with instant result updates
- **Multiple Poll Types**: Single choice, multiple choice, ranked voting, and custom formats
- **Customizable Polls**: Flexible poll creation with various question types
- **Time-based Voting**: Set start/end times, voting duration limits
- **Anonymous & Authenticated Voting**: Support for both registered and guest voting

### Customization Options
- **Custom Themes**: Personalized branding and color schemes
- **Voting Restrictions**: IP-based, user-based, or time-based limitations
- **Result Display**: Various chart types, real-time or delayed results
- **Poll Templates**: Pre-built templates for common voting scenarios
- **Custom Fields**: Additional data collection during voting

### Administrative Features
- **User Management**: Role-based access control (Admin, Moderator, User)
- **Poll Analytics**: Detailed voting statistics and participation metrics
- **Moderation Tools**: Content filtering, spam prevention
- **Export Options**: CSV, PDF, and JSON export of results
- **Audit Trail**: Complete voting history and system logs

### Technical Features
- **Real-time Updates**: WebSocket-based live data synchronization
- **Responsive Design**: Mobile-first, cross-platform compatibility
- **Scalable Architecture**: Microservices-ready design
- **Security**: JWT authentication, rate limiting, input validation
- **Performance**: Optimized database queries, caching strategies

## Technology Stack

### Frontend
- **HTML5**: Semantic markup and structure
- **CSS3**: Modern styling with Flexbox and Grid
- **Bootstrap 5**: Responsive framework and components
- **JavaScript (ES6+)**: Modern JavaScript features
- **jQuery**: DOM manipulation and AJAX
- **Chart.js**: Data visualization and real-time charts
- **Font Awesome**: Icons and visual elements

### Backend
- **PHP 8+**: Server-side scripting
- **MySQL 8**: Relational database
- **Apache/Nginx**: Web server
- **PDO**: Database abstraction layer
- **Sessions**: User authentication and state management
- **JSON**: Data exchange format for AJAX

### Real-time Features
- **AJAX**: Asynchronous data updates
- **JavaScript Polling**: Real-time vote updates
- **WebSocket (optional)**: Enhanced real-time communication
- **Server-Sent Events**: Live notifications

### Development Tools
- **XAMPP/WAMP**: Local development environment
- **phpMyAdmin**: Database management
- **Git**: Version control
- **Composer**: PHP dependency management

## Project Structure

```
ovs/
├── index.php                 # Main landing page
├── config/                   # Configuration files
│   ├── database.php         # Database connection
│   ├── config.php           # Application settings
│   └── session.php          # Session management
├── includes/                 # Shared PHP includes
│   ├── header.php           # Common header
│   ├── footer.php           # Common footer
│   ├── navbar.php           # Navigation bar
│   └── functions.php        # Utility functions
├── auth/                     # Authentication system
│   ├── login.php            # Login page
│   ├── register.php         # Registration page
│   ├── logout.php           # Logout handler
│   └── forgot-password.php  # Password reset
├── polls/                    # Poll management
│   ├── create.php           # Create new poll
│   ├── edit.php             # Edit existing poll
│   ├── view.php             # View poll and vote
│   ├── results.php          # Poll results
│   └── manage.php           # User's polls
├── admin/                    # Admin dashboard
│   ├── index.php            # Admin home
│   ├── users.php            # User management
│   ├── polls.php            # Poll management
│   └── analytics.php        # System analytics
├── api/                      # AJAX endpoints
│   ├── vote.php             # Cast vote
│   ├── results.php          # Get live results
│   ├── polls.php            # Poll operations
│   └── auth.php             # Authentication API
├── assets/                   # Static assets
│   ├── css/                 # Stylesheets
│   ├── js/                  # JavaScript files
│   ├── images/              # Images and icons
│   └── uploads/             # User uploads
├── database/                 # Database files
│   ├── schema.sql           # Database structure
│   ├── seeds.sql            # Sample data
│   └── migrations/          # Database updates
└── README.md
```

## Quick Start

1. **Setup XAMPP/WAMP**
   - Install XAMPP or WAMP server
   - Start Apache and MySQL services
   - Ensure PHP 8+ is installed

2. **Clone and Setup**
   ```bash
   git clone <repository-url>
   cd xampp/htdocs/ovs  # or your web server directory
   ```

3. **Database Setup**
   ```bash
   # Access phpMyAdmin at http://localhost/phpmyadmin
   # Create database 'ovs_db'
   # Import database/schema.sql
   # Optionally import database/seeds.sql for sample data
   ```

4. **Configuration**
   ```bash
   # Copy and edit configuration
   cp config/config.example.php config/config.php
   # Update database credentials in config/database.php
   ```

5. **Access Application**
   - Main Site: http://localhost/ovs
   - Admin Panel: http://localhost/ovs/admin
   - phpMyAdmin: http://localhost/phpmyadmin

## Environment Variables

Create `.env` files in both frontend and backend directories with the required configuration variables. See `.env.example` for reference.

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests for new functionality
5. Submit a pull request

## License

This project is licensed under the MIT License - see the LICENSE file for details.
