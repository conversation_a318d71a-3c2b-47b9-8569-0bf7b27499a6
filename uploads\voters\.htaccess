# Voter Photos Directory Security
# Allow only image files

<Files "*">
    Order Deny,Allow
    Deny from all
</Files>

<FilesMatch "\.(jpg|jpeg|png|gif|webp)$">
    Order Allow,Deny
    Allow from all
</FilesMatch>

# Prevent PHP execution
<FilesMatch "\.php$">
    Order Deny,Allow
    Deny from all
</FilesMatch>

# Set proper headers for images
<IfModule mod_headers.c>
    <FilesMatch "\.(jpg|jpeg|png|gif|webp)$">
        Header set Cache-Control "public, max-age=2592000"
        Header set Content-Type "image"
    </FilesMatch>
</IfModule>
