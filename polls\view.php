<?php
/**
 * পোল দেখা এবং ভোট দেওয়ার পেজ
 * Poll View and Voting Page
 */

require_once '../config/config.php';

$poll_id = intval($_GET['id'] ?? 0);
if (!$poll_id) {
    setFlashMessage('error', 'Invalid poll ID');
    redirect(APP_URL);
}

$error = '';
$success = '';
$can_vote = false;
$vote_error = '';

// Get poll details
try {
    $db = getDB();
    $stmt = $db->prepare("
        SELECT p.*, u.username as creator_name, u.first_name, u.last_name,
               (SELECT COUNT(*) FROM votes WHERE poll_id = p.id) as total_votes
        FROM polls p 
        JOIN users u ON p.created_by = u.id 
        WHERE p.id = ? AND p.is_active = 1
    ");
    $stmt->execute([$poll_id]);
    $poll = $stmt->fetch();
    
    if (!$poll) {
        setFlashMessage('error', 'Poll not found');
        redirect(APP_URL);
    }
    
    // Decode settings
    $poll['settings'] = json_decode($poll['settings'], true);
    
    // Get poll options
    $stmt = $db->prepare("
        SELECT id, option_text, option_order 
        FROM poll_options 
        WHERE poll_id = ? 
        ORDER BY option_order
    ");
    $stmt->execute([$poll_id]);
    $poll['options'] = $stmt->fetchAll();
    
} catch (Exception $e) {
    setFlashMessage('error', 'Failed to load poll');
    redirect(APP_URL);
}

// Check voting access
$user = getCurrentUser();
$session_id = getAnonymousSessionId();
$user_area = null;

// Get user's area if logged in
if ($user && $user['area_id']) {
    try {
        $stmt = $db->prepare("SELECT * FROM areas WHERE id = ?");
        $stmt->execute([$user['area_id']]);
        $user_area = $stmt->fetch();
    } catch (Exception $e) {
        // Continue without area info
    }
}

// Check access permissions
$access_type = $poll['access_type'] ?? 'public';
$allowed_areas = $poll['allowed_areas'] ? json_decode($poll['allowed_areas'], true) : [];
$require_voter_verification = $poll['require_voter_verification'] ?? false;

switch ($access_type) {
    case 'public':
        $can_vote = true;
        break;
        
    case 'area_based':
        if ($user && $user_area && in_array($user_area['id'], $allowed_areas)) {
            $can_vote = true;
        } else {
            $vote_error = 'এই ভোটটি শুধুমাত্র নির্দিষ্ট এলাকার বাসিন্দাদের জন্য';
        }
        break;
        
    case 'mixed':
        $can_vote = true; // Mixed allows both public and area-based
        break;
        
    default:
        $can_vote = true;
}

// Check if user already voted
$already_voted = false;
if ($can_vote) {
    try {
        $stmt = $db->prepare("
            SELECT id FROM votes 
            WHERE poll_id = ? AND (user_id = ? OR session_id = ?)
        ");
        $stmt->execute([$poll_id, $user['id'] ?? null, $session_id]);
        $already_voted = $stmt->fetch() !== false;
    } catch (Exception $e) {
        // Continue
    }
}

// Check if poll has ended
$poll_ended = $poll['end_date'] && strtotime($poll['end_date']) < time();

// Handle vote submission
if ($_SERVER['REQUEST_METHOD'] === 'POST' && $can_vote && !$already_voted && !$poll_ended) {
    if (!validateCSRFToken($_POST['csrf_token'] ?? '')) {
        $error = 'Invalid request. Please try again.';
    } else {
        $vote_data = [];
        
        // Process vote based on poll type
        switch ($poll['type']) {
            case 'single_choice':
                $selected_option = intval($_POST['option'] ?? 0);
                if ($selected_option) {
                    $vote_data['selected_options'] = [$selected_option];
                }
                break;
                
            case 'multiple_choice':
                $selected_options = $_POST['options'] ?? [];
                if (!empty($selected_options)) {
                    $vote_data['selected_options'] = array_map('intval', $selected_options);
                }
                break;
                
            case 'text_response':
                $text_response = sanitizeInput($_POST['text_response'] ?? '');
                if (!empty($text_response)) {
                    $vote_data['text_response'] = $text_response;
                }
                break;
                
            case 'rating_scale':
                $rating = intval($_POST['rating'] ?? 0);
                if ($rating >= 1 && $rating <= 10) {
                    $vote_data['rating'] = $rating;
                }
                break;
        }
        
        if (!empty($vote_data)) {
            // Cast vote
            $result = castVote($poll_id, $user['id'] ?? null, $session_id, $vote_data);
            
            if ($result['success']) {
                $success = 'আপনার ভোট সফলভাবে জমা দেওয়া হয়েছে!';
                $already_voted = true;
                
                // Update total votes
                $poll['total_votes']++;
            } else {
                $error = $result['message'];
            }
        } else {
            $error = 'Please select an option or provide a response';
        }
    }
}

// Get results if allowed
$show_results = false;
$results = null;

$show_results_setting = $poll['settings']['showResults'] ?? 'after_vote';
switch ($show_results_setting) {
    case 'always':
        $show_results = true;
        break;
    case 'after_vote':
        $show_results = $already_voted;
        break;
    case 'after_end':
        $show_results = $poll_ended;
        break;
    case 'never':
        $show_results = false;
        break;
}

if ($show_results) {
    $results = getPollResults($poll_id);
}

$page_title = htmlspecialchars($poll['title']);
include '../includes/header.php';
?>

<div class="container mt-4">
    <div class="row">
        <div class="col-lg-8 mx-auto">
            <!-- Poll Header -->
            <div class="card mb-4">
                <div class="card-header">
                    <div class="d-flex justify-content-between align-items-start">
                        <div>
                            <h2 class="card-title mb-1"><?php echo htmlspecialchars($poll['title']); ?></h2>
                            <p class="text-muted mb-0">
                                <i class="fas fa-user"></i> 
                                <?php echo htmlspecialchars($poll['creator_name']); ?>
                                <span class="mx-2">•</span>
                                <i class="fas fa-calendar"></i>
                                <?php echo formatDate($poll['created_at'], 'd M Y'); ?>
                                <span class="mx-2">•</span>
                                <i class="fas fa-vote-yea"></i>
                                <?php echo number_format($poll['total_votes']); ?> ভোট
                            </p>
                        </div>
                        <div class="text-end">
                            <?php if ($access_type !== 'public'): ?>
                            <span class="badge bg-info mb-2">
                                <i class="fas fa-map-marker-alt"></i>
                                <?php 
                                switch ($access_type) {
                                    case 'area_based': echo 'এলাকা ভিত্তিক'; break;
                                    case 'mixed': echo 'মিক্স ভোটিং'; break;
                                }
                                ?>
                            </span><br>
                            <?php endif; ?>
                            
                            <?php if ($poll['end_date']): ?>
                            <small class="text-muted">
                                <i class="fas fa-clock"></i>
                                শেষ: <?php echo formatDate($poll['end_date'], 'd M Y H:i'); ?>
                            </small>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
                
                <?php if ($poll['description']): ?>
                <div class="card-body">
                    <p class="card-text"><?php echo nl2br(htmlspecialchars($poll['description'])); ?></p>
                </div>
                <?php endif; ?>
            </div>
            
            <!-- Access Control Info -->
            <?php if ($access_type !== 'public'): ?>
            <div class="alert alert-info mb-4">
                <h6><i class="fas fa-info-circle"></i> ভোটিং অ্যাক্সেস তথ্য</h6>
                <?php if ($access_type === 'area_based'): ?>
                    <p class="mb-0">এই ভোটটি শুধুমাত্র নির্দিষ্ট এলাকার বাসিন্দাদের জন্য।</p>
                <?php elseif ($access_type === 'mixed'): ?>
                    <p class="mb-0">এই ভোটে সবাই অংশগ্রহণ করতে পারবেন, তবে এলাকা ভিত্তিক বিশেষ সুবিধা রয়েছে।</p>
                <?php endif; ?>
                
                <?php if ($user_area): ?>
                    <p class="mb-0 mt-1">
                        <strong>আপনার এলাকা:</strong> 
                        <?php echo htmlspecialchars($user_area['name_bn'] ?: $user_area['name']); ?>
                    </p>
                <?php endif; ?>
            </div>
            <?php endif; ?>
            
            <!-- Error Messages -->
            <?php if ($error): ?>
            <div class="alert alert-danger">
                <i class="fas fa-exclamation-triangle"></i> <?php echo htmlspecialchars($error); ?>
            </div>
            <?php endif; ?>
            
            <?php if ($success): ?>
            <div class="alert alert-success">
                <i class="fas fa-check-circle"></i> <?php echo htmlspecialchars($success); ?>
            </div>
            <?php endif; ?>
            
            <?php if ($vote_error): ?>
            <div class="alert alert-warning">
                <i class="fas fa-exclamation-triangle"></i> <?php echo htmlspecialchars($vote_error); ?>
            </div>
            <?php endif; ?>
            
            <!-- Voting Section -->
            <?php if ($can_vote && !$already_voted && !$poll_ended): ?>
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="mb-0"><i class="fas fa-vote-yea"></i> আপনার ভোট দিন</h5>
                </div>
                <div class="card-body">
                    <form method="POST" action="">
                        <input type="hidden" name="csrf_token" value="<?php echo generateCSRFToken(); ?>">
                        
                        <?php if ($poll['type'] === 'single_choice'): ?>
                            <?php foreach ($poll['options'] as $option): ?>
                            <div class="vote-option">
                                <input type="radio" name="option" value="<?php echo $option['id']; ?>" 
                                       id="option_<?php echo $option['id']; ?>" required>
                                <label for="option_<?php echo $option['id']; ?>">
                                    <?php echo htmlspecialchars($option['option_text']); ?>
                                </label>
                            </div>
                            <?php endforeach; ?>
                            
                        <?php elseif ($poll['type'] === 'multiple_choice'): ?>
                            <?php foreach ($poll['options'] as $option): ?>
                            <div class="vote-option">
                                <input type="checkbox" name="options[]" value="<?php echo $option['id']; ?>" 
                                       id="option_<?php echo $option['id']; ?>">
                                <label for="option_<?php echo $option['id']; ?>">
                                    <?php echo htmlspecialchars($option['option_text']); ?>
                                </label>
                            </div>
                            <?php endforeach; ?>
                            
                        <?php elseif ($poll['type'] === 'text_response'): ?>
                            <div class="form-floating">
                                <textarea class="form-control" name="text_response" id="text_response" 
                                          style="height: 120px" maxlength="1000" required></textarea>
                                <label for="text_response">আপনার মতামত লিখুন</label>
                            </div>
                            
                        <?php elseif ($poll['type'] === 'rating_scale'): ?>
                            <div class="text-center">
                                <p class="mb-3">১ থেকে ১০ এর মধ্যে রেটিং দিন:</p>
                                <div class="rating-scale">
                                    <?php for ($i = 1; $i <= 10; $i++): ?>
                                    <label class="rating-option">
                                        <input type="radio" name="rating" value="<?php echo $i; ?>" required>
                                        <span class="rating-number"><?php echo $i; ?></span>
                                    </label>
                                    <?php endfor; ?>
                                </div>
                            </div>
                        <?php endif; ?>
                        
                        <div class="mt-4">
                            <button type="submit" class="btn btn-primary btn-lg">
                                <i class="fas fa-vote-yea"></i> ভোট দিন
                            </button>
                        </div>
                    </form>
                </div>
            </div>
            
            <?php elseif ($already_voted): ?>
            <div class="alert alert-success">
                <i class="fas fa-check-circle"></i> আপনি ইতিমধ্যে এই ভোটে অংশগ্রহণ করেছেন। ধন্যবাদ!
            </div>
            
            <?php elseif ($poll_ended): ?>
            <div class="alert alert-warning">
                <i class="fas fa-clock"></i> এই ভোটের সময় শেষ হয়ে গেছে।
            </div>
            
            <?php elseif (!$can_vote): ?>
            <div class="alert alert-warning">
                <i class="fas fa-ban"></i> আপনি এই ভোটে অংশগ্রহণ করতে পারবেন না।
            </div>
            <?php endif; ?>
            
            <!-- Results Section -->
            <?php if ($show_results && $results): ?>
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-chart-bar"></i> ভোটের ফলাফল
                        <span class="real-time-indicator"></span>
                        <small class="text-muted">লাইভ আপডেট</small>
                    </h5>
                </div>
                <div class="card-body" id="poll-results">
                    <?php if ($poll['type'] === 'rating_scale'): ?>
                        <!-- Rating Results -->
                        <div class="text-center mb-4">
                            <h3 class="text-primary">গড় রেটিং: <?php echo number_format($results['average_rating'] ?? 0, 1); ?>/১০</h3>
                            <p class="text-muted">মোট <?php echo $results['total_votes']; ?>টি রেটিং</p>
                        </div>
                        
                    <?php elseif ($poll['type'] === 'text_response'): ?>
                        <!-- Text Responses -->
                        <p class="text-muted mb-3">মোট <?php echo $results['total_votes']; ?>টি মতামত পাওয়া গেছে</p>
                        
                    <?php else: ?>
                        <!-- Choice Results -->
                        <?php foreach ($results['options'] as $option): ?>
                        <div class="mb-3">
                            <div class="d-flex justify-content-between align-items-center mb-1">
                                <span class="fw-medium"><?php echo htmlspecialchars($option['option_text']); ?></span>
                                <span class="text-muted">
                                    <?php echo number_format($option['vote_count']); ?> ভোট 
                                    (<?php echo $option['percentage']; ?>%)
                                </span>
                            </div>
                            <div class="progress" style="height: 30px;">
                                <div class="progress-bar bg-primary" role="progressbar" 
                                     style="width: <?php echo $option['percentage']; ?>%" 
                                     aria-valuenow="<?php echo $option['percentage']; ?>" 
                                     aria-valuemin="0" aria-valuemax="100">
                                    <?php echo $option['percentage']; ?>%
                                </div>
                            </div>
                        </div>
                        <?php endforeach; ?>
                        
                        <div class="mt-4 text-center">
                            <p class="text-muted mb-0">
                                <strong>মোট ভোট:</strong> <?php echo number_format($results['total_votes']); ?>
                            </p>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
            <?php endif; ?>
        </div>
    </div>
</div>

<style>
.rating-scale {
    display: flex;
    justify-content: center;
    gap: 10px;
    flex-wrap: wrap;
}

.rating-option {
    display: flex;
    flex-direction: column;
    align-items: center;
    cursor: pointer;
    padding: 10px;
    border: 2px solid #e9ecef;
    border-radius: 8px;
    transition: all 0.3s ease;
}

.rating-option:hover {
    border-color: #0d6efd;
    background-color: rgba(13, 110, 253, 0.1);
}

.rating-option input[type="radio"] {
    display: none;
}

.rating-option input[type="radio"]:checked + .rating-number {
    background-color: #0d6efd;
    color: white;
}

.rating-number {
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    background-color: #f8f9fa;
    font-weight: bold;
    font-size: 18px;
    transition: all 0.3s ease;
}
</style>

<?php 
// Set current poll ID for real-time updates
$inline_js = "window.currentPollId = $poll_id;";

include '../includes/footer.php'; 
?>
