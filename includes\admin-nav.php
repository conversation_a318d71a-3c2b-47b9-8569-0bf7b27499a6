<?php
// Get current page for active menu highlighting
$current_page = basename($_SERVER['PHP_SELF']);
?>

<nav class="navbar navbar-expand-lg navbar-dark bg-primary">
    <div class="container-fluid">
        <a class="navbar-brand" href="<?php echo APP_URL; ?>/admin/">
            <i class="fas fa-vote-yea"></i> OVS Admin
        </a>
        
        <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
            <span class="navbar-toggler-icon"></span>
        </button>
        
        <div class="collapse navbar-collapse" id="navbarNav">
            <ul class="navbar-nav me-auto">
                <li class="nav-item">
                    <a class="nav-link <?php echo $current_page === 'index.php' ? 'active' : ''; ?>" 
                       href="<?php echo APP_URL; ?>/admin/">
                        <i class="fas fa-tachometer-alt"></i> ড্যাশবোর্ড
                    </a>
                </li>
                
                <li class="nav-item">
                    <a class="nav-link <?php echo $current_page === 'voters.php' ? 'active' : ''; ?>" 
                       href="<?php echo APP_URL; ?>/admin/voters.php">
                        <i class="fas fa-users"></i> ভোটার ব্যবস্থাপনা
                    </a>
                </li>
                
                <li class="nav-item">
                    <a class="nav-link <?php echo $current_page === 'candidates.php' ? 'active' : ''; ?>"
                       href="<?php echo APP_URL; ?>/admin/candidates.php">
                        <i class="fas fa-user-tie"></i> প্রার্থী ব্যবস্থাপনা
                    </a>
                </li>

                <li class="nav-item">
                    <a class="nav-link <?php echo $current_page === 'positions.php' ? 'active' : ''; ?>"
                       href="<?php echo APP_URL; ?>/admin/positions.php">
                        <i class="fas fa-briefcase"></i> পদ ব্যবস্থাপনা
                    </a>
                </li>
                
                <li class="nav-item">
                    <a class="nav-link <?php echo $current_page === 'areas.php' ? 'active' : ''; ?>" 
                       href="<?php echo APP_URL; ?>/admin/areas.php">
                        <i class="fas fa-map"></i> এলাকা ব্যবস্থাপনা
                    </a>
                </li>
                
                <li class="nav-item dropdown">
                    <a class="nav-link dropdown-toggle" href="#" id="toolsDropdown" role="button" data-bs-toggle="dropdown">
                        <i class="fas fa-tools"></i> টুলস
                    </a>
                    <ul class="dropdown-menu">
                        <li><a class="dropdown-item" href="<?php echo APP_URL; ?>/admin/voter-id-generator.php">
                            <i class="fas fa-magic"></i> ভোটার আইডি জেনারেটর
                        </a></li>
                        <li><a class="dropdown-item" href="<?php echo APP_URL; ?>/admin/ward-code-demo.php">
                            <i class="fas fa-code"></i> ওয়ার্ড কোড ডেমো
                        </a></li>
                        <li><a class="dropdown-item" href="<?php echo APP_URL; ?>/admin/debug-upload.php">
                            <i class="fas fa-bug"></i> আপলোড ডিবাগ
                        </a></li>
                        <li><hr class="dropdown-divider"></li>
                        <li><a class="dropdown-item" href="<?php echo APP_URL; ?>/admin/duplicate-policy-demo.php">
                            <i class="fas fa-shield-alt"></i> ডুপ্লিকেট পলিসি
                        </a></li>
                    </ul>
                </li>
            </ul>
            
            <ul class="navbar-nav">
                <li class="nav-item dropdown">
                    <a class="nav-link dropdown-toggle" href="#" id="userDropdown" role="button" data-bs-toggle="dropdown">
                        <i class="fas fa-user"></i> <?php echo getCurrentUser()['username'] ?? 'Admin'; ?>
                    </a>
                    <ul class="dropdown-menu dropdown-menu-end">
                        <li><a class="dropdown-item" href="<?php echo APP_URL; ?>/admin/profile.php">
                            <i class="fas fa-user-cog"></i> প্রোফাইল
                        </a></li>
                        <li><a class="dropdown-item" href="<?php echo APP_URL; ?>/admin/settings.php">
                            <i class="fas fa-cog"></i> সেটিংস
                        </a></li>
                        <li><hr class="dropdown-divider"></li>
                        <li><a class="dropdown-item" href="<?php echo APP_URL; ?>/auth/logout.php">
                            <i class="fas fa-sign-out-alt"></i> লগআউট
                        </a></li>
                    </ul>
                </li>
            </ul>
        </div>
    </div>
</nav>
