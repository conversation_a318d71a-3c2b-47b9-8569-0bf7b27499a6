-- Voter Photo Feature Database Update
-- Add photo column and create uploads directory structure

-- Add photo column to voters table (if not exists)
ALTER TABLE `voters` 
ADD COLUMN `photo` varchar(255) DEFAULT NULL AFTER `address`;

-- Create voter_uploads table for tracking uploads
CREATE TABLE IF NOT EXISTS `voter_uploads` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `voter_id` int(11) NOT NULL,
  `original_filename` varchar(255) NOT NULL,
  `stored_filename` varchar(255) NOT NULL,
  `file_size` int(11) NOT NULL,
  `mime_type` varchar(100) NOT NULL,
  `upload_type` enum('photo','document') NOT NULL DEFAULT 'photo',
  `uploaded_by` int(11) NOT NULL,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  <PERSON>EY `voter_id` (`voter_id`),
  <PERSON><PERSON><PERSON> `uploaded_by` (`uploaded_by`),
  FOREIGN KEY (`voter_id`) R<PERSON>ERENCES `voters` (`id`) ON DELETE CASCADE,
  <PERSON>OR<PERSON><PERSON><PERSON> KEY (`uploaded_by`) REFERENCES `users` (`id`) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

COMMIT;
