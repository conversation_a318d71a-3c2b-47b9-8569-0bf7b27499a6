<?php
/**
 * Generate Voter ID API
 * AJAX endpoint for generating unique voter IDs
 */

header('Content-Type: application/json');
require_once '../config/config.php';
require_once '../includes/voter-id-generator.php';

// Check if user is logged in
if (!isLoggedIn()) {
    http_response_code(401);
    echo json_encode(['success' => false, 'message' => 'Unauthorized']);
    exit;
}

$action = $_GET['action'] ?? $_POST['action'] ?? '';

try {
    $db = getDB();
    
    switch ($action) {
        case 'generate':
            $format = $_GET['format'] ?? $_POST['format'] ?? 'V{YYYY}{MM}{RRRRR}';
            $area_id = intval($_GET['area_id'] ?? $_POST['area_id'] ?? 0);
            
            if ($area_id > 0) {
                $voter_id = generateVoterIDWithArea($db, $area_id, $format);
            } else {
                $voter_id = generateVoterID($db, $format);
            }
            
            echo json_encode([
                'success' => true,
                'voter_id' => $voter_id,
                'format' => $format
            ]);
            break;
            
        case 'bulk_generate':
            $count = intval($_GET['count'] ?? $_POST['count'] ?? 5);
            $format = $_GET['format'] ?? $_POST['format'] ?? 'V{YYYY}{MM}{RRRRR}';
            
            if ($count > 100) {
                $count = 100; // Limit to prevent abuse
            }
            
            $voter_ids = bulkGenerateVoterIDs($db, $count, $format);
            
            echo json_encode([
                'success' => true,
                'voter_ids' => $voter_ids,
                'count' => count($voter_ids),
                'format' => $format
            ]);
            break;
            
        case 'validate':
            $voter_id = $_GET['voter_id'] ?? $_POST['voter_id'] ?? '';
            
            if (empty($voter_id)) {
                echo json_encode(['success' => false, 'message' => 'Voter ID required']);
                break;
            }
            
            // Check format validation
            $validation = validateVoterID($voter_id);
            
            // Check uniqueness
            $stmt = $db->prepare("SELECT id FROM voters WHERE voter_id = ?");
            $stmt->execute([$voter_id]);
            $exists = $stmt->fetch();
            
            if ($exists) {
                $validation['unique'] = false;
                $validation['issues'][] = 'Voter ID already exists';
            } else {
                $validation['unique'] = true;
            }
            
            echo json_encode([
                'success' => true,
                'validation' => $validation
            ]);
            break;
            
        case 'get_formats':
            $formats = getVoterIDFormats();
            
            echo json_encode([
                'success' => true,
                'formats' => $formats
            ]);
            break;
            
        case 'preview':
            $format = $_GET['format'] ?? $_POST['format'] ?? 'V{YYYY}{MM}{RRRRR}';
            $area_id = intval($_GET['area_id'] ?? $_POST['area_id'] ?? 0);
            
            // Generate multiple examples
            $examples = [];
            for ($i = 0; $i < 5; $i++) {
                if ($area_id > 0) {
                    $examples[] = generateVoterIDPattern($format); // Don't check uniqueness for preview
                } else {
                    $examples[] = generateVoterIDPattern($format);
                }
            }
            
            echo json_encode([
                'success' => true,
                'examples' => $examples,
                'format' => $format
            ]);
            break;
            
        case 'get_next_sequential':
            // Get next sequential number for the current year
            $year = date('Y');
            $stmt = $db->prepare("
                SELECT MAX(CAST(SUBSTRING(voter_id, -4) AS UNSIGNED)) as max_seq 
                FROM voters 
                WHERE voter_id LIKE ? AND voter_id REGEXP '^V[0-9]{4}[0-9]{4}$'
            ");
            $stmt->execute(["V{$year}%"]);
            $result = $stmt->fetch();
            
            $next_seq = ($result['max_seq'] ?? 0) + 1;
            $voter_id = "V{$year}" . str_pad($next_seq, 4, '0', STR_PAD_LEFT);
            
            echo json_encode([
                'success' => true,
                'voter_id' => $voter_id,
                'sequence' => $next_seq
            ]);
            break;
            
        default:
            http_response_code(400);
            echo json_encode(['success' => false, 'message' => 'Invalid action']);
            break;
    }
    
} catch (Exception $e) {
    error_log("Voter ID generation error: " . $e->getMessage());
    http_response_code(500);
    echo json_encode(['success' => false, 'message' => 'Server error: ' . $e->getMessage()]);
}
?>
