<?php
/**
 * Modern Sidebar Demo Page
 * মডার্ন সাইডবার ডেমো পেজ
 */

require_once 'config/config.php';

$page_title = 'মডার্ন সাইডবার ডেমো - Modern Sidebar Demo';
$show_top_nav = true;

// Start output buffering for content
ob_start();
?>

<div class="container-fluid py-4">
    <!-- Header Section -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card bg-gradient-primary text-white">
                <div class="card-body text-center py-5">
                    <h1 class="display-4 mb-3">🎨 মডার্ন সাইডবার</h1>
                    <p class="lead mb-4">
                        আধুনিক এবং responsive সাইডবার যা সকল ডিভাইসে নিখুঁতভাবে কাজ করে
                    </p>
                    <div class="d-flex gap-3 justify-content-center flex-wrap">
                        <span class="badge bg-light text-dark px-3 py-2">
                            <i class="fas fa-mobile-alt"></i> Mobile Responsive
                        </span>
                        <span class="badge bg-light text-dark px-3 py-2">
                            <i class="fas fa-palette"></i> Modern Design
                        </span>
                        <span class="badge bg-light text-dark px-3 py-2">
                            <i class="fas fa-bolt"></i> Fast & Smooth
                        </span>
                        <span class="badge bg-light text-dark px-3 py-2">
                            <i class="fas fa-universal-access"></i> Accessible
                        </span>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Features Section -->
    <div class="row mb-4">
        <div class="col-md-4 mb-3">
            <div class="card h-100 border-start-primary">
                <div class="card-body">
                    <div class="text-primary mb-3">
                        <i class="fas fa-mobile-alt fa-3x"></i>
                    </div>
                    <h5 class="card-title">Responsive Design</h5>
                    <p class="card-text">
                        সাইডবার সকল ডিভাইসে নিখুঁতভাবে কাজ করে। মোবাইলে overlay হিসেবে এবং ডেস্কটপে fixed sidebar হিসেবে।
                    </p>
                </div>
            </div>
        </div>
        
        <div class="col-md-4 mb-3">
            <div class="card h-100 border-start-success">
                <div class="card-body">
                    <div class="text-success mb-3">
                        <i class="fas fa-paint-brush fa-3x"></i>
                    </div>
                    <h5 class="card-title">Modern UI</h5>
                    <p class="card-text">
                        আধুনিক gradient background, smooth animations এবং beautiful icons সহ আকর্ষণীয় ডিজাইন।
                    </p>
                </div>
            </div>
        </div>
        
        <div class="col-md-4 mb-3">
            <div class="card h-100 border-start-info">
                <div class="card-body">
                    <div class="text-info mb-3">
                        <i class="fas fa-cogs fa-3x"></i>
                    </div>
                    <h5 class="card-title">Easy Integration</h5>
                    <p class="card-text">
                        সহজেই যেকোনো পেজে integrate করা যায়। শুধু layout file include করলেই হবে।
                    </p>
                </div>
            </div>
        </div>
    </div>

    <!-- Navigation Features -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-list"></i> নেভিগেশন ফিচার
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <h6 class="text-primary">✨ মূল ফিচারসমূহ:</h6>
                            <ul class="list-unstyled">
                                <li class="mb-2">
                                    <i class="fas fa-check text-success"></i>
                                    Role-based menu items (Admin/User)
                                </li>
                                <li class="mb-2">
                                    <i class="fas fa-check text-success"></i>
                                    Active page highlighting
                                </li>
                                <li class="mb-2">
                                    <i class="fas fa-check text-success"></i>
                                    Smooth hover effects
                                </li>
                                <li class="mb-2">
                                    <i class="fas fa-check text-success"></i>
                                    User profile section
                                </li>
                                <li class="mb-2">
                                    <i class="fas fa-check text-success"></i>
                                    Quick action buttons
                                </li>
                            </ul>
                        </div>
                        <div class="col-md-6">
                            <h6 class="text-info">📱 Responsive Features:</h6>
                            <ul class="list-unstyled">
                                <li class="mb-2">
                                    <i class="fas fa-check text-success"></i>
                                    Mobile-first design
                                </li>
                                <li class="mb-2">
                                    <i class="fas fa-check text-success"></i>
                                    Touch-friendly interface
                                </li>
                                <li class="mb-2">
                                    <i class="fas fa-check text-success"></i>
                                    Overlay on mobile devices
                                </li>
                                <li class="mb-2">
                                    <i class="fas fa-check text-success"></i>
                                    Keyboard navigation support
                                </li>
                                <li class="mb-2">
                                    <i class="fas fa-check text-success"></i>
                                    Auto-close on mobile
                                </li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Usage Instructions -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-code"></i> কিভাবে ব্যবহার করবেন
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <h6 class="text-primary">১. Layout Include করুন:</h6>
                            <pre class="bg-light p-3 rounded"><code>&lt;?php
$page_title = 'Your Page Title';
$show_top_nav = true;

// Content
ob_start();
?&gt;
&lt;div class="container-fluid"&gt;
    &lt;!-- Your content here --&gt;
&lt;/div&gt;
&lt;?php
$content = ob_get_clean();
include 'includes/sidebar-layout.php';
?&gt;</code></pre>
                        </div>
                        <div class="col-md-6">
                            <h6 class="text-success">২. CSS Classes:</h6>
                            <ul class="list-unstyled">
                                <li><code>.modern-sidebar</code> - Main sidebar</li>
                                <li><code>.sidebar-active</code> - Active state</li>
                                <li><code>.nav-link.active</code> - Active menu item</li>
                                <li><code>.mobile-sidebar-toggle</code> - Mobile toggle</li>
                            </ul>
                            
                            <h6 class="text-info mt-3">৩. JavaScript Functions:</h6>
                            <ul class="list-unstyled">
                                <li><code>APP.setupSidebar()</code> - Initialize</li>
                                <li><code>APP.updateActiveNavItem()</code> - Update active</li>
                                <li><code>APP.updateSidebarBadges()</code> - Update badges</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Demo Actions -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-play"></i> ডেমো অ্যাকশন
                    </h5>
                </div>
                <div class="card-body text-center">
                    <p class="mb-4">নিচের বাটনগুলো ক্লিক করে সাইডবারের বিভিন্ন ফিচার টেস্ট করুন:</p>
                    
                    <div class="d-flex gap-3 justify-content-center flex-wrap">
                        <button class="btn btn-primary" onclick="toggleSidebar()">
                            <i class="fas fa-bars"></i> Toggle Sidebar
                        </button>
                        <button class="btn btn-success" onclick="highlightActiveItem()">
                            <i class="fas fa-highlight"></i> Highlight Active
                        </button>
                        <button class="btn btn-info" onclick="addNotificationBadge()">
                            <i class="fas fa-bell"></i> Add Badge
                        </button>
                        <button class="btn btn-warning" onclick="animateNavItems()">
                            <i class="fas fa-magic"></i> Animate Items
                        </button>
                    </div>
                    
                    <div class="mt-4">
                        <small class="text-muted">
                            <i class="fas fa-info-circle"></i>
                            মোবাইল ডিভাইসে সাইডবার টগল করতে উপরের বাম কোণের বাটন ব্যবহার করুন
                        </small>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// Demo functions
function toggleSidebar() {
    $('#modernSidebar').toggleClass('active');
    $('#sidebarOverlay').toggleClass('active');
}

function highlightActiveItem() {
    $('.nav-link').removeClass('active');
    $('.nav-link').first().addClass('active');
    APP.showAlert('Active item highlighted!', 'success', 2000);
}

function addNotificationBadge() {
    const dashboardLink = $('.nav-link[href*="dashboard"]');
    dashboardLink.find('.badge').remove();
    dashboardLink.append('<span class="badge bg-danger ms-auto">5</span>');
    APP.showAlert('Notification badge added!', 'info', 2000);
}

function animateNavItems() {
    $('.nav-item').each(function(index) {
        $(this).delay(index * 100).queue(function() {
            $(this).addClass('animate__animated animate__pulse');
            setTimeout(() => {
                $(this).removeClass('animate__animated animate__pulse');
            }, 1000);
            $(this).dequeue();
        });
    });
    APP.showAlert('Navigation items animated!', 'warning', 2000);
}
</script>

<?php
$content = ob_get_clean();

// Additional CSS for animations
$additional_head = '<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/animate.css/4.1.1/animate.min.css">';

// Include the sidebar layout
include 'includes/sidebar-layout.php';
?>
