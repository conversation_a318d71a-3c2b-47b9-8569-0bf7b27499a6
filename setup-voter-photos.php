<?php
/**
 * Voter Photos Setup Script
 * Setup database and directories for voter photos
 */

require_once 'config/config.php';

// Check if user is admin
if (!isLoggedIn() || !isAdmin()) {
    echo "<h2>Access Denied</h2>";
    echo "<p>Please login as admin first: <a href='auth/login.php'>Login</a></p>";
    exit;
}

echo "<h2>📸 ভোটার ছবি ফিচার সেটআপ</h2>";

try {
    $db = getDB();
    
    if (isset($_POST['setup_photos'])) {
        echo "<h3>🔧 Setting up voter photos...</h3>";
        
        // 1. Add photo column to voters table
        try {
            $db->exec("ALTER TABLE `voters` ADD COLUMN `photo` varchar(255) DEFAULT NULL AFTER `address`");
            echo "<div style='background: #d4edda; padding: 10px; margin: 5px 0; border-radius: 3px;'>✅ Added photo column to voters table</div>";
        } catch (Exception $e) {
            if (strpos($e->getMessage(), 'Duplicate column name') !== false) {
                echo "<div style='background: #fff3cd; padding: 10px; margin: 5px 0; border-radius: 3px;'>⚠️ Photo column already exists</div>";
            } else {
                throw $e;
            }
        }
        
        // 2. Create voter_uploads table
        try {
            $db->exec("
                CREATE TABLE IF NOT EXISTS `voter_uploads` (
                  `id` int(11) NOT NULL AUTO_INCREMENT,
                  `voter_id` int(11) NOT NULL,
                  `original_filename` varchar(255) NOT NULL,
                  `stored_filename` varchar(255) NOT NULL,
                  `file_size` int(11) NOT NULL,
                  `mime_type` varchar(100) NOT NULL,
                  `upload_type` enum('photo','document') NOT NULL DEFAULT 'photo',
                  `uploaded_by` int(11) NOT NULL,
                  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
                  PRIMARY KEY (`id`),
                  KEY `voter_id` (`voter_id`),
                  KEY `uploaded_by` (`uploaded_by`)
                ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
            ");
            echo "<div style='background: #d4edda; padding: 10px; margin: 5px 0; border-radius: 3px;'>✅ Created voter_uploads table</div>";
        } catch (Exception $e) {
            echo "<div style='background: #fff3cd; padding: 10px; margin: 5px 0; border-radius: 3px;'>⚠️ voter_uploads table already exists</div>";
        }
        
        // 3. Create upload directories
        $upload_dir = __DIR__ . '/uploads/voters/';
        if (!file_exists($upload_dir)) {
            if (mkdir($upload_dir, 0755, true)) {
                echo "<div style='background: #d4edda; padding: 10px; margin: 5px 0; border-radius: 3px;'>✅ Created uploads/voters/ directory</div>";
            } else {
                echo "<div style='background: #f8d7da; padding: 10px; margin: 5px 0; border-radius: 3px;'>❌ Failed to create uploads directory</div>";
            }
        } else {
            echo "<div style='background: #fff3cd; padding: 10px; margin: 5px 0; border-radius: 3px;'>⚠️ Upload directory already exists</div>";
        }
        
        // 4. Create .htaccess for security
        $htaccess_content = '# Voter Photos Directory Security
# Allow only image files

<Files "*">
    Order Deny,Allow
    Deny from all
</Files>

<FilesMatch "\.(jpg|jpeg|png|gif|webp)$">
    Order Allow,Deny
    Allow from all
</FilesMatch>

# Prevent PHP execution
<FilesMatch "\.php$">
    Order Deny,Allow
    Deny from all
</FilesMatch>';

        $htaccess_file = $upload_dir . '.htaccess';
        if (file_put_contents($htaccess_file, $htaccess_content)) {
            echo "<div style='background: #d4edda; padding: 10px; margin: 5px 0; border-radius: 3px;'>✅ Created security .htaccess file</div>";
        } else {
            echo "<div style='background: #f8d7da; padding: 10px; margin: 5px 0; border-radius: 3px;'>❌ Failed to create .htaccess file</div>";
        }
        
        // 5. Create default avatar
        $assets_dir = __DIR__ . '/assets/images/';
        if (!file_exists($assets_dir)) {
            mkdir($assets_dir, 0755, true);
        }
        
        $default_avatar = $assets_dir . 'default-avatar.png';
        if (!file_exists($default_avatar)) {
            // Create a simple default avatar using GD
            if (extension_loaded('gd')) {
                $img = imagecreatetruecolor(150, 150);
                $bg_color = imagecolorallocate($img, 229, 231, 235); // Light gray
                $icon_color = imagecolorallocate($img, 156, 163, 175); // Darker gray
                
                imagefill($img, 0, 0, $bg_color);
                
                // Draw simple person icon
                imagefilledellipse($img, 75, 50, 40, 40, $icon_color); // Head
                imagefilledellipse($img, 75, 120, 80, 60, $icon_color); // Body
                
                imagepng($img, $default_avatar);
                imagedestroy($img);
                
                echo "<div style='background: #d4edda; padding: 10px; margin: 5px 0; border-radius: 3px;'>✅ Created default avatar image</div>";
            } else {
                echo "<div style='background: #fff3cd; padding: 10px; margin: 5px 0; border-radius: 3px;'>⚠️ GD extension not available, default avatar not created</div>";
            }
        }
        
        // 6. Test photo upload functionality
        echo "<div style='background: #e7f3ff; padding: 15px; border-radius: 5px; margin: 15px 0;'>";
        echo "<h4>📋 Setup Summary:</h4>";
        echo "<ul>";
        echo "<li>✅ Database tables updated</li>";
        echo "<li>✅ Upload directory created</li>";
        echo "<li>✅ Security configured</li>";
        echo "<li>✅ Default avatar ready</li>";
        echo "</ul>";
        echo "</div>";
        
        echo "<div style='background: #d4edda; padding: 15px; border-radius: 5px; margin: 15px 0;'>";
        echo "<h4>🎉 Voter Photos Feature Ready!</h4>";
        echo "<p>এখন আপনি ভোটার ম্যানেজমেন্ট পেজে গিয়ে ছবি আপলোড করতে পারবেন।</p>";
        echo "</div>";
    }
    
    // Check current status
    echo "<h3>📊 Current Status:</h3>";
    
    // Check photo column
    $stmt = $db->query("SHOW COLUMNS FROM voters LIKE 'photo'");
    $photo_column = $stmt->fetch();
    
    echo "<ul>";
    echo "<li>Photo column in voters table: " . ($photo_column ? '✅ Exists' : '❌ Missing') . "</li>";
    
    // Check voter_uploads table
    $stmt = $db->query("SHOW TABLES LIKE 'voter_uploads'");
    $uploads_table = $stmt->fetch();
    echo "<li>voter_uploads table: " . ($uploads_table ? '✅ Exists' : '❌ Missing') . "</li>";
    
    // Check upload directory
    $upload_dir = __DIR__ . '/uploads/voters/';
    echo "<li>Upload directory: " . (file_exists($upload_dir) ? '✅ Exists' : '❌ Missing') . "</li>";
    
    // Check .htaccess
    $htaccess_file = $upload_dir . '.htaccess';
    echo "<li>Security .htaccess: " . (file_exists($htaccess_file) ? '✅ Exists' : '❌ Missing') . "</li>";
    
    // Check default avatar
    $default_avatar = __DIR__ . '/assets/images/default-avatar.png';
    echo "<li>Default avatar: " . (file_exists($default_avatar) ? '✅ Exists' : '❌ Missing') . "</li>";
    echo "</ul>";
    
    // Show setup button if needed
    if (!$photo_column || !$uploads_table || !file_exists($upload_dir)) {
        echo "<div style='background: #fff3cd; padding: 15px; border-radius: 5px; margin: 15px 0;'>";
        echo "<h4>⚠️ Setup Required</h4>";
        echo "<p>ভোটার ছবি ফিচার ব্যবহার করার জন্য সেটআপ সম্পন্ন করুন।</p>";
        echo "</div>";
        
        echo "<form method='POST'>";
        echo "<button type='submit' name='setup_photos' style='background: #28a745; color: white; padding: 15px 30px; border: none; border-radius: 5px; font-size: 16px; cursor: pointer;'>";
        echo "📸 Setup Voter Photos";
        echo "</button>";
        echo "</form>";
    } else {
        echo "<div style='background: #d4edda; padding: 15px; border-radius: 5px; margin: 15px 0;'>";
        echo "<h4>✅ All Ready!</h4>";
        echo "<p>ভোটার ছবি ফিচার সম্পূর্ণ প্রস্তুত।</p>";
        echo "</div>";
    }
    
} catch (Exception $e) {
    echo "<div style='background: #f8d7da; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
    echo "<h4>❌ Database Error!</h4>";
    echo "<p>Error: " . $e->getMessage() . "</p>";
    echo "</div>";
}

echo "<br><h3>🔗 Next Steps:</h3>";
echo "<ol>";
echo "<li><a href='admin/voters.php'>Go to Voter Management</a></li>";
echo "<li>Click camera icon next to any voter</li>";
echo "<li>Upload voter photos</li>";
echo "<li>Test photo display and deletion</li>";
echo "</ol>";

echo "<br><div style='background: #e2e3e5; padding: 10px; border-radius: 5px;'>";
echo "<strong>Security Note:</strong> Delete this file after setup.";
echo "</div>";
?>
