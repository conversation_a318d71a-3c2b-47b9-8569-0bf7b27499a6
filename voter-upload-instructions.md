# ভোটার আপলোড নির্দেশনা
## Voter Upload Instructions

### 📋 **CSV ফরম্যাট নির্দেশনা**

#### **Required Headers (প্রয়োজনীয় কলাম):**
```
voter_id,name,name_bn,father_name,mother_name,nid,mobile,email,date_of_birth,gender,area_id,address
```

#### **Sample Data (নমুনা ডেটা):**

| voter_id | name | name_bn | father_name | mother_name | nid | mobile | email | date_of_birth | gender | area_id | address |
|----------|------|---------|-------------|-------------|-----|--------|-------|---------------|--------|---------|---------|
| V001 | M<PERSON><PERSON> <PERSON> | মোঃ রহমান খান | <PERSON><PERSON><PERSON> <PERSON> | Ms<PERSON>. <PERSON><PERSON> | 1234567890123 | 01712345678 | <EMAIL> | 1985-05-15 | male | 23 | বাড়ি নং ১২৩, রোড নং ৫, ধানমন্ডি, ঢাকা |
| V002 | Mst. Salma Begum | মোসাঃ সালমা বেগম | Md. Karim Uddin | Mst. Rashida Begum | 2345678901234 | 01823456789 | <EMAIL> | 1990-08-22 | female | 24 | বাড়ি নং ৪৫৬, রোড নং ৭, ধানমন্ডি, ঢাকা |
| V003 | Md. Kamal Hossain | মোঃ কামাল হোসেন | Md. Nazrul Islam | Mst. Rahima Khatun | 3456789012345 | 01934567890 | <EMAIL> | 1988-12-10 | male | 25 | বাড়ি নং ৭৮৯, রোড নং ৯, গুলশান, ঢাকা |
| V004 | Mst. Nasreen Akter | মোসাঃ নাসরিন আক্তার | Md. Shahid Ullah | Mst. Jahanara Begum | 4567890123456 | 01645678901 | <EMAIL> | 1992-03-18 | female | 26 | বাড়ি নং ১০১, রোড নং ১১, গুলশান, ঢাকা |
| V005 | Md. Aminul Islam | মোঃ আমিনুল ইসলাম | Md. Rafiqul Islam | Mst. Rokeya Begum | 5678901234567 | 01756789012 | <EMAIL> | 1987-07-25 | male | 27 | বাড়ি নং ২০২, রোড নং ১৩, বনানী, ঢাকা |

### 📝 **Field Descriptions (ক্ষেত্রের বিবরণ):**

#### **Required Fields (আবশ্যক):**
- **voter_id**: Unique voter identifier (V001, V002, etc.)
- **name**: Full name in English
- **nid**: National ID number (13-17 digits)
- **mobile**: Mobile number (11 digits, starting with 01)
- **area_id**: Area ID from areas table

#### **Optional Fields (ঐচ্ছিক):**
- **name_bn**: Name in Bengali
- **father_name**: Father's name
- **mother_name**: Mother's name
- **email**: Email address
- **date_of_birth**: Date in YYYY-MM-DD format
- **gender**: male/female
- **address**: Full address

### ⚠️ **Important Rules (গুরুত্বপূর্ণ নিয়ম):**

1. **Unique Fields:**
   - voter_id must be unique
   - nid must be unique
   - mobile must be unique

2. **Data Format:**
   - Date: YYYY-MM-DD (e.g., 1985-05-15)
   - Mobile: 11 digits (e.g., 01712345678)
   - NID: 10-17 digits
   - Gender: male or female

3. **Area ID:**
   - Must exist in areas table
   - Download areas reference list first

### 🔧 **How to Use:**

1. **Download Template:**
   - Click "টেমপ্লেট ডাউনলোড (CSV)"
   - Open in Excel/LibreOffice

2. **Download Areas List:**
   - Click "এলাকা তালিকা (CSV)"
   - Find correct area_id for each voter

3. **Edit Data:**
   - Replace sample data with real data
   - Keep headers unchanged
   - Ensure all required fields are filled

4. **Save as CSV:**
   - File → Save As → CSV (UTF-8)
   - Keep UTF-8 encoding for Bengali text

5. **Upload:**
   - Click "ভোটার আপলোড"
   - Select your CSV file
   - Click upload

### 🎯 **Sample Area IDs:**

| area_id | area_name | type |
|---------|-----------|------|
| 23 | ধানমন্ডি ওয়ার্ড ০১ | ward |
| 24 | ধানমন্ডি ওয়ার্ড ০২ | ward |
| 25 | ধানমন্ডি ওয়ার্ড ০৩ | ward |
| 26 | গুলশান ওয়ার্ড ০১ | ward |
| 27 | গুলশান ওয়ার্ড ০২ | ward |

### 🚨 **Common Errors:**

1. **Duplicate voter_id**: Each voter must have unique ID
2. **Invalid area_id**: Area must exist in system
3. **Wrong date format**: Use YYYY-MM-DD only
4. **Missing required fields**: Fill all required columns
5. **Encoding issues**: Save as UTF-8 CSV

### 📞 **Support:**

If you encounter any issues:
1. Check the upload help modal
2. Verify your CSV format
3. Ensure all required fields are present
4. Contact system administrator
