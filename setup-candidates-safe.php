<?php
/**
 * Safe Candidates Setup
 * Alternative setup without foreign key constraints
 */

require_once 'config/config.php';

try {
    $db = getDB();
    
    echo "<h2>🗳️ Safe Candidates System Setup...</h2>";
    
    // Drop existing tables if they exist (for clean setup)
    echo "<h3>🧹 Cleaning up existing tables...</h3>";
    $tables_to_drop = ['candidate_elections', 'candidates', 'positions', 'election_symbols'];
    
    foreach ($tables_to_drop as $table) {
        try {
            $db->exec("DROP TABLE IF EXISTS $table");
            echo "<p>🗑️ Dropped table: $table</p>";
        } catch (Exception $e) {
            echo "<p>ℹ️ Table $table didn't exist or couldn't be dropped</p>";
        }
    }
    
    // Create positions table (no foreign keys)
    echo "<h3>🏛️ Creating Positions Table...</h3>";
    $sql = "CREATE TABLE positions (
        id INT AUTO_INCREMENT PRIMARY KEY,
        position_code VARCHAR(20) UNIQUE NOT NULL,
        title VARCHAR(255) NOT NULL,
        title_bn VARCHAR(255),
        description TEXT,
        description_bn TEXT,
        election_level ENUM('ward', 'village', 'upazila', 'district', 'national') NOT NULL,
        area_type ENUM('ward', 'village', 'union', 'municipality', 'upazila', 'district', 'division', 'national') DEFAULT NULL,
        is_active TINYINT(1) DEFAULT 1,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";
    
    $db->exec($sql);
    echo "<p>✅ Positions table created!</p>";
    
    // Create election symbols table
    echo "<h3>🎯 Creating Election Symbols Table...</h3>";
    $sql = "CREATE TABLE election_symbols (
        id INT AUTO_INCREMENT PRIMARY KEY,
        name VARCHAR(100) NOT NULL,
        name_bn VARCHAR(100),
        symbol_file VARCHAR(255),
        is_available TINYINT(1) DEFAULT 1,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";
    
    $db->exec($sql);
    echo "<p>✅ Election Symbols table created!</p>";
    
    // Create candidates table (no foreign keys initially)
    echo "<h3>👥 Creating Candidates Table...</h3>";
    $sql = "CREATE TABLE candidates (
        id INT AUTO_INCREMENT PRIMARY KEY,
        candidate_id VARCHAR(50) UNIQUE NOT NULL,
        name VARCHAR(255) NOT NULL,
        name_bn VARCHAR(255),
        photo VARCHAR(255),
        symbol VARCHAR(255),
        symbol_name VARCHAR(255),
        symbol_name_bn VARCHAR(255),
        election_level ENUM('ward', 'village', 'upazila', 'district', 'national') NOT NULL,
        area_id INT DEFAULT NULL,
        position_id INT DEFAULT NULL,
        position VARCHAR(100),
        position_bn VARCHAR(100),
        party_name VARCHAR(255),
        party_name_bn VARCHAR(255),
        description TEXT,
        description_bn TEXT,
        contact_mobile VARCHAR(20),
        contact_email VARCHAR(255),
        is_active TINYINT(1) DEFAULT 1,
        created_by INT DEFAULT NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";
    
    $db->exec($sql);
    echo "<p>✅ Candidates table created!</p>";
    
    // Create candidate elections table
    echo "<h3>🗳️ Creating Candidate Elections Table...</h3>";
    $sql = "CREATE TABLE candidate_elections (
        id INT AUTO_INCREMENT PRIMARY KEY,
        candidate_id INT NOT NULL,
        position_id INT DEFAULT NULL,
        election_id INT DEFAULT NULL,
        election_name VARCHAR(255) NOT NULL,
        election_name_bn VARCHAR(255),
        election_date DATE,
        status ENUM('registered', 'approved', 'rejected', 'withdrawn') DEFAULT 'registered',
        vote_count INT DEFAULT 0,
        is_winner TINYINT(1) DEFAULT 0,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";
    
    $db->exec($sql);
    echo "<p>✅ Candidate Elections table created!</p>";
    
    // Create upload directories
    echo "<h3>📁 Creating Upload Directories...</h3>";
    $upload_dirs = [
        'uploads/candidates/photos',
        'uploads/candidates/symbols',
        'uploads/candidates/thumbnails'
    ];
    
    foreach ($upload_dirs as $dir) {
        if (!file_exists($dir)) {
            mkdir($dir, 0755, true);
            echo "<p>✅ Created directory: $dir</p>";
        } else {
            echo "<p>ℹ️ Directory already exists: $dir</p>";
        }
    }
    
    // Insert sample positions
    echo "<h3>🏛️ Adding Sample Positions...</h3>";
    
    $sample_positions = [
        // Ward Level
        ['WC', 'Ward Commissioner', 'ওয়ার্ড কমিশনার', 'Elected representative of a ward', 'ওয়ার্ডের নির্বাচিত প্রতিনিধি', 'ward', 'ward'],
        ['WM', 'Ward Member', 'ওয়ার্ড সদস্য', 'Member of ward council', 'ওয়ার্ড কাউন্সিলের সদস্য', 'ward', 'ward'],
        
        // Village Level
        ['VP', 'Village Chairman', 'গ্রাম চেয়ারম্যান', 'Chairman of village council', 'গ্রাম কাউন্সিলের চেয়ারম্যান', 'village', 'village'],
        ['VM', 'Village Member', 'গ্রাম সদস্য', 'Member of village council', 'গ্রাম কাউন্সিলের সদস্য', 'village', 'village'],
        
        // Union Level
        ['UC', 'Union Chairman', 'ইউনিয়ন চেয়ারম্যান', 'Chairman of union parishad', 'ইউনিয়ন পরিষদের চেয়ারম্যান', 'village', 'union'],
        ['UM', 'Union Member', 'ইউনিয়ন সদস্য', 'Member of union parishad', 'ইউনিয়ন পরিষদের সদস্য', 'village', 'union'],
        ['UWM', 'Union Women Member', 'ইউনিয়ন মহিলা সদস্য', 'Women reserved member of union parishad', 'ইউনিয়ন পরিষদের মহিলা সংরক্ষিত সদস্য', 'village', 'union'],
        
        // Municipality Level
        ['MC', 'Mayor', 'মেয়র', 'Mayor of municipality', 'পৌরসভার মেয়র', 'village', 'municipality'],
        ['CC', 'City Councilor', 'সিটি কাউন্সিলর', 'Councilor of city corporation', 'সিটি কর্পোরেশনের কাউন্সিলর', 'village', 'municipality'],
        
        // Upazila Level
        ['UZC', 'Upazila Chairman', 'উপজেলা চেয়ারম্যান', 'Chairman of upazila parishad', 'উপজেলা পরিষদের চেয়ারম্যান', 'upazila', 'upazila'],
        ['UZVP', 'Upazila Vice Chairman', 'উপজেলা ভাইস চেয়ারম্যান', 'Vice Chairman of upazila parishad', 'উপজেলা পরিষদের ভাইস চেয়ারম্যান', 'upazila', 'upazila'],
        ['UZWVP', 'Upazila Women Vice Chairman', 'উপজেলা মহিলা ভাইস চেয়ারম্যান', 'Women Vice Chairman of upazila parishad', 'উপজেলা পরিষদের মহিলা ভাইস চেয়ারম্যান', 'upazila', 'upazila'],
        
        // District Level
        ['DC', 'District Commissioner', 'জেলা প্রশাসক', 'Chief administrative officer of district', 'জেলার প্রধান প্রশাসনিক কর্মকর্তা', 'district', 'district'],
        ['ZPC', 'Zilla Parishad Chairman', 'জেলা পরিষদ চেয়ারম্যান', 'Chairman of zilla parishad', 'জেলা পরিষদের চেয়ারম্যান', 'district', 'district'],
        
        // National Level
        ['MP', 'Member of Parliament', 'সংসদ সদস্য', 'Elected member of national parliament', 'জাতীয় সংসদের নির্বাচিত সদস্য', 'national', 'national'],
        ['PM', 'Prime Minister', 'প্রধানমন্ত্রী', 'Head of government', 'সরকার প্রধান', 'national', 'national'],
        ['PRES', 'President', 'রাষ্ট্রপতি', 'Head of state', 'রাষ্ট্র প্রধান', 'national', 'national']
    ];
    
    $stmt = $db->prepare("INSERT INTO positions (position_code, title, title_bn, description, description_bn, election_level, area_type) VALUES (?, ?, ?, ?, ?, ?, ?)");
    
    $position_count = 0;
    foreach ($sample_positions as $position) {
        try {
            $stmt->execute($position);
            $position_count++;
        } catch (Exception $e) {
            echo "<p>⚠️ Could not insert position {$position[0]}: " . $e->getMessage() . "</p>";
        }
    }
    
    echo "<p>✅ Added $position_count sample positions!</p>";
    
    // Insert sample symbols
    echo "<h3>🎯 Adding Sample Election Symbols...</h3>";
    
    $sample_symbols = [
        ['Boat', 'নৌকা', 'boat.png'],
        ['Rice Sheaf', 'ধানের শীষ', 'rice.png'],
        ['Tiger', 'বাঘ', 'tiger.png'],
        ['Elephant', 'হাতি', 'elephant.png'],
        ['Lotus', 'পদ্ম', 'lotus.png'],
        ['Eagle', 'ঈগল', 'eagle.png'],
        ['Rose', 'গোলাপ', 'rose.png'],
        ['Star', 'তারা', 'star.png'],
        ['Sun', 'সূর্য', 'sun.png'],
        ['Moon', 'চাঁদ', 'moon.png']
    ];
    
    $stmt = $db->prepare("INSERT INTO election_symbols (name, name_bn, symbol_file) VALUES (?, ?, ?)");
    
    $symbol_count = 0;
    foreach ($sample_symbols as $symbol) {
        try {
            $stmt->execute($symbol);
            $symbol_count++;
        } catch (Exception $e) {
            echo "<p>⚠️ Could not insert symbol {$symbol[0]}: " . $e->getMessage() . "</p>";
        }
    }
    
    echo "<p>✅ Added $symbol_count sample symbols!</p>";
    
    echo "<div style='background: #d4edda; padding: 20px; border-radius: 10px; margin: 20px 0;'>";
    echo "<h3>🎉 Safe Candidates System Setup Complete!</h3>";
    echo "<p><strong>✅ What was created:</strong></p>";
    echo "<ul>";
    echo "<li>✅ Positions table with $position_count positions</li>";
    echo "<li>✅ Election symbols table with $symbol_count symbols</li>";
    echo "<li>✅ Candidates table (ready for use)</li>";
    echo "<li>✅ Candidate elections table</li>";
    echo "<li>✅ Upload directories created</li>";
    echo "</ul>";
    echo "<p><strong>⚠️ Note:</strong> Foreign key constraints were skipped to avoid errors. The system will work perfectly without them.</p>";
    echo "<p><strong>🔗 Next Steps:</strong></p>";
    echo "<ul>";
    echo "<li><a href='admin/positions.php'>Manage Positions</a></li>";
    echo "<li><a href='admin/candidates.php'>Manage Candidates</a></li>";
    echo "<li><a href='admin/candidate-cards.php'>View Candidate Cards</a></li>";
    echo "</ul>";
    echo "</div>";
    
} catch (Exception $e) {
    echo "<div style='background: #f8d7da; padding: 20px; border-radius: 10px;'>";
    echo "<h3>❌ Error during safe setup:</h3>";
    echo "<p>" . $e->getMessage() . "</p>";
    echo "<p><strong>💡 Troubleshooting:</strong></p>";
    echo "<ul>";
    echo "<li>Check database connection</li>";
    echo "<li>Verify database user has CREATE/DROP privileges</li>";
    echo "<li>Check if database name is correct</li>";
    echo "<li>Try refreshing the page</li>";
    echo "</ul>";
    echo "</div>";
}
?>

<!DOCTYPE html>
<html>
<head>
    <title>Safe Candidates Setup</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
</head>
<body>
<div class="container mt-4">
    <div class="row">
        <div class="col-12">
            <!-- Content generated above -->
        </div>
    </div>
</div>
</body>
</html>
