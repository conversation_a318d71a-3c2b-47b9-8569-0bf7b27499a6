<?php
/**
 * Database Connection Test
 * Use this file to test your database configuration
 */

// Display all errors
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h2>Database Connection Test</h2>";

// Test basic PHP info
echo "<h3>PHP Information</h3>";
echo "PHP Version: " . phpversion() . "<br>";
echo "PDO Available: " . (extension_loaded('pdo') ? 'Yes' : 'No') . "<br>";
echo "PDO MySQL Available: " . (extension_loaded('pdo_mysql') ? 'Yes' : 'No') . "<br><br>";

// Test database configuration
echo "<h3>Database Configuration</h3>";

// Database settings (update these to match your setup)
$db_host = 'localhost';
$db_name = 'ovs_db';
$db_user = 'root';
$db_pass = '';
$db_charset = 'utf8mb4';

echo "Host: $db_host<br>";
echo "Database: $db_name<br>";
echo "Username: $db_user<br>";
echo "Password: " . (empty($db_pass) ? '(empty)' : '(set)') . "<br>";
echo "Charset: $db_charset<br><br>";

// Test connection
echo "<h3>Connection Test</h3>";

try {
    $dsn = "mysql:host=$db_host;dbname=$db_name;charset=$db_charset";
    $pdo_options = [
        PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
        PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
        PDO::ATTR_EMULATE_PREPARES => false,
    ];
    
    echo "Attempting to connect...<br>";
    $pdo = new PDO($dsn, $db_user, $db_pass, $pdo_options);
    echo "<span style='color: green;'>✓ Database connection successful!</span><br><br>";
    
    // Test if tables exist
    echo "<h3>Table Check</h3>";
    $tables = ['users', 'polls', 'poll_options', 'votes', 'poll_analytics'];
    
    foreach ($tables as $table) {
        try {
            $stmt = $pdo->query("SELECT COUNT(*) FROM $table");
            $count = $stmt->fetchColumn();
            echo "<span style='color: green;'>✓ Table '$table' exists with $count records</span><br>";
        } catch (PDOException $e) {
            echo "<span style='color: red;'>✗ Table '$table' not found or error: " . $e->getMessage() . "</span><br>";
        }
    }
    
    echo "<br><h3>Sample Data Check</h3>";
    
    // Check for admin user
    try {
        $stmt = $pdo->prepare("SELECT username, email, role FROM users WHERE role = 'admin' LIMIT 1");
        $stmt->execute();
        $admin = $stmt->fetch();
        
        if ($admin) {
            echo "<span style='color: green;'>✓ Admin user found: " . $admin['username'] . " (" . $admin['email'] . ")</span><br>";
        } else {
            echo "<span style='color: orange;'>⚠ No admin user found. You may need to import seeds.sql</span><br>";
        }
    } catch (PDOException $e) {
        echo "<span style='color: red;'>✗ Error checking admin user: " . $e->getMessage() . "</span><br>";
    }
    
    // Check for sample polls
    try {
        $stmt = $pdo->query("SELECT COUNT(*) FROM polls");
        $pollCount = $stmt->fetchColumn();
        
        if ($pollCount > 0) {
            echo "<span style='color: green;'>✓ Found $pollCount sample polls</span><br>";
        } else {
            echo "<span style='color: orange;'>⚠ No polls found. You may need to import seeds.sql</span><br>";
        }
    } catch (PDOException $e) {
        echo "<span style='color: red;'>✗ Error checking polls: " . $e->getMessage() . "</span><br>";
    }
    
} catch (PDOException $e) {
    echo "<span style='color: red;'>✗ Database connection failed!</span><br>";
    echo "<strong>Error:</strong> " . $e->getMessage() . "<br><br>";
    
    echo "<h3>Troubleshooting Steps:</h3>";
    echo "<ol>";
    echo "<li>Make sure MySQL/MariaDB is running in XAMPP</li>";
    echo "<li>Check if the database 'ovs_db' exists in phpMyAdmin</li>";
    echo "<li>Verify the username and password are correct</li>";
    echo "<li>Import the database schema from database/schema.sql</li>";
    echo "<li>Optionally import sample data from database/seeds.sql</li>";
    echo "</ol>";
}

echo "<br><h3>Next Steps</h3>";
echo "<p>If the connection is successful:</p>";
echo "<ol>";
echo "<li>Update config/config.php with your database settings</li>";
echo "<li>Visit <a href='index.php'>index.php</a> to see the main site</li>";
echo "<li>Try logging in with demo account: <EMAIL> / admin123</li>";
echo "</ol>";

echo "<br><p><a href='index.php'>← Back to Main Site</a></p>";
?>
