<?php
/**
 * এলাকা/ওয়ার্ড ম্যানেজমেন্ট পেজ
 * Area/Ward Management Page
 */

require_once '../config/config.php';

// Require admin access
if (!isLoggedIn() || !isAdmin()) {
    redirect(APP_URL . '/auth/login.php');
}

$action = $_GET['action'] ?? 'list';
$error = '';

// Handle actions
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (!validateCSRFToken($_POST['csrf_token'] ?? '')) {
        $error = 'Invalid request. Please try again.';
    } else {
        switch ($action) {
            case 'add':
                $result = addArea($_POST);
                if ($result['success']) {
                    setFlashMessage('success', $result['message']);
                    redirect($_SERVER['PHP_SELF']);
                } else {
                    $error = $result['message'];
                }
                break;
        }
    }
}

// Get areas list with hierarchical structure
try {
    $db = getDB();

    // Get all areas with hierarchy info
    $stmt = $db->query("
        SELECT a.*, p.name as parent_name, p.name_bn as parent_name_bn, p.type as parent_type,
               (SELECT COUNT(*) FROM voters WHERE area_id = a.id AND is_active = 1) as voter_count,
               (SELECT COUNT(*) FROM areas WHERE parent_id = a.id AND is_active = 1) as child_count
        FROM areas a
        LEFT JOIN areas p ON a.parent_id = p.id
        WHERE a.is_active = 1
        ORDER BY
            CASE a.type
                WHEN 'division' THEN 1
                WHEN 'district' THEN 2
                WHEN 'upazila' THEN 3
                WHEN 'municipality' THEN 4
                WHEN 'union' THEN 4
                WHEN 'village' THEN 5
                WHEN 'city' THEN 5
                WHEN 'ward' THEN 6
                ELSE 7
            END,
            a.name
    ");
    $areas = $stmt->fetchAll();

    // Build hierarchical structure
    $hierarchy = buildHierarchy($areas);

    // Group by type for statistics
    $grouped_areas = [];
    foreach ($areas as $area) {
        $grouped_areas[$area['type']][] = $area;
    }

    // Get statistics
    $stats = [];
    foreach (['division', 'district', 'upazila', 'municipality', 'union', 'village', 'city', 'ward'] as $type) {
        $stmt = $db->prepare("SELECT COUNT(*) as count FROM areas WHERE type = ? AND is_active = 1");
        $stmt->execute([$type]);
        $stats[$type] = $stmt->fetch()['count'];
    }

} catch (Exception $e) {
    $error = "Failed to load areas: " . $e->getMessage();
    $areas = [];
    $grouped_areas = [];
    $hierarchy = [];
    $stats = [];
}

$page_title = 'প্রশাসনিক এলাকা ম্যানেজমেন্ট - Administrative Area Management';
$page_css = ['
<style>
.hierarchy-flow {
    display: flex;
    align-items: center;
    flex-wrap: wrap;
    gap: 10px;
}

.hierarchy-item {
    padding: 8px 16px;
    border-radius: 20px;
    font-weight: 500;
    font-size: 14px;
}

.hierarchy-item.division { background: #e3f2fd; color: #1976d2; }
.hierarchy-item.district { background: #e8f5e8; color: #388e3c; }
.hierarchy-item.upazila { background: #e1f5fe; color: #0288d1; }
.hierarchy-item.union { background: #fff3e0; color: #f57c00; }
.hierarchy-item.village { background: #f3e5f5; color: #7b1fa2; }
.hierarchy-item.ward { background: #fce4ec; color: #c2185b; }

.hierarchy-tree .hierarchy-item {
    border-radius: 5px;
    background: #f8f9fa;
    transition: all 0.3s ease;
}

.hierarchy-tree .hierarchy-item:hover {
    background: #e9ecef;
    transform: translateX(5px);
}

@media (max-width: 768px) {
    .hierarchy-flow {
        flex-direction: column;
        align-items: flex-start;
    }

    .hierarchy-flow .fas.fa-arrow-right {
        transform: rotate(90deg);
    }
}
</style>
'];
include '../includes/header.php';
?>

<div class="container-fluid mt-4">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h2><i class="fas fa-sitemap"></i> প্রশাসনিক এলাকা ম্যানেজমেন্ট</h2>
                <div class="btn-group">
                    <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addAreaModal">
                        <i class="fas fa-plus"></i> নতুন এলাকা যোগ করুন
                    </button>
                    <a href="voters.php" class="btn btn-success">
                        <i class="fas fa-users"></i> ভোটার ম্যানেজমেন্ট
                    </a>
                </div>
            </div>

            <!-- Administrative Hierarchy Guide -->
            <div class="card mb-4 border-info">
                <div class="card-header bg-info text-white">
                    <h5 class="mb-0"><i class="fas fa-info-circle"></i> প্রশাসনিক কাঠামো</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-12">
                            <div class="hierarchy-flow">
                                <span class="hierarchy-item division">বিভাগ</span>
                                <i class="fas fa-arrow-right mx-2"></i>
                                <span class="hierarchy-item district">জেলা</span>
                                <i class="fas fa-arrow-right mx-2"></i>
                                <span class="hierarchy-item upazila">উপজেলা</span>
                                <i class="fas fa-arrow-right mx-2"></i>
                                <span class="hierarchy-item union">ইউনিয়ন/পৌরসভা</span>
                                <i class="fas fa-arrow-right mx-2"></i>
                                <span class="hierarchy-item village">গ্রাম/শহর</span>
                                <i class="fas fa-arrow-right mx-2"></i>
                                <span class="hierarchy-item ward">ওয়ার্ড</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Statistics Cards -->
            <div class="row mb-4">
                <div class="col-lg-3 col-md-6 mb-3">
                    <div class="card bg-primary text-white">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h4 class="mb-0"><?php echo $stats['division'] ?? 0; ?></h4>
                                    <p class="mb-0">বিভাগ</p>
                                </div>
                                <div class="align-self-center">
                                    <i class="fas fa-map fa-2x"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="col-lg-3 col-md-6 mb-3">
                    <div class="card bg-success text-white">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h4 class="mb-0"><?php echo $stats['district'] ?? 0; ?></h4>
                                    <p class="mb-0">জেলা</p>
                                </div>
                                <div class="align-self-center">
                                    <i class="fas fa-city fa-2x"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="col-lg-3 col-md-6 mb-3">
                    <div class="card bg-info text-white">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h4 class="mb-0"><?php echo $stats['upazila'] ?? 0; ?></h4>
                                    <p class="mb-0">উপজেলা</p>
                                </div>
                                <div class="align-self-center">
                                    <i class="fas fa-building fa-2x"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="col-lg-3 col-md-6 mb-3">
                    <div class="card bg-warning text-white">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h4 class="mb-0"><?php echo ($stats['union'] ?? 0) + ($stats['municipality'] ?? 0); ?></h4>
                                    <p class="mb-0">ইউনিয়ন/পৌরসভা</p>
                                </div>
                                <div class="align-self-center">
                                    <i class="fas fa-home fa-2x"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <?php if ($error): ?>
            <div class="alert alert-danger">
                <i class="fas fa-exclamation-triangle"></i> <?php echo htmlspecialchars($error); ?>
            </div>
            <?php endif; ?>
            
            <!-- Hierarchical Areas Display -->
            <?php
            $type_labels = [
                'division' => 'বিভাগ',
                'district' => 'জেলা',
                'upazila' => 'উপজেলা',
                'municipality' => 'পৌরসভা',
                'union' => 'ইউনিয়ন',
                'village' => 'গ্রাম',
                'city' => 'শহর',
                'ward' => 'ওয়ার্ড'
            ];
            $type_colors = [
                'division' => 'primary',
                'district' => 'success',
                'upazila' => 'info',
                'municipality' => 'warning',
                'union' => 'secondary',
                'village' => 'dark',
                'city' => 'danger',
                'ward' => 'light'
            ];
            $type_icons = [
                'division' => 'fas fa-map',
                'district' => 'fas fa-city',
                'upazila' => 'fas fa-building',
                'municipality' => 'fas fa-landmark',
                'union' => 'fas fa-home',
                'village' => 'fas fa-tree',
                'city' => 'fas fa-building-columns',
                'ward' => 'fas fa-map-marker-alt'
            ];
            ?>

            <!-- Hierarchical Tree View -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-sitemap"></i> প্রশাসনিক কাঠামো (ট্রি ভিউ)
                    </h5>
                </div>
                <div class="card-body">
                    <?php if (!empty($areas)): ?>
                        <div class="hierarchy-tree">
                            <?php displayHierarchyTree($areas); ?>
                        </div>
                    <?php else: ?>
                        <p class="text-muted">কোন এলাকা পাওয়া যায়নি। প্রথমে বিভাগ তৈরি করুন।</p>
                    <?php endif; ?>
                </div>
            </div>
            
            <?php foreach ($type_labels as $type => $label): ?>
                <?php if (isset($grouped_areas[$type])): ?>
                <div class="card mb-4">
                    <div class="card-header bg-<?php echo $type_colors[$type]; ?> text-white">
                        <h5 class="mb-0">
                            <i class="fas fa-map-marker-alt"></i> <?php echo $label; ?> 
                            (<?php echo count($grouped_areas[$type]); ?>টি)
                        </h5>
                    </div>
                    <div class="card-body p-0">
                        <div class="table-responsive">
                            <table class="table table-striped table-hover mb-0">
                                <thead class="table-light">
                                    <tr>
                                        <th>কোড</th>
                                        <th>নাম</th>
                                        <th>প্যারেন্ট</th>
                                        <th>ভোটার সংখ্যা</th>
                                        <th>তারিখ</th>
                                        <th>অ্যাকশন</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($grouped_areas[$type] as $area): ?>
                                    <tr>
                                        <td>
                                            <code><?php echo htmlspecialchars($area['code']); ?></code>
                                        </td>
                                        <td>
                                            <div>
                                                <strong><?php echo htmlspecialchars($area['name_bn'] ?: $area['name']); ?></strong>
                                                <?php if ($area['name_bn'] && $area['name']): ?>
                                                <br><small class="text-muted"><?php echo htmlspecialchars($area['name']); ?></small>
                                                <?php endif; ?>
                                            </div>
                                        </td>
                                        <td>
                                            <?php if ($area['parent_name']): ?>
                                                <span class="badge bg-secondary">
                                                    <?php echo htmlspecialchars($area['parent_name_bn'] ?: $area['parent_name']); ?>
                                                </span>
                                            <?php else: ?>
                                                <span class="text-muted">-</span>
                                            <?php endif; ?>
                                        </td>
                                        <td>
                                            <span class="badge bg-info">
                                                <?php echo number_format($area['voter_count']); ?> জন
                                            </span>
                                        </td>
                                        <td>
                                            <small><?php echo formatDate($area['created_at'], 'd/m/Y'); ?></small>
                                        </td>
                                        <td>
                                            <div class="btn-group btn-group-sm">
                                                <button class="btn btn-outline-primary btn-sm" 
                                                        onclick="editArea(<?php echo $area['id']; ?>)">
                                                    <i class="fas fa-edit"></i>
                                                </button>
                                                <button class="btn btn-outline-danger btn-sm" 
                                                        onclick="deleteArea(<?php echo $area['id']; ?>)"
                                                        <?php echo $area['voter_count'] > 0 ? 'disabled title="ভোটার আছে বলে মুছা যাবে না"' : ''; ?>>
                                                    <i class="fas fa-trash"></i>
                                                </button>
                                            </div>
                                        </td>
                                    </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
                <?php endif; ?>
            <?php endforeach; ?>
            
            <?php if (empty($areas)): ?>
            <div class="card">
                <div class="card-body text-center py-5">
                    <i class="fas fa-map-marker-alt fa-3x text-muted mb-3"></i>
                    <h5>কোন এলাকা/ওয়ার্ড পাওয়া যায়নি</h5>
                    <p class="text-muted">নতুন এলাকা/ওয়ার্ড যোগ করুন</p>
                    <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addAreaModal">
                        <i class="fas fa-plus"></i> প্রথম এলাকা যোগ করুন
                    </button>
                </div>
            </div>
            <?php endif; ?>
        </div>
    </div>
</div>

<!-- Add Area Modal -->
<div class="modal fade" id="addAreaModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">নতুন এলাকা/ওয়ার্ড যোগ করুন</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form method="POST" action="?action=add">
                <div class="modal-body">
                    <input type="hidden" name="csrf_token" value="<?php echo generateCSRFToken(); ?>">
                    
                    <div class="form-floating mb-3">
                        <select class="form-select" name="type" id="area_type" required>
                            <option value="">ধরন নির্বাচন করুন</option>
                            <option value="division">বিভাগ</option>
                            <option value="district">জেলা</option>
                            <option value="upazila">উপজেলা</option>
                            <option value="municipality">পৌরসভা</option>
                            <option value="union">ইউনিয়ন</option>
                            <option value="village">গ্রাম</option>
                            <option value="city">শহর</option>
                            <option value="ward">ওয়ার্ড</option>
                        </select>
                        <label>ধরন *</label>
                        <div class="form-text">
                            <small class="text-info">
                                <i class="fas fa-info-circle"></i>
                                ক্রম: বিভাগ → জেলা → উপজেলা → ইউনিয়ন/পৌরসভা → গ্রাম/শহর → ওয়ার্ড
                            </small>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-floating mb-3">
                                <input type="text" class="form-control" name="name" required>
                                <label>নাম (ইংরেজি) *</label>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-floating mb-3">
                                <input type="text" class="form-control" name="name_bn">
                                <label>নাম (বাংলা)</label>
                            </div>
                        </div>
                    </div>
                    
                    <div class="form-floating mb-3">
                        <input type="text" class="form-control" name="code" required>
                        <label>কোড *</label>
                        <div class="form-text">উদাহরণ: WARD-01, AREA-01, DIS-01</div>
                    </div>
                    
                    <div class="form-floating mb-3" id="parent_selection" style="display: none;">
                        <select class="form-select" name="parent_id" id="parent_area">
                            <option value="">প্যারেন্ট এলাকা নির্বাচন করুন</option>
                        </select>
                        <label>প্যারেন্ট এলাকা</label>
                        <div class="form-text">
                            <small id="parent_help_text" class="text-muted"></small>
                        </div>
                    </div>
                    
                    <div class="form-floating mb-3">
                        <textarea class="form-control" name="description" style="height: 80px"></textarea>
                        <label>বিবরণ</label>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">বাতিল</button>
                    <button type="submit" class="btn btn-primary">সংরক্ষণ করুন</button>
                </div>
            </form>
        </div>
    </div>
</div>

<?php
$inline_js = "
// Area hierarchy management
const areaHierarchy = {
    'division': { parent: null, children: ['district'], label: 'বিভাগ' },
    'district': { parent: 'division', children: ['upazila'], label: 'জেলা' },
    'upazila': { parent: 'district', children: ['municipality', 'union'], label: 'উপজেলা' },
    'municipality': { parent: 'upazila', children: ['ward'], label: 'পৌরসভা' },
    'union': { parent: 'upazila', children: ['village'], label: 'ইউনিয়ন' },
    'village': { parent: 'union', children: ['ward'], label: 'গ্রাম' },
    'city': { parent: 'upazila', children: ['ward'], label: 'শহর' },
    'ward': { parent: ['municipality', 'village', 'city'], children: [], label: 'ওয়ার্ড' }
};

// Safe JSON encoding
let allAreas = [];
try {
    allAreas = " . json_encode($areas ?: []) . " || [];
} catch(e) {
    console.error('Error loading areas data:', e);
    allAreas = [];
}

document.addEventListener('DOMContentLoaded', function() {
    try {
        const areaTypeSelect = document.getElementById('area_type');
        const parentSelection = document.getElementById('parent_selection');
        const parentAreaSelect = document.getElementById('parent_area');
        const parentHelpText = document.getElementById('parent_help_text');

        // Check if elements exist
        if (!areaTypeSelect || !parentSelection || !parentAreaSelect || !parentHelpText) {
            console.warn('Some form elements not found');
            return;
        }

        areaTypeSelect.addEventListener('change', function() {
            try {
                const selectedType = this.value;

                if (!selectedType) {
                    parentSelection.style.display = 'none';
                    return;
                }

                const hierarchy = areaHierarchy[selectedType];

                if (hierarchy && hierarchy.parent) {
                    parentSelection.style.display = 'block';

                    // Clear previous options
                    parentAreaSelect.innerHTML = '<option value=\"\">প্যারেন্ট এলাকা নির্বাচন করুন</option>';

                    // Get valid parent types
                    const parentTypes = Array.isArray(hierarchy.parent) ? hierarchy.parent : [hierarchy.parent];

                    // Filter areas by parent types
                    const validParents = allAreas.filter(area => parentTypes.includes(area.type));

                    // Add options
                    validParents.forEach(area => {
                        const option = document.createElement('option');
                        option.value = area.id;
                        const areaLabel = areaHierarchy[area.type] ? areaHierarchy[area.type].label : area.type;
                        option.textContent = (area.name_bn || area.name) + ' (' + areaLabel + ')';
                        parentAreaSelect.appendChild(option);
                    });

                    // Update help text
                    const parentLabels = parentTypes.map(type => areaHierarchy[type] ? areaHierarchy[type].label : type).join('/');
                    parentHelpText.textContent = 'এই ' + hierarchy.label + ' টি কোন ' + parentLabels + ' এর অধীনে?';

                    if (validParents.length === 0) {
                        parentHelpText.innerHTML = '<span class=\"text-warning\">⚠️ প্রথমে ' + parentLabels + ' তৈরি করুন</span>';
                    }
                } else {
                    parentSelection.style.display = 'none';
                    if (parentHelpText) {
                        parentHelpText.textContent = 'এটি একটি রুট লেভেল এলাকা (কোন প্যারেন্ট নেই)';
                    }
                }
            } catch(e) {
                console.error('Error in area type change handler:', e);
            }
        });
    } catch(e) {
        console.error('Error initializing area management:', e);
    }
});

function editArea(id) {
    try {
        if (typeof OVS !== 'undefined' && OVS.showAlert) {
            OVS.showAlert('Edit functionality will be implemented soon', 'info');
        } else {
            alert('Edit functionality will be implemented soon');
        }
    } catch(e) {
        console.error('Error in editArea:', e);
    }
}

function deleteArea(id) {
    try {
        if (confirm('আপনি কি এই এলাকা মুছে ফেলতে চান?')) {
            if (typeof OVS !== 'undefined' && OVS.showAlert) {
                OVS.showAlert('Delete functionality will be implemented soon', 'info');
            } else {
                alert('Delete functionality will be implemented soon');
            }
        }
    } catch(e) {
        console.error('Error in deleteArea:', e);
    }
}

// Prevent any auto-refresh
if (typeof window !== 'undefined') {
    window.addEventListener('error', function(e) {
        console.error('JavaScript error caught:', e);
        return true; // Prevent default error handling
    });
}
";

include '../includes/footer.php';
?>

<?php
// Helper functions for area management

function buildHierarchy($areas) {
    $hierarchy = [];
    $lookup = [];

    // Create lookup array
    foreach ($areas as $area) {
        $lookup[$area['id']] = $area;
        $lookup[$area['id']]['children'] = [];
    }

    // Build hierarchy
    foreach ($areas as $area) {
        if ($area['parent_id'] && isset($lookup[$area['parent_id']])) {
            $lookup[$area['parent_id']]['children'][] = &$lookup[$area['id']];
        } else {
            $hierarchy[] = &$lookup[$area['id']];
        }
    }

    return $hierarchy;
}

function displayHierarchyTree($areas, $level = 0) {
    $hierarchy = buildHierarchy($areas);
    displayTreeLevel($hierarchy, $level);
}

function displayTreeLevel($items, $level = 0) {
    $type_icons = [
        'division' => 'fas fa-map text-primary',
        'district' => 'fas fa-city text-success',
        'upazila' => 'fas fa-building text-info',
        'municipality' => 'fas fa-landmark text-warning',
        'union' => 'fas fa-home text-secondary',
        'village' => 'fas fa-tree text-success',
        'city' => 'fas fa-building-columns text-danger',
        'ward' => 'fas fa-map-marker-alt text-dark'
    ];

    $type_labels = [
        'division' => 'বিভাগ',
        'district' => 'জেলা',
        'upazila' => 'উপজেলা',
        'municipality' => 'পৌরসভা',
        'union' => 'ইউনিয়ন',
        'village' => 'গ্রাম',
        'city' => 'শহর',
        'ward' => 'ওয়ার্ড'
    ];

    foreach ($items as $item) {
        $indent = str_repeat('&nbsp;&nbsp;&nbsp;&nbsp;', $level);
        $icon = $type_icons[$item['type']] ?? 'fas fa-circle';
        $label = $type_labels[$item['type']] ?? ucfirst($item['type']);

        echo "<div class='hierarchy-item' style='margin: 5px 0; padding: 8px; border-left: 3px solid #dee2e6;'>";
        echo $indent;
        echo "<i class='$icon'></i> ";
        echo "<strong>" . htmlspecialchars($item['name_bn'] ?: $item['name']) . "</strong>";
        echo " <small class='text-muted'>($label)</small>";
        echo " <span class='badge bg-info ms-2'>" . $item['voter_count'] . " ভোটার</span>";

        if ($item['child_count'] > 0) {
            echo " <span class='badge bg-secondary ms-1'>" . $item['child_count'] . " সাব-এলাকা</span>";
        }

        echo " <small class='text-muted'>(" . htmlspecialchars($item['code']) . ")</small>";
        echo "</div>";

        if (!empty($item['children'])) {
            displayTreeLevel($item['children'], $level + 1);
        }
    }
}

function addArea($data) {
    try {
        $db = getDB();

        // Validate hierarchy
        $type = sanitizeInput($data['type']);
        $parent_id = intval($data['parent_id']) ?: null;

        if ($parent_id) {
            $stmt = $db->prepare("SELECT type FROM areas WHERE id = ?");
            $stmt->execute([$parent_id]);
            $parent = $stmt->fetch();

            if (!$parent) {
                return ['success' => false, 'message' => 'প্যারেন্ট এলাকা পাওয়া যায়নি'];
            }

            // Validate hierarchy rules
            $valid_hierarchy = [
                'district' => ['division'],
                'upazila' => ['district'],
                'municipality' => ['upazila'],
                'union' => ['upazila'],
                'village' => ['union'],
                'city' => ['upazila'],
                'ward' => ['municipality', 'village', 'city']
            ];

            if (isset($valid_hierarchy[$type]) && !in_array($parent['type'], $valid_hierarchy[$type])) {
                return ['success' => false, 'message' => 'ভুল হায়ারার্কি! ' . $type . ' শুধুমাত্র ' . implode('/', $valid_hierarchy[$type]) . ' এর অধীনে হতে পারে'];
            }
        }

        // Check if code already exists
        $code = sanitizeInput($data['code']);
        $stmt = $db->prepare("SELECT id FROM areas WHERE code = ?");
        $stmt->execute([$code]);
        if ($stmt->fetch()) {
            return ['success' => false, 'message' => 'এই কোড ইতিমধ্যে ব্যবহৃত হয়েছে'];
        }

        // Insert area
        $stmt = $db->prepare("
            INSERT INTO areas (name, name_bn, type, code, parent_id, description)
            VALUES (?, ?, ?, ?, ?, ?)
        ");

        $stmt->execute([
            sanitizeInput($data['name']),
            sanitizeInput($data['name_bn']) ?: null,
            $type,
            $code,
            $parent_id,
            sanitizeInput($data['description']) ?: null
        ]);

        logActivity('CREATE', 'area', $db->lastInsertId());

        return ['success' => true, 'message' => 'এলাকা সফলভাবে যোগ করা হয়েছে'];

    } catch (Exception $e) {
        error_log("Failed to add area: " . $e->getMessage());
        return ['success' => false, 'message' => 'এলাকা যোগ করতে ব্যর্থ: ' . $e->getMessage()];
    }
}
?>
