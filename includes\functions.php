<?php
/**
 * Common Functions
 * Dynamic Realtime Online Voting System
 */

// User Management Functions
function createUser($username, $email, $password, $firstName = null, $lastName = null) {
    try {
        $db = getDB();
        
        // Check if user already exists
        $stmt = $db->prepare("SELECT id FROM users WHERE email = ? OR username = ?");
        $stmt->execute([$email, $username]);
        if ($stmt->fetch()) {
            return ['success' => false, 'message' => 'User with this email or username already exists'];
        }
        
        // Hash password
        $hashedPassword = password_hash($password, PASSWORD_DEFAULT);
        
        // Insert user
        $stmt = $db->prepare("
            INSERT INTO users (username, email, password, first_name, last_name, email_verification_token) 
            VALUES (?, ?, ?, ?, ?, ?)
        ");
        
        $verificationToken = generateRandomString(64);
        $stmt->execute([$username, $email, $hashedPassword, $firstName, $lastName, $verificationToken]);
        
        $userId = $db->lastInsertId();
        
        logActivity('REGISTER', 'user', $userId);
        
        return [
            'success' => true, 
            'user_id' => $userId,
            'verification_token' => $verificationToken,
            'message' => 'User created successfully'
        ];
        
    } catch (Exception $e) {
        error_log("Failed to create user: " . $e->getMessage());
        return ['success' => false, 'message' => 'Failed to create user'];
    }
}

function authenticateUser($email, $password) {
    try {
        $db = getDB();
        
        $stmt = $db->prepare("
            SELECT id, username, email, password, first_name, last_name, role, is_active, email_verified 
            FROM users WHERE email = ?
        ");
        $stmt->execute([$email]);
        $user = $stmt->fetch();
        
        if (!$user) {
            return ['success' => false, 'message' => 'Invalid email or password'];
        }
        
        if (!$user['is_active']) {
            return ['success' => false, 'message' => 'Account is deactivated'];
        }
        
        if (!password_verify($password, $user['password'])) {
            return ['success' => false, 'message' => 'Invalid email or password'];
        }
        
        // Remove password from user data
        unset($user['password']);
        
        return ['success' => true, 'user' => $user];
        
    } catch (Exception $e) {
        error_log("Authentication failed: " . $e->getMessage());
        return ['success' => false, 'message' => 'Authentication failed'];
    }
}

function getUserById($userId) {
    try {
        $db = getDB();
        $stmt = $db->prepare("
            SELECT id, username, email, first_name, last_name, role, is_active, avatar, bio, website, location, created_at, last_login
            FROM users WHERE id = ?
        ");
        $stmt->execute([$userId]);
        return $stmt->fetch();
    } catch (Exception $e) {
        error_log("Failed to get user: " . $e->getMessage());
        return null;
    }
}

function updateUserProfile($userId, $data) {
    try {
        $db = getDB();
        
        $allowedFields = ['username', 'first_name', 'last_name', 'bio', 'website', 'location'];
        $updateFields = [];
        $values = [];
        
        foreach ($allowedFields as $field) {
            if (isset($data[$field])) {
                $updateFields[] = "$field = ?";
                $values[] = $data[$field];
            }
        }
        
        if (empty($updateFields)) {
            return ['success' => false, 'message' => 'No valid fields to update'];
        }
        
        $values[] = $userId;
        
        $stmt = $db->prepare("UPDATE users SET " . implode(', ', $updateFields) . " WHERE id = ?");
        $stmt->execute($values);
        
        logActivity('UPDATE', 'user', $userId, $data);
        
        return ['success' => true, 'message' => 'Profile updated successfully'];
        
    } catch (Exception $e) {
        error_log("Failed to update profile: " . $e->getMessage());
        return ['success' => false, 'message' => 'Failed to update profile'];
    }
}

// Poll Management Functions
function createPoll($userId, $title, $description, $type, $options, $settings = []) {
    try {
        $db = getDB();
        $db->beginTransaction();

        // Default settings
        $defaultSettings = [
            'allowAnonymous' => true,
            'requireAuth' => false,
            'allowMultipleVotes' => false,
            'showResults' => 'after_vote',
            'ipRestriction' => false,
            'access_type' => 'public',
            'allowed_areas' => [],
            'require_voter_verification' => false,
            'customization' => [
                'theme' => 'default',
                'backgroundColor' => '#ffffff',
                'textColor' => '#333333',
                'accentColor' => '#0d6efd'
            ]
        ];

        $pollSettings = array_merge($defaultSettings, $settings);

        // Insert poll
        $stmt = $db->prepare("
            INSERT INTO polls (title, description, type, access_type, allowed_areas, require_voter_verification, settings, created_by, start_date, end_date)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        ");

        $stmt->execute([
            $title,
            $description,
            $type,
            $pollSettings['access_type'],
            !empty($pollSettings['allowed_areas']) ? json_encode($pollSettings['allowed_areas']) : null,
            $pollSettings['require_voter_verification'] ? 1 : 0,
            json_encode($pollSettings),
            $userId,
            $settings['start_date'] ?? null,
            $settings['end_date'] ?? null
        ]);
        
        $pollId = $db->lastInsertId();
        
        // Insert poll options
        $stmt = $db->prepare("INSERT INTO poll_options (poll_id, option_text, option_order) VALUES (?, ?, ?)");
        
        foreach ($options as $index => $option) {
            $stmt->execute([$pollId, $option, $index + 1]);
        }
        
        // Create analytics record
        $stmt = $db->prepare("INSERT INTO poll_analytics (poll_id) VALUES (?)");
        $stmt->execute([$pollId]);
        
        $db->commit();
        
        logActivity('CREATE', 'poll', $pollId);
        
        return ['success' => true, 'poll_id' => $pollId, 'message' => 'Poll created successfully'];
        
    } catch (Exception $e) {
        $db->rollBack();
        error_log("Failed to create poll: " . $e->getMessage());
        return ['success' => false, 'message' => 'Failed to create poll'];
    }
}

function getPollById($pollId, $includeOptions = true) {
    try {
        $db = getDB();
        
        $stmt = $db->prepare("
            SELECT p.*, u.username as creator_name,
                   (SELECT COUNT(*) FROM votes WHERE poll_id = p.id) as vote_count
            FROM polls p 
            JOIN users u ON p.created_by = u.id 
            WHERE p.id = ?
        ");
        $stmt->execute([$pollId]);
        $poll = $stmt->fetch();
        
        if (!$poll) {
            return null;
        }
        
        // Decode settings
        $poll['settings'] = json_decode($poll['settings'], true);
        
        if ($includeOptions) {
            $stmt = $db->prepare("
                SELECT id, option_text, option_order 
                FROM poll_options 
                WHERE poll_id = ? 
                ORDER BY option_order
            ");
            $stmt->execute([$pollId]);
            $poll['options'] = $stmt->fetchAll();
        }
        
        return $poll;
        
    } catch (Exception $e) {
        error_log("Failed to get poll: " . $e->getMessage());
        return null;
    }
}

function getUserPolls($userId, $page = 1, $limit = 10) {
    try {
        $db = getDB();
        $offset = ($page - 1) * $limit;
        
        $stmt = $db->prepare("
            SELECT p.*, 
                   (SELECT COUNT(*) FROM votes WHERE poll_id = p.id) as vote_count
            FROM polls p 
            WHERE p.created_by = ? 
            ORDER BY p.created_at DESC 
            LIMIT ? OFFSET ?
        ");
        $stmt->execute([$userId, $limit, $offset]);
        $polls = $stmt->fetchAll();
        
        // Get total count
        $stmt = $db->prepare("SELECT COUNT(*) as total FROM polls WHERE created_by = ?");
        $stmt->execute([$userId]);
        $total = $stmt->fetch()['total'];
        
        return [
            'polls' => $polls,
            'total' => $total,
            'page' => $page,
            'limit' => $limit,
            'total_pages' => ceil($total / $limit)
        ];
        
    } catch (Exception $e) {
        error_log("Failed to get user polls: " . $e->getMessage());
        return ['polls' => [], 'total' => 0, 'page' => 1, 'limit' => $limit, 'total_pages' => 0];
    }
}

// Voting Functions
function castVote($pollId, $userId, $sessionId, $voteData) {
    try {
        $db = getDB();
        
        // Get poll settings
        $poll = getPollById($pollId, false);
        if (!$poll) {
            return ['success' => false, 'message' => 'Poll not found'];
        }
        
        if (!$poll['is_active']) {
            return ['success' => false, 'message' => 'Poll is not active'];
        }
        
        // Check if poll has ended
        if ($poll['end_date'] && strtotime($poll['end_date']) < time()) {
            return ['success' => false, 'message' => 'Poll has ended'];
        }
        
        $settings = json_decode($poll['settings'], true);
        
        // Check if user already voted
        if (!$settings['allowMultipleVotes']) {
            $stmt = $db->prepare("
                SELECT id FROM votes 
                WHERE poll_id = ? AND (user_id = ? OR session_id = ?)
            ");
            $stmt->execute([$pollId, $userId, $sessionId]);
            if ($stmt->fetch()) {
                return ['success' => false, 'message' => 'You have already voted in this poll'];
            }
        }
        
        // Insert vote
        $stmt = $db->prepare("
            INSERT INTO votes (poll_id, user_id, session_id, ip_address, selected_options, text_response, rating, ranking, user_agent) 
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
        ");
        
        $stmt->execute([
            $pollId,
            $userId,
            $sessionId,
            $_SERVER['REMOTE_ADDR'] ?? null,
            isset($voteData['selected_options']) ? json_encode($voteData['selected_options']) : null,
            $voteData['text_response'] ?? null,
            $voteData['rating'] ?? null,
            isset($voteData['ranking']) ? json_encode($voteData['ranking']) : null,
            $_SERVER['HTTP_USER_AGENT'] ?? null
        ]);
        
        $voteId = $db->lastInsertId();
        
        // Update analytics
        updatePollAnalytics($pollId);
        
        logActivity('VOTE', 'poll', $pollId, ['vote_id' => $voteId]);
        
        return ['success' => true, 'vote_id' => $voteId, 'message' => 'Vote cast successfully'];
        
    } catch (Exception $e) {
        error_log("Failed to cast vote: " . $e->getMessage());
        return ['success' => false, 'message' => 'Failed to cast vote'];
    }
}

function getPollResults($pollId) {
    try {
        $db = getDB();

        // Get poll type first
        $stmt = $db->prepare("SELECT type FROM polls WHERE id = ?");
        $stmt->execute([$pollId]);
        $poll = $stmt->fetch();

        if (!$poll) {
            return null;
        }

        $results = [
            'poll_id' => $pollId,
            'updated_at' => date('Y-m-d H:i:s')
        ];

        if ($poll['type'] === 'rating_scale') {
            // Get rating results
            $stmt = $db->prepare("
                SELECT rating, COUNT(*) as count
                FROM votes
                WHERE poll_id = ? AND rating IS NOT NULL
                GROUP BY rating
                ORDER BY rating
            ");
            $stmt->execute([$pollId]);
            $ratings = $stmt->fetchAll();

            // Calculate average
            $stmt = $db->prepare("
                SELECT COUNT(*) as total, AVG(rating) as average
                FROM votes
                WHERE poll_id = ? AND rating IS NOT NULL
            ");
            $stmt->execute([$pollId]);
            $ratingStats = $stmt->fetch();

            $results['total_votes'] = $ratingStats['total'];
            $results['average_rating'] = $ratingStats['average'];
            $results['rating_breakdown'] = $ratings;

        } elseif ($poll['type'] === 'text_response') {
            // Get text responses count
            $stmt = $db->prepare("SELECT COUNT(*) as total FROM votes WHERE poll_id = ? AND text_response IS NOT NULL");
            $stmt->execute([$pollId]);
            $results['total_votes'] = $stmt->fetch()['total'];

        } else {
            // Get poll options with vote counts
            $stmt = $db->prepare("
                SELECT po.id, po.option_text, po.option_order,
                       COUNT(v.id) as vote_count
                FROM poll_options po
                LEFT JOIN votes v ON po.poll_id = v.poll_id
                    AND JSON_CONTAINS(v.selected_options, CAST(po.id AS JSON))
                WHERE po.poll_id = ?
                GROUP BY po.id, po.option_text, po.option_order
                ORDER BY po.option_order
            ");
            $stmt->execute([$pollId]);
            $options = $stmt->fetchAll();

            // Get total votes
            $stmt = $db->prepare("SELECT COUNT(*) as total FROM votes WHERE poll_id = ?");
            $stmt->execute([$pollId]);
            $totalVotes = $stmt->fetch()['total'];

            // Calculate percentages
            foreach ($options as &$option) {
                $option['percentage'] = $totalVotes > 0 ? round(($option['vote_count'] / $totalVotes) * 100, 1) : 0;
            }

            $results['total_votes'] = $totalVotes;
            $results['options'] = $options;
        }

        return $results;

    } catch (Exception $e) {
        error_log("Failed to get poll results: " . $e->getMessage());
        return null;
    }
}

function updatePollAnalytics($pollId) {
    try {
        $db = getDB();
        
        // Update unique voters count
        $stmt = $db->prepare("
            UPDATE poll_analytics 
            SET unique_voters = (
                SELECT COUNT(DISTINCT COALESCE(user_id, session_id)) 
                FROM votes WHERE poll_id = ?
            )
            WHERE poll_id = ?
        ");
        $stmt->execute([$pollId, $pollId]);
        
    } catch (Exception $e) {
        error_log("Failed to update analytics: " . $e->getMessage());
    }
}

// Validation Functions
function validateEmail($email) {
    return filter_var($email, FILTER_VALIDATE_EMAIL) !== false;
}

function validatePassword($password) {
    return strlen($password) >= PASSWORD_MIN_LENGTH;
}

function validateUsername($username) {
    return preg_match('/^[a-zA-Z0-9_]{3,30}$/', $username);
}

// File Upload Functions
function uploadFile($file, $allowedTypes = null, $maxSize = null) {
    if (!$allowedTypes) {
        $allowedTypes = UPLOAD_ALLOWED_TYPES;
    }
    
    if (!$maxSize) {
        $maxSize = UPLOAD_MAX_SIZE;
    }
    
    if ($file['error'] !== UPLOAD_ERR_OK) {
        return ['success' => false, 'message' => 'File upload error'];
    }
    
    if ($file['size'] > $maxSize) {
        return ['success' => false, 'message' => 'File too large'];
    }
    
    $extension = strtolower(pathinfo($file['name'], PATHINFO_EXTENSION));
    if (!in_array($extension, $allowedTypes)) {
        return ['success' => false, 'message' => 'File type not allowed'];
    }
    
    $filename = uniqid() . '.' . $extension;
    $filepath = UPLOAD_PATH . $filename;
    
    if (!move_uploaded_file($file['tmp_name'], $filepath)) {
        return ['success' => false, 'message' => 'Failed to save file'];
    }
    
    return ['success' => true, 'filename' => $filename, 'filepath' => $filepath];
}
?>
