<?php
/**
 * Fixed Sidebar Layout Test
 * সঠিক সাইডবার লেআউট টেস্ট
 */

require_once 'config/config.php';

$page_title = '✅ সঠিক সাইডবার লেআউট - Fixed Sidebar Layout';
$show_top_nav = true;

// Start output buffering for content
ob_start();
?>

<div class="container-fluid py-4">
    <!-- Success Alert -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="alert alert-success border-0" style="background: linear-gradient(135deg, #4ecdc4, #44a08d); color: white;">
                <h4 class="alert-heading">
                    <i class="fas fa-check-circle"></i> সাইডবার সফলভাবে পাশাপাশি এসেছে!
                </h4>
                <p class="mb-0">
                    এখন সাইডবার বাম পাশে fixed position এ আছে এবং মেইন কন্টেন্ট ডান পাশে proper margin সহ দেখাচ্ছে।
                </p>
            </div>
        </div>
    </div>

    <!-- Layout Information -->
    <div class="row mb-4">
        <div class="col-md-6">
            <div class="card border-0" style="background: linear-gradient(135deg, #667eea, #764ba2); color: white;">
                <div class="card-header border-0" style="background: rgba(255, 255, 255, 0.1);">
                    <h5 class="mb-0">
                        <i class="fas fa-desktop"></i> ডেস্কটপ লেআউট
                    </h5>
                </div>
                <div class="card-body">
                    <ul class="list-unstyled mb-0">
                        <li class="mb-2">
                            <i class="fas fa-check text-warning"></i>
                            সাইডবার: Fixed position, 300px width
                        </li>
                        <li class="mb-2">
                            <i class="fas fa-check text-warning"></i>
                            মেইন কন্টেন্ট: 320px left margin
                        </li>
                        <li class="mb-2">
                            <i class="fas fa-check text-warning"></i>
                            Responsive: 992px+ breakpoint
                        </li>
                        <li class="mb-0">
                            <i class="fas fa-check text-warning"></i>
                            Colorful gradient backgrounds
                        </li>
                    </ul>
                </div>
            </div>
        </div>
        
        <div class="col-md-6">
            <div class="card border-0" style="background: linear-gradient(135deg, #ff6b6b, #feca57); color: white;">
                <div class="card-header border-0" style="background: rgba(255, 255, 255, 0.1);">
                    <h5 class="mb-0">
                        <i class="fas fa-mobile-alt"></i> মোবাইল লেআউট
                    </h5>
                </div>
                <div class="card-body">
                    <ul class="list-unstyled mb-0">
                        <li class="mb-2">
                            <i class="fas fa-check text-light"></i>
                            সাইডবার: Overlay mode
                        </li>
                        <li class="mb-2">
                            <i class="fas fa-check text-light"></i>
                            মেইন কন্টেন্ট: Full width
                        </li>
                        <li class="mb-2">
                            <i class="fas fa-check text-light"></i>
                            Toggle button: Top-left corner
                        </li>
                        <li class="mb-0">
                            <i class="fas fa-check text-light"></i>
                            Auto-close on link click
                        </li>
                    </ul>
                </div>
            </div>
        </div>
    </div>

    <!-- Current Status -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card border-0" style="background: rgba(255, 255, 255, 0.9); backdrop-filter: blur(10px);">
                <div class="card-header border-0" style="background: linear-gradient(135deg, #48dbfb, #0abde3); color: white;">
                    <h5 class="mb-0">
                        <i class="fas fa-info-circle"></i> বর্তমান স্ট্যাটাস
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-3">
                            <div class="text-center p-3" style="background: linear-gradient(135deg, #ff6b6b, #feca57); color: white; border-radius: 15px;">
                                <h4 id="screenWidth">-</h4>
                                <small>Screen Width</small>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="text-center p-3" style="background: linear-gradient(135deg, #48dbfb, #0abde3); color: white; border-radius: 15px;">
                                <h4 id="layoutMode">-</h4>
                                <small>Layout Mode</small>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="text-center p-3" style="background: linear-gradient(135deg, #667eea, #764ba2); color: white; border-radius: 15px;">
                                <h4 id="sidebarWidth">-</h4>
                                <small>Sidebar Width</small>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="text-center p-3" style="background: linear-gradient(135deg, #ff9ff3, #f368e0); color: white; border-radius: 15px;">
                                <h4 id="contentMargin">-</h4>
                                <small>Content Margin</small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Test Content -->
    <div class="row mb-4">
        <div class="col-md-8">
            <div class="card border-0" style="background: rgba(255, 255, 255, 0.9); backdrop-filter: blur(10px);">
                <div class="card-header border-0" style="background: linear-gradient(135deg, #4ecdc4, #44a08d); color: white;">
                    <h5 class="mb-0">
                        <i class="fas fa-file-alt"></i> টেস্ট কন্টেন্ট
                    </h5>
                </div>
                <div class="card-body">
                    <p>
                        এই কন্টেন্টটি প্রমাণ করে যে সাইডবার এবং মেইন কন্টেন্ট এখন সঠিকভাবে পাশাপাশি আছে। 
                        সাইডবার বাম পাশে fixed position এ থাকবে এবং এই কন্টেন্ট ডান পাশে proper spacing সহ দেখাবে।
                    </p>
                    
                    <h6 style="color: #667eea;">✅ সমাধান করা সমস্যাসমূহ:</h6>
                    <ul>
                        <li>সাইডবার এখন <code>position: fixed</code> এ আছে</li>
                        <li>মেইন কন্টেন্ট এর <code>margin-left: 320px</code> আছে</li>
                        <li>Width calculation সঠিক: <code>calc(100% - 320px)</code></li>
                        <li>Mobile responsive: 991px breakpoint</li>
                        <li>Colorful gradient design maintained</li>
                    </ul>
                    
                    <div class="alert alert-info mt-3" style="background: linear-gradient(135deg, #48dbfb, #0abde3); color: white; border: none;">
                        <strong>পরীক্ষা:</strong> ব্রাউজারের width পরিবর্তন করে responsive behavior দেখুন।
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-md-4">
            <div class="card border-0" style="background: rgba(255, 255, 255, 0.9); backdrop-filter: blur(10px);">
                <div class="card-header border-0" style="background: linear-gradient(135deg, #ff9ff3, #f368e0); color: white;">
                    <h5 class="mb-0">
                        <i class="fas fa-tools"></i> লেআউট টেস্ট
                    </h5>
                </div>
                <div class="card-body">
                    <div class="d-grid gap-2">
                        <button class="btn" style="background: linear-gradient(135deg, #ff6b6b, #feca57); color: white; border: none;" onclick="updateLayoutInfo()">
                            <i class="fas fa-sync"></i> Update Info
                        </button>
                        <button class="btn" style="background: linear-gradient(135deg, #48dbfb, #0abde3); color: white; border: none;" onclick="testSidebarPosition()">
                            <i class="fas fa-ruler"></i> Test Position
                        </button>
                        <button class="btn" style="background: linear-gradient(135deg, #667eea, #764ba2); color: white; border: none;" onclick="testResponsive()">
                            <i class="fas fa-mobile-alt"></i> Test Responsive
                        </button>
                        <button class="btn" style="background: linear-gradient(135deg, #4ecdc4, #44a08d); color: white; border: none;" onclick="highlightLayout()">
                            <i class="fas fa-highlight"></i> Highlight Layout
                        </button>
                    </div>
                </div>
            </div>
            
            <!-- CSS Info -->
            <div class="card border-0 mt-3" style="background: rgba(255, 255, 255, 0.9); backdrop-filter: blur(10px);">
                <div class="card-header border-0" style="background: linear-gradient(135deg, #ffd700, #ffed4e); color: #333;">
                    <h6 class="mb-0">
                        <i class="fas fa-code"></i> CSS Values
                    </h6>
                </div>
                <div class="card-body">
                    <small>
                        <strong>Sidebar:</strong><br>
                        • Position: Fixed<br>
                        • Width: 300px<br>
                        • Left: 0<br><br>
                        
                        <strong>Main Content:</strong><br>
                        • Margin-left: 320px<br>
                        • Width: calc(100% - 320px)<br>
                        • Position: Relative
                    </small>
                </div>
            </div>
        </div>
    </div>

    <!-- Success Message -->
    <div class="row">
        <div class="col-12">
            <div class="card border-0" style="background: linear-gradient(135deg, #4ecdc4, #44a08d); color: white;">
                <div class="card-body text-center py-4">
                    <h3 class="mb-3">
                        🎉 সাইডবার লেআউট সফলভাবে ঠিক হয়েছে!
                    </h3>
                    <p class="mb-0">
                        এখন সাইডবার এবং কন্টেন্ট perfect side-by-side layout এ আছে।
                    </p>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
function updateLayoutInfo() {
    const width = $(window).width();
    const sidebar = $('.modern-sidebar');
    const mainContent = $('.main-content');
    
    $('#screenWidth').text(width + 'px');
    
    if (width > 991) {
        $('#layoutMode').text('Desktop');
        $('#sidebarWidth').text('300px');
        $('#contentMargin').text(mainContent.css('margin-left'));
    } else {
        $('#layoutMode').text('Mobile');
        $('#sidebarWidth').text('100%');
        $('#contentMargin').text('0px');
    }
}

function testSidebarPosition() {
    const sidebar = $('.modern-sidebar');
    const position = sidebar.css('position');
    const left = sidebar.css('left');
    const width = sidebar.css('width');
    
    alert(`Sidebar Position Info:\n\n` +
          `Position: ${position}\n` +
          `Left: ${left}\n` +
          `Width: ${width}\n` +
          `Z-index: ${sidebar.css('z-index')}`);
}

function testResponsive() {
    const width = $(window).width();
    let message = `Current Width: ${width}px\n\n`;
    
    if (width > 991) {
        message += '✅ Desktop Mode:\n' +
                  '• Sidebar: Fixed position\n' +
                  '• Content: 320px margin\n' +
                  '• Layout: Side-by-side';
    } else {
        message += '📱 Mobile Mode:\n' +
                  '• Sidebar: Overlay\n' +
                  '• Content: Full width\n' +
                  '• Layout: Stacked';
    }
    
    alert(message);
}

function highlightLayout() {
    // Temporarily highlight sidebar and content
    const sidebar = $('.modern-sidebar');
    const mainContent = $('.main-content');
    
    sidebar.css('box-shadow', '0 0 20px #ff6b6b');
    mainContent.css('box-shadow', '0 0 20px #48dbfb');
    
    setTimeout(() => {
        sidebar.css('box-shadow', '10px 0 30px rgba(0, 0, 0, 0.3)');
        mainContent.css('box-shadow', '');
    }, 2000);
    
    APP.showAlert('Layout highlighted! 🎯', 'success', 2000);
}

// Initialize
$(document).ready(function() {
    updateLayoutInfo();
    $(window).on('resize', updateLayoutInfo);
});
</script>

<?php
$content = ob_get_clean();

// Include the sidebar layout
include 'includes/sidebar-layout.php';
?>
