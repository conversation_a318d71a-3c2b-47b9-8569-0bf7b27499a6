<?php
/**
 * Registration Page
 * Dynamic Realtime Online Voting System
 */

require_once '../config/config.php';

// Redirect if already logged in
if (isLoggedIn()) {
    redirect(APP_URL);
}

$error = '';
$success = '';
$formData = [
    'username' => '',
    'email' => '',
    'first_name' => '',
    'last_name' => ''
];

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    // Validate CSRF token
    if (!validateCSRFToken($_POST['csrf_token'] ?? '')) {
        $error = 'Invalid request. Please try again.';
    } else {
        // Sanitize input
        $formData['username'] = sanitizeInput($_POST['username'] ?? '');
        $formData['email'] = sanitizeInput($_POST['email'] ?? '');
        $formData['first_name'] = sanitizeInput($_POST['first_name'] ?? '');
        $formData['last_name'] = sanitizeInput($_POST['last_name'] ?? '');
        $password = $_POST['password'] ?? '';
        $confirmPassword = $_POST['confirm_password'] ?? '';
        
        // Validate input
        if (empty($formData['username']) || empty($formData['email']) || empty($password)) {
            $error = 'Please fill in all required fields.';
        } elseif (!validateUsername($formData['username'])) {
            $error = 'Username must be 3-30 characters and contain only letters, numbers, and underscores.';
        } elseif (!isValidEmail($formData['email'])) {
            $error = 'Please enter a valid email address.';
        } elseif (!validatePassword($password)) {
            $error = 'Password must be at least ' . PASSWORD_MIN_LENGTH . ' characters long.';
        } elseif ($password !== $confirmPassword) {
            $error = 'Passwords do not match.';
        } else {
            // Attempt to create user
            $result = createUser(
                $formData['username'],
                $formData['email'],
                $password,
                $formData['first_name'] ?: null,
                $formData['last_name'] ?: null
            );
            
            if ($result['success']) {
                $success = 'Account created successfully! You can now log in.';
                // Clear form data on success
                $formData = array_fill_keys(array_keys($formData), '');
            } else {
                $error = $result['message'];
            }
        }
    }
}

$page_title = 'Create Account';
include '../includes/header.php';
?>

<div class="container mt-5">
    <div class="row justify-content-center">
        <div class="col-md-8 col-lg-6">
            <div class="card shadow">
                <div class="card-body p-4">
                    <div class="text-center mb-4">
                        <h2 class="card-title">Create Account</h2>
                        <p class="text-muted">Join our voting community</p>
                    </div>
                    
                    <?php if ($error): ?>
                    <div class="alert alert-danger" role="alert">
                        <i class="fas fa-exclamation-triangle"></i> <?php echo htmlspecialchars($error); ?>
                    </div>
                    <?php endif; ?>
                    
                    <?php if ($success): ?>
                    <div class="alert alert-success" role="alert">
                        <i class="fas fa-check-circle"></i> <?php echo htmlspecialchars($success); ?>
                        <div class="mt-2">
                            <a href="login.php" class="btn btn-success btn-sm">
                                <i class="fas fa-sign-in-alt"></i> Login Now
                            </a>
                        </div>
                    </div>
                    <?php endif; ?>
                    
                    <form method="POST" action="" id="registerForm">
                        <input type="hidden" name="csrf_token" value="<?php echo generateCSRFToken(); ?>">
                        
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-floating mb-3">
                                    <input type="text" class="form-control" id="first_name" name="first_name" 
                                           value="<?php echo htmlspecialchars($formData['first_name']); ?>">
                                    <label for="first_name">First Name</label>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-floating mb-3">
                                    <input type="text" class="form-control" id="last_name" name="last_name" 
                                           value="<?php echo htmlspecialchars($formData['last_name']); ?>">
                                    <label for="last_name">Last Name</label>
                                </div>
                            </div>
                        </div>
                        
                        <div class="form-floating mb-3">
                            <input type="text" class="form-control" id="username" name="username" 
                                   value="<?php echo htmlspecialchars($formData['username']); ?>" required>
                            <label for="username">Username *</label>
                            <div class="form-text">3-30 characters, letters, numbers, and underscores only</div>
                        </div>
                        
                        <div class="form-floating mb-3">
                            <input type="email" class="form-control" id="email" name="email" 
                                   value="<?php echo htmlspecialchars($formData['email']); ?>" required>
                            <label for="email">Email Address *</label>
                        </div>
                        
                        <div class="form-floating mb-3">
                            <input type="password" class="form-control" id="password" name="password" required>
                            <label for="password">Password *</label>
                            <div class="form-text">Minimum <?php echo PASSWORD_MIN_LENGTH; ?> characters</div>
                        </div>
                        
                        <div class="form-floating mb-3">
                            <input type="password" class="form-control" id="confirm_password" name="confirm_password" required>
                            <label for="confirm_password">Confirm Password *</label>
                        </div>
                        
                        <div class="form-check mb-3">
                            <input class="form-check-input" type="checkbox" id="terms" name="terms" required>
                            <label class="form-check-label" for="terms">
                                I agree to the <a href="#" target="_blank">Terms of Service</a> and 
                                <a href="#" target="_blank">Privacy Policy</a> *
                            </label>
                        </div>
                        
                        <button type="submit" class="btn btn-primary w-100 mb-3">
                            <i class="fas fa-user-plus"></i> Create Account
                        </button>
                    </form>
                    
                    <hr class="my-4">
                    
                    <div class="text-center">
                        <p class="mb-0">Already have an account?</p>
                        <a href="login.php" class="btn btn-outline-primary w-100 mt-2">
                            <i class="fas fa-sign-in-alt"></i> Sign In
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<?php 
$inline_js = "
// Form validation
document.getElementById('registerForm').addEventListener('submit', function(e) {
    const password = document.getElementById('password').value;
    const confirmPassword = document.getElementById('confirm_password').value;
    
    if (password !== confirmPassword) {
        e.preventDefault();
        OVS.showAlert('Passwords do not match.', 'danger');
        return false;
    }
    
    if (password.length < " . PASSWORD_MIN_LENGTH . ") {
        e.preventDefault();
        OVS.showAlert('Password must be at least " . PASSWORD_MIN_LENGTH . " characters long.', 'danger');
        return false;
    }
});

// Real-time password confirmation
document.getElementById('confirm_password').addEventListener('input', function() {
    const password = document.getElementById('password').value;
    const confirmPassword = this.value;
    
    if (confirmPassword && password !== confirmPassword) {
        this.setCustomValidity('Passwords do not match');
        this.classList.add('is-invalid');
    } else {
        this.setCustomValidity('');
        this.classList.remove('is-invalid');
    }
});

// Username validation
document.getElementById('username').addEventListener('input', function() {
    const username = this.value;
    const pattern = /^[a-zA-Z0-9_]{3,30}$/;
    
    if (username && !pattern.test(username)) {
        this.setCustomValidity('Username must be 3-30 characters and contain only letters, numbers, and underscores');
        this.classList.add('is-invalid');
    } else {
        this.setCustomValidity('');
        this.classList.remove('is-invalid');
    }
});

// Auto-focus first name field
document.getElementById('first_name').focus();
";

include '../includes/footer.php'; 
?>
