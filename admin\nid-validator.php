<?php
/**
 * NID Validator Tool
 * Test and validate NID formats
 */

require_once '../config/config.php';

// Check if user is admin
if (!isLoggedIn() || !isAdmin()) {
    redirect(APP_URL . '/auth/login.php');
}

echo "<h2>🔍 NID Validator Tool</h2>";

if (isset($_POST['test_nid'])) {
    $test_nid = $_POST['nid_value'];
    
    echo "<h3>📋 NID Validation Results:</h3>";
    echo "<div style='background: #f8f9fa; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
    
    echo "<p><strong>Original NID:</strong> '$test_nid'</p>";
    
    // Clean NID
    $clean_nid = preg_replace('/[^0-9]/', '', $test_nid);
    echo "<p><strong>Cleaned NID:</strong> '$clean_nid'</p>";
    echo "<p><strong>Length:</strong> " . strlen($clean_nid) . "</p>";
    
    // Old validation (strict)
    $old_valid = preg_match('/^\d{10,17}$/', $test_nid);
    echo "<p><strong>Old Validation (Strict):</strong> " . ($old_valid ? '✅ Valid' : '❌ Invalid') . "</p>";
    
    // New validation (flexible)
    $new_valid = (strlen($clean_nid) >= 10 && strlen($clean_nid) <= 17);
    echo "<p><strong>New Validation (Flexible):</strong> " . ($new_valid ? '✅ Valid' : '❌ Invalid') . "</p>";
    
    // Character analysis
    echo "<h4>Character Analysis:</h4>";
    echo "<ul>";
    for ($i = 0; $i < strlen($test_nid); $i++) {
        $char = $test_nid[$i];
        $ascii = ord($char);
        echo "<li>Position $i: '$char' (ASCII: $ascii)</li>";
    }
    echo "</ul>";
    
    echo "</div>";
}

// Test common NID formats
$test_nids = [
    '1234567890123',     // 13 digits
    '12345678901234',    // 14 digits  
    '123456789012345',   // 15 digits
    '1234567890',        // 10 digits
    '12345678901234567', // 17 digits
    '123456789',         // 9 digits (invalid)
    '123456789012345678', // 18 digits (invalid)
    '১২৩৪৫৬৭৮৯০১২ৃ',      // Bengali digits
    '1234-5678-9012',    // With dashes
    '1234 5678 9012',    // With spaces
];

echo "<h3>🧪 Common NID Format Tests:</h3>";
echo "<div style='background: #e7f3ff; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
echo "<tr style='background: #f8f9fa;'>";
echo "<th style='padding: 10px;'>NID</th>";
echo "<th style='padding: 10px;'>Length</th>";
echo "<th style='padding: 10px;'>Cleaned</th>";
echo "<th style='padding: 10px;'>Cleaned Length</th>";
echo "<th style='padding: 10px;'>Old Valid</th>";
echo "<th style='padding: 10px;'>New Valid</th>";
echo "</tr>";

foreach ($test_nids as $nid) {
    $clean_nid = preg_replace('/[^0-9]/', '', $nid);
    $old_valid = preg_match('/^\d{10,17}$/', $nid);
    $new_valid = (strlen($clean_nid) >= 10 && strlen($clean_nid) <= 17);
    
    echo "<tr>";
    echo "<td style='padding: 5px;'>" . htmlspecialchars($nid) . "</td>";
    echo "<td style='padding: 5px;'>" . strlen($nid) . "</td>";
    echo "<td style='padding: 5px;'>" . htmlspecialchars($clean_nid) . "</td>";
    echo "<td style='padding: 5px;'>" . strlen($clean_nid) . "</td>";
    echo "<td style='padding: 5px; color: " . ($old_valid ? 'green' : 'red') . ";'>" . ($old_valid ? '✅' : '❌') . "</td>";
    echo "<td style='padding: 5px; color: " . ($new_valid ? 'green' : 'red') . ";'>" . ($new_valid ? '✅' : '❌') . "</td>";
    echo "</tr>";
}

echo "</table>";
echo "</div>";

// Check current template NID
try {
    echo "<h3>📄 Current Template NID Check:</h3>";
    
    // Read current template
    $template_url = 'download-template.php?format=csv';
    $template_content = file_get_contents('http://' . $_SERVER['HTTP_HOST'] . dirname($_SERVER['REQUEST_URI']) . '/' . $template_url);
    
    if ($template_content) {
        $lines = explode("\n", $template_content);
        if (count($lines) > 1) {
            $headers = str_getcsv($lines[0]);
            $nid_index = array_search('nid', $headers);
            
            echo "<div style='background: #fff3cd; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
            echo "<h4>Template NID Analysis:</h4>";
            
            for ($i = 1; $i < min(6, count($lines)); $i++) {
                if (trim($lines[$i])) {
                    $row = str_getcsv($lines[$i]);
                    if (isset($row[$nid_index])) {
                        $nid = $row[$nid_index];
                        $clean_nid = preg_replace('/[^0-9]/', '', $nid);
                        $valid = (strlen($clean_nid) >= 10 && strlen($clean_nid) <= 17);
                        
                        echo "<p><strong>Row $i NID:</strong> '$nid' → '$clean_nid' (Length: " . strlen($clean_nid) . ") " . ($valid ? '✅' : '❌') . "</p>";
                    }
                }
            }
            echo "</div>";
        }
    }
    
} catch (Exception $e) {
    echo "<p>Could not analyze template: " . $e->getMessage() . "</p>";
}
?>

<!DOCTYPE html>
<html>
<head>
    <title>NID Validator</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
</head>
<body>

<div class="container mt-4">
    <div class="row">
        <div class="col-md-6">
            <div class="card">
                <div class="card-header">
                    <h5>🧪 Test Your NID</h5>
                </div>
                <div class="card-body">
                    <form method="POST">
                        <div class="mb-3">
                            <label for="nid_value" class="form-label">Enter NID to Test</label>
                            <input type="text" class="form-control" name="nid_value" placeholder="Enter NID number" required>
                        </div>
                        <button type="submit" name="test_nid" class="btn btn-primary">
                            <i class="fas fa-check"></i> Validate NID
                        </button>
                    </form>
                </div>
            </div>
        </div>
        
        <div class="col-md-6">
            <div class="card">
                <div class="card-header">
                    <h5>🔧 Quick Actions</h5>
                </div>
                <div class="card-body">
                    <a href="test-template.php" class="btn btn-success me-2">
                        <i class="fas fa-download"></i> Test Template
                    </a>
                    <a href="debug-upload.php" class="btn btn-warning me-2">
                        <i class="fas fa-bug"></i> Debug Upload
                    </a>
                    <a href="voters.php" class="btn btn-secondary">
                        <i class="fas fa-users"></i> Back to Voters
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>

</body>
</html>
