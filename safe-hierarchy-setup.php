<?php
/**
 * Safe Administrative Hierarchy Setup
 * Preserves existing voters and updates areas safely
 */

require_once 'config/config.php';

// Check if user is admin
if (!isLoggedIn() || !isAdmin()) {
    echo "<h2>Access Denied</h2>";
    echo "<p>Please login as admin first: <a href='auth/login.php'>Login</a></p>";
    exit;
}

echo "<h2>🛡️ নিরাপদ প্রশাসনিক হায়ারার্কি সেটআপ</h2>";

try {
    $db = getDB();
    
    // Check existing data
    $stmt = $db->query("SELECT COUNT(*) as voter_count FROM voters WHERE is_active = 1");
    $voter_count = $stmt->fetch()['voter_count'];
    
    $stmt = $db->query("SELECT COUNT(*) as area_count FROM areas WHERE is_active = 1");
    $area_count = $stmt->fetch()['area_count'];
    
    echo "<h3>📊 Current Status:</h3>";
    echo "<ul>";
    echo "<li>Existing Voters: <strong>$voter_count</strong></li>";
    echo "<li>Existing Areas: <strong>$area_count</strong></li>";
    echo "</ul>";
    
    if (isset($_POST['setup_safe_hierarchy'])) {
        echo "<h3>🔧 Setting up hierarchy safely...</h3>";
        
        // Step 1: Update areas table enum
        try {
            $db->exec("ALTER TABLE `areas` MODIFY COLUMN `type` enum('division','district','upazila','municipality','union','village','city','ward') NOT NULL DEFAULT 'ward'");
            echo "<div style='background: #d4edda; padding: 10px; margin: 5px 0; border-radius: 3px;'>✅ Updated areas table structure</div>";
        } catch (Exception $e) {
            echo "<div style='background: #fff3cd; padding: 10px; margin: 5px 0; border-radius: 3px;'>⚠️ Areas table already updated</div>";
        }
        
        // Step 2: Temporarily disable foreign key checks
        $db->exec("SET FOREIGN_KEY_CHECKS = 0");
        
        // Step 3: Backup existing voters' area assignments
        $stmt = $db->query("SELECT id, area_id FROM voters WHERE is_active = 1");
        $voter_areas = $stmt->fetchAll();
        
        // Step 4: Update existing areas to new types if needed
        $stmt = $db->query("SELECT id, name, name_bn, type FROM areas WHERE is_active = 1");
        $existing_areas = $stmt->fetchAll();
        
        $updated_areas = 0;
        foreach ($existing_areas as $area) {
            // Convert old types to new hierarchy
            $new_type = $area['type'];
            if ($area['type'] === 'area') {
                $new_type = 'village'; // Convert generic 'area' to 'village'
                $db->exec("UPDATE areas SET type = 'village' WHERE id = {$area['id']}");
                $updated_areas++;
            }
        }
        
        if ($updated_areas > 0) {
            echo "<div style='background: #d4edda; padding: 10px; margin: 5px 0; border-radius: 3px;'>✅ Updated $updated_areas existing areas to new hierarchy</div>";
        }
        
        // Step 5: Add missing hierarchy levels
        $hierarchy_data = [
            // Divisions
            ['Dhaka Division', 'ঢাকা বিভাগ', 'division', null, 'DIV-01'],
            ['Chittagong Division', 'চট্টগ্রাম বিভাগ', 'division', null, 'DIV-02'],
            
            // Districts (will be linked to divisions)
            ['Dhaka District', 'ঢাকা জেলা', 'district', 'DIV-01', 'DIS-01'],
            ['Gazipur District', 'গাজীপুর জেলা', 'district', 'DIV-01', 'DIS-02'],
            
            // Upazilas (will be linked to districts)
            ['Dhanmondi Upazila', 'ধানমন্ডি উপজেলা', 'upazila', 'DIS-01', 'UPZ-01'],
            ['Gulshan Upazila', 'গুলশান উপজেলা', 'upazila', 'DIS-01', 'UPZ-02'],
            ['Tejgaon Upazila', 'তেজগাঁও উপজেলা', 'upazila', 'DIS-01', 'UPZ-03'],
        ];
        
        $created_count = 0;
        foreach ($hierarchy_data as $data) {
            // Check if already exists
            $stmt = $db->prepare("SELECT id FROM areas WHERE code = ?");
            $stmt->execute([$data[4]]);
            if (!$stmt->fetch()) {
                // Find parent ID
                $parent_id = null;
                if ($data[3]) {
                    $stmt = $db->prepare("SELECT id FROM areas WHERE code = ?");
                    $stmt->execute([$data[3]]);
                    $parent = $stmt->fetch();
                    $parent_id = $parent ? $parent['id'] : null;
                }
                
                // Insert new area
                $stmt = $db->prepare("INSERT INTO areas (name, name_bn, type, parent_id, code, description) VALUES (?, ?, ?, ?, ?, ?)");
                $stmt->execute([$data[0], $data[1], $data[2], $parent_id, $data[4], $data[1]]);
                $created_count++;
            }
        }
        
        echo "<div style='background: #d4edda; padding: 10px; margin: 5px 0; border-radius: 3px;'>✅ Created $created_count new hierarchy areas</div>";
        
        // Step 6: Link existing wards to appropriate parents
        $stmt = $db->query("SELECT id, name, name_bn FROM areas WHERE type = 'ward' AND parent_id IS NULL");
        $orphan_wards = $stmt->fetchAll();
        
        if (!empty($orphan_wards)) {
            // Create a default municipality for orphan wards
            $stmt = $db->prepare("SELECT id FROM areas WHERE type = 'upazila' LIMIT 1");
            $stmt->execute();
            $upazila = $stmt->fetch();
            
            if ($upazila) {
                // Create default municipality
                $stmt = $db->prepare("INSERT INTO areas (name, name_bn, type, parent_id, code, description) VALUES (?, ?, 'municipality', ?, ?, ?)");
                $stmt->execute(['Default Municipality', 'ডিফল্ট পৌরসভা', $upazila['id'], 'MUN-DEFAULT', 'ডিফল্ট পৌরসভা']);
                $default_municipality_id = $db->lastInsertId();
                
                // Link orphan wards to default municipality
                foreach ($orphan_wards as $ward) {
                    $db->exec("UPDATE areas SET parent_id = $default_municipality_id WHERE id = {$ward['id']}");
                }
                
                echo "<div style='background: #d4edda; padding: 10px; margin: 5px 0; border-radius: 3px;'>✅ Linked " . count($orphan_wards) . " orphan wards to default municipality</div>";
            }
        }
        
        // Step 7: Re-enable foreign key checks
        $db->exec("SET FOREIGN_KEY_CHECKS = 1");
        
        // Step 8: Verify voter data integrity
        $stmt = $db->query("
            SELECT v.id, v.voter_id, v.name, a.name as area_name 
            FROM voters v 
            LEFT JOIN areas a ON v.area_id = a.id 
            WHERE v.is_active = 1 
            LIMIT 5
        ");
        $sample_voters = $stmt->fetchAll();
        
        echo "<div style='background: #e7f3ff; padding: 15px; border-radius: 5px; margin: 15px 0;'>";
        echo "<h4>✅ Voter Data Integrity Check:</h4>";
        if (!empty($sample_voters)) {
            echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
            echo "<tr><th style='padding: 5px;'>Voter ID</th><th style='padding: 5px;'>Name</th><th style='padding: 5px;'>Area</th></tr>";
            foreach ($sample_voters as $voter) {
                echo "<tr>";
                echo "<td style='padding: 5px;'>{$voter['voter_id']}</td>";
                echo "<td style='padding: 5px;'>{$voter['name']}</td>";
                echo "<td style='padding: 5px;'>" . ($voter['area_name'] ?: 'No Area') . "</td>";
                echo "</tr>";
            }
            echo "</table>";
            echo "<p>✅ All voter data preserved successfully!</p>";
        }
        echo "</div>";
        
        echo "<div style='background: #d4edda; padding: 15px; border-radius: 5px; margin: 15px 0;'>";
        echo "<h4>🎉 Safe Hierarchy Setup Complete!</h4>";
        echo "<p>✅ Existing voter data preserved</p>";
        echo "<p>✅ Administrative hierarchy created</p>";
        echo "<p>✅ Foreign key constraints maintained</p>";
        echo "</div>";
    }
    
    // Show current hierarchy status
    $stmt = $db->query("
        SELECT type, COUNT(*) as count 
        FROM areas 
        WHERE is_active = 1 
        GROUP BY type 
        ORDER BY 
            CASE type 
                WHEN 'division' THEN 1 
                WHEN 'district' THEN 2 
                WHEN 'upazila' THEN 3 
                WHEN 'municipality' THEN 4
                WHEN 'union' THEN 4
                WHEN 'village' THEN 5
                WHEN 'city' THEN 5
                WHEN 'ward' THEN 6 
            END
    ");
    $current_stats = $stmt->fetchAll();
    
    echo "<h3>📊 Current Administrative Structure:</h3>";
    if (!empty($current_stats)) {
        $type_labels = [
            'division' => 'বিভাগ',
            'district' => 'জেলা',
            'upazila' => 'উপজেলা',
            'municipality' => 'পৌরসভা',
            'union' => 'ইউনিয়ন',
            'village' => 'গ্রাম',
            'city' => 'শহর',
            'ward' => 'ওয়ার্ড'
        ];
        
        echo "<div class='row'>";
        foreach ($current_stats as $stat) {
            $label = $type_labels[$stat['type']] ?? ucfirst($stat['type']);
            $color = ['division' => 'primary', 'district' => 'success', 'upazila' => 'info', 'municipality' => 'warning', 'union' => 'secondary', 'village' => 'dark', 'city' => 'danger', 'ward' => 'light'][$stat['type']] ?? 'secondary';
            
            echo "<div style='display: inline-block; margin: 10px; padding: 15px; background: #f8f9fa; border-radius: 5px; border-left: 4px solid #007bff;'>";
            echo "<h4 style='margin: 0; color: #007bff;'>{$stat['count']}</h4>";
            echo "<p style='margin: 5px 0 0 0;'>$label</p>";
            echo "</div>";
        }
        echo "</div>";
    }
    
    // Show setup button if needed
    if (empty($current_stats) || count($current_stats) < 3) {
        echo "<div style='background: #fff3cd; padding: 15px; border-radius: 5px; margin: 15px 0;'>";
        echo "<h4>⚠️ Incomplete Hierarchy Detected</h4>";
        echo "<p>Your administrative structure needs to be completed. This safe setup will:</p>";
        echo "<ul>";
        echo "<li>✅ Preserve all existing voter data</li>";
        echo "<li>✅ Update area types safely</li>";
        echo "<li>✅ Create missing hierarchy levels</li>";
        echo "<li>✅ Maintain foreign key integrity</li>";
        echo "</ul>";
        echo "</div>";
        
        echo "<form method='POST'>";
        echo "<button type='submit' name='setup_safe_hierarchy' style='background: #28a745; color: white; padding: 15px 30px; border: none; border-radius: 5px; font-size: 16px; cursor: pointer;'>";
        echo "🛡️ Safe Hierarchy Setup";
        echo "</button>";
        echo "</form>";
    }
    
} catch (Exception $e) {
    echo "<div style='background: #f8d7da; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
    echo "<h4>❌ Database Error!</h4>";
    echo "<p>Error: " . $e->getMessage() . "</p>";
    echo "</div>";
}

echo "<br><h3>🔗 Next Steps:</h3>";
echo "<ol>";
echo "<li><a href='admin/areas.php'>View Administrative Areas</a></li>";
echo "<li><a href='admin/voters.php'>Manage Voters</a></li>";
echo "<li><a href='polls/create.php'>Create Area-Based Polls</a></li>";
echo "</ol>";

echo "<br><div style='background: #e2e3e5; padding: 10px; border-radius: 5px;'>";
echo "<strong>Security Note:</strong> Delete this file after setup.";
echo "</div>";
?>
