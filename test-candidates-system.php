<?php
/**
 * Test Candidates System
 * Comprehensive test for all candidate management features
 */

require_once 'config/config.php';

echo "<h1>🧪 Candidates System Test</h1>";

try {
    $db = getDB();
    
    // Test 1: Check if all tables exist
    echo "<h2>📋 Test 1: Database Tables</h2>";
    $required_tables = ['positions', 'candidates', 'election_symbols', 'candidate_elections'];
    
    foreach ($required_tables as $table) {
        $stmt = $db->query("SHOW TABLES LIKE '$table'");
        if ($stmt->rowCount() > 0) {
            echo "<p>✅ Table '$table' exists</p>";
        } else {
            echo "<p>❌ Table '$table' missing</p>";
        }
    }
    
    // Test 2: Check positions data
    echo "<h2>🏛️ Test 2: Positions Data</h2>";
    $stmt = $db->query("SELECT COUNT(*) as count FROM positions WHERE is_active = 1");
    $position_count = $stmt->fetch()['count'];
    echo "<p>📊 Total active positions: <strong>$position_count</strong></p>";
    
    if ($position_count > 0) {
        $stmt = $db->query("SELECT election_level, COUNT(*) as count FROM positions WHERE is_active = 1 GROUP BY election_level");
        $levels = $stmt->fetchAll();
        
        echo "<ul>";
        foreach ($levels as $level) {
            echo "<li><strong>" . ucfirst($level['election_level']) . ":</strong> {$level['count']} positions</li>";
        }
        echo "</ul>";
    }
    
    // Test 3: Check symbols data
    echo "<h2>🎯 Test 3: Election Symbols</h2>";
    $stmt = $db->query("SELECT COUNT(*) as count FROM election_symbols WHERE is_available = 1");
    $symbol_count = $stmt->fetch()['count'];
    echo "<p>📊 Total available symbols: <strong>$symbol_count</strong></p>";
    
    if ($symbol_count > 0) {
        $stmt = $db->query("SELECT name, name_bn FROM election_symbols WHERE is_available = 1 LIMIT 5");
        $symbols = $stmt->fetchAll();
        
        echo "<p><strong>Sample symbols:</strong></p>";
        echo "<ul>";
        foreach ($symbols as $symbol) {
            echo "<li>{$symbol['name']} ({$symbol['name_bn']})</li>";
        }
        echo "</ul>";
    }
    
    // Test 4: Check candidates data
    echo "<h2>👥 Test 4: Candidates Data</h2>";
    $stmt = $db->query("SELECT COUNT(*) as count FROM candidates WHERE is_active = 1");
    $candidate_count = $stmt->fetch()['count'];
    echo "<p>📊 Total active candidates: <strong>$candidate_count</strong></p>";
    
    if ($candidate_count > 0) {
        $stmt = $db->query("
            SELECT c.name, c.candidate_id, p.title as position_title, a.name as area_name
            FROM candidates c
            LEFT JOIN positions p ON c.position_id = p.id
            LEFT JOIN areas a ON c.area_id = a.id
            WHERE c.is_active = 1
            LIMIT 5
        ");
        $candidates = $stmt->fetchAll();
        
        echo "<p><strong>Sample candidates:</strong></p>";
        echo "<ul>";
        foreach ($candidates as $candidate) {
            echo "<li><strong>{$candidate['name']}</strong> ({$candidate['candidate_id']}) - {$candidate['position_title']} - {$candidate['area_name']}</li>";
        }
        echo "</ul>";
    }
    
    // Test 5: Check upload directories
    echo "<h2>📁 Test 5: Upload Directories</h2>";
    $upload_dirs = [
        'uploads/candidates/photos',
        'uploads/candidates/symbols',
        'uploads/candidates/thumbnails'
    ];
    
    foreach ($upload_dirs as $dir) {
        if (is_dir($dir) && is_writable($dir)) {
            echo "<p>✅ Directory '$dir' exists and writable</p>";
        } else {
            echo "<p>❌ Directory '$dir' missing or not writable</p>";
        }
    }
    
    // Test 6: Check helper functions
    echo "<h2>🔧 Test 6: Helper Functions</h2>";
    $helper_functions = [
        'getCurrentUser',
        'generateCSRFToken',
        'validateCSRFToken',
        'getFlashMessage',
        'setFlashMessage',
        'sanitizeInput',
        'getElectionLevelColor'
    ];
    
    foreach ($helper_functions as $function) {
        if (function_exists($function)) {
            echo "<p>✅ Function '$function' available</p>";
        } else {
            echo "<p>❌ Function '$function' missing</p>";
        }
    }
    
    // Test 7: Test page accessibility
    echo "<h2>🌐 Test 7: Page Accessibility</h2>";
    $pages = [
        'admin/positions.php' => 'Positions Management',
        'admin/candidates.php' => 'Candidates Management',
        'admin/candidate-cards.php' => 'Candidate Cards View'
    ];
    
    foreach ($pages as $page => $title) {
        if (file_exists($page)) {
            echo "<p>✅ Page '$title' ($page) exists</p>";
        } else {
            echo "<p>❌ Page '$title' ($page) missing</p>";
        }
    }
    
    // Test 8: Database relationships
    echo "<h2>🔗 Test 8: Database Relationships</h2>";
    
    // Test position-candidate relationship
    $stmt = $db->query("
        SELECT COUNT(*) as count 
        FROM candidates c 
        INNER JOIN positions p ON c.position_id = p.id 
        WHERE c.is_active = 1
    ");
    $linked_candidates = $stmt->fetch()['count'];
    echo "<p>📊 Candidates with valid positions: <strong>$linked_candidates</strong></p>";
    
    // Test area-candidate relationship
    $stmt = $db->query("
        SELECT COUNT(*) as count 
        FROM candidates c 
        INNER JOIN areas a ON c.area_id = a.id 
        WHERE c.is_active = 1
    ");
    $area_linked_candidates = $stmt->fetch()['count'];
    echo "<p>📊 Candidates with valid areas: <strong>$area_linked_candidates</strong></p>";
    
    // Overall system status
    echo "<h2>🎯 Overall System Status</h2>";
    
    $total_score = 0;
    $max_score = 8;
    
    // Score calculation
    if ($position_count > 0) $total_score++;
    if ($symbol_count > 0) $total_score++;
    if (is_dir('uploads/candidates/photos')) $total_score++;
    if (function_exists('getCurrentUser')) $total_score++;
    if (file_exists('admin/candidates.php')) $total_score++;
    if (file_exists('admin/positions.php')) $total_score++;
    if (file_exists('admin/candidate-cards.php')) $total_score++;
    if ($total_score >= 6) $total_score++; // Bonus for overall functionality
    
    $percentage = round(($total_score / $max_score) * 100);
    
    if ($percentage >= 90) {
        $status = "🎉 Excellent";
        $color = "#d4edda";
        $message = "System is fully functional and ready for production!";
    } elseif ($percentage >= 70) {
        $status = "✅ Good";
        $color = "#fff3cd";
        $message = "System is mostly functional with minor issues.";
    } elseif ($percentage >= 50) {
        $status = "⚠️ Fair";
        $color = "#f8d7da";
        $message = "System has some issues that need attention.";
    } else {
        $status = "❌ Poor";
        $color = "#f8d7da";
        $message = "System needs significant fixes before use.";
    }
    
    echo "<div style='background: $color; padding: 20px; border-radius: 10px; margin: 20px 0;'>";
    echo "<h3>$status - Score: $total_score/$max_score ($percentage%)</h3>";
    echo "<p><strong>$message</strong></p>";
    
    if ($percentage >= 70) {
        echo "<h4>🚀 Ready to Use:</h4>";
        echo "<ul>";
        echo "<li><a href='admin/positions.php'>Manage Positions</a></li>";
        echo "<li><a href='admin/candidates.php'>Manage Candidates</a></li>";
        echo "<li><a href='admin/candidate-cards.php'>View Candidate Cards</a></li>";
        echo "</ul>";
    } else {
        echo "<h4>🔧 Next Steps:</h4>";
        echo "<ul>";
        echo "<li>Run setup-candidates-safe.php if not done</li>";
        echo "<li>Check file permissions on uploads directory</li>";
        echo "<li>Verify database connection</li>";
        echo "<li>Check PHP error logs</li>";
        echo "</ul>";
    }
    
    echo "</div>";
    
} catch (Exception $e) {
    echo "<div style='background: #f8d7da; padding: 20px; border-radius: 10px;'>";
    echo "<h3>❌ Test Failed</h3>";
    echo "<p><strong>Error:</strong> " . $e->getMessage() . "</p>";
    echo "<p><strong>💡 Solutions:</strong></p>";
    echo "<ul>";
    echo "<li>Check database connection in config.php</li>";
    echo "<li>Run setup-candidates-safe.php first</li>";
    echo "<li>Verify database credentials</li>";
    echo "<li>Check PHP error logs</li>";
    echo "</ul>";
    echo "</div>";
}
?>

<!DOCTYPE html>
<html>
<head>
    <title>Candidates System Test</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; }
        .container { max-width: 1000px; margin: 0 auto; padding: 20px; }
        h1, h2 { color: #333; }
        p { margin: 5px 0; }
        ul { margin: 10px 0; }
    </style>
</head>
<body>
<div class="container">
    <!-- Content generated above -->
</div>
</body>
</html>
