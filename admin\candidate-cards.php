<?php
/**
 * Candidate Cards View
 * Display candidates in card format for better visual presentation
 */

require_once '../config/config.php';

// Check if user is logged in and is admin
if (!isLoggedIn() || !isAdmin()) {
    redirect(APP_URL . '/auth/login.php');
}

$search = $_GET['search'] ?? '';
$level_filter = $_GET['level'] ?? '';
$area_filter = $_GET['area'] ?? '';

try {
    $db = getDB();
    
    // Build search query
    $where_conditions = ['c.is_active = 1'];
    $params = [];
    
    if (!empty($search)) {
        $where_conditions[] = "(c.name LIKE ? OR c.name_bn LIKE ? OR c.candidate_id LIKE ?)";
        $search_term = "%$search%";
        $params[] = $search_term;
        $params[] = $search_term;
        $params[] = $search_term;
    }
    
    if (!empty($level_filter)) {
        $where_conditions[] = "c.election_level = ?";
        $params[] = $level_filter;
    }
    
    if (!empty($area_filter)) {
        $where_conditions[] = "c.area_id = ?";
        $params[] = $area_filter;
    }
    
    $where_clause = implode(' AND ', $where_conditions);
    
    // Get candidates
    $sql = "SELECT c.*, a.name as area_name, a.name_bn as area_name_bn,
                   p.title as position_title, p.title_bn as position_title_bn
            FROM candidates c
            LEFT JOIN areas a ON c.area_id = a.id
            LEFT JOIN positions p ON c.position_id = p.id
            WHERE $where_clause
            ORDER BY c.election_level, c.created_at DESC";
    
    $stmt = $db->prepare($sql);
    $stmt->execute($params);
    $candidates = $stmt->fetchAll();
    
    // Get areas for filters
    $areas_stmt = $db->query("SELECT id, name, name_bn, type FROM areas WHERE is_active = 1 ORDER BY type, name");
    $areas = $areas_stmt->fetchAll();
    
} catch (Exception $e) {
    $error = 'Database error: ' . $e->getMessage();
    $candidates = [];
    $areas = [];
}

// Election levels
$election_levels = [
    'ward' => 'ওয়ার্ড কমিশনার',
    'village' => 'গ্রাম প্রধান/চেয়ারম্যান', 
    'upazila' => 'উপজেলা চেয়ারম্যান',
    'district' => 'জেলা প্রশাসক',
    'national' => 'জাতীয় সংসদ সদস্য'
];

function getElectionLevelColor($level) {
    $colors = [
        'ward' => 'primary',
        'village' => 'success',
        'upazila' => 'info',
        'district' => 'warning',
        'national' => 'danger'
    ];
    return $colors[$level] ?? 'secondary';
}

// Helper functions for candidate-cards.php
if (!function_exists('getCurrentUser')) {
    function getCurrentUser() {
        return $_SESSION['user'] ?? ['id' => 1, 'username' => 'admin'];
    }
}

if (!function_exists('generateCSRFToken')) {
    function generateCSRFToken() {
        if (!isset($_SESSION['csrf_token'])) {
            $_SESSION['csrf_token'] = bin2hex(random_bytes(32));
        }
        return $_SESSION['csrf_token'];
    }
}

if (!function_exists('validateCSRFToken')) {
    function validateCSRFToken($token) {
        return isset($_SESSION['csrf_token']) && hash_equals($_SESSION['csrf_token'], $token);
    }
}
?>

<!DOCTYPE html>
<html lang="bn">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>প্রার্থী কার্ড ভিউ - OVS</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        .candidate-card {
            transition: transform 0.2s, box-shadow 0.2s;
            height: 100%;
        }
        
        .candidate-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.15);
        }
        
        .candidate-photo {
            width: 80px;
            height: 80px;
            object-fit: cover;
            border: 3px solid #fff;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .candidate-symbol {
            width: 50px;
            height: 50px;
            object-fit: contain;
        }
        
        .election-level-badge {
            position: absolute;
            top: 10px;
            right: 10px;
        }
        
        .party-badge {
            background: linear-gradient(45deg, #007bff, #0056b3);
            color: white;
            border: none;
        }
        
        .candidate-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 10px 10px 0 0;
            padding: 20px;
            position: relative;
        }
        
        .candidate-body {
            padding: 20px;
        }
        
        .contact-info {
            background: #f8f9fa;
            border-radius: 8px;
            padding: 10px;
            margin-top: 10px;
        }
    </style>
</head>
<body>

<?php include '../includes/admin-nav.php'; ?>

<div class="container-fluid mt-4">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h2><i class="fas fa-id-card"></i> প্রার্থী কার্ড ভিউ</h2>
                <div>
                    <a href="candidates.php" class="btn btn-outline-secondary me-2">
                        <i class="fas fa-list"></i> তালিকা ভিউ
                    </a>
                    <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addCandidateModal">
                        <i class="fas fa-plus"></i> নতুন প্রার্থী
                    </button>
                </div>
            </div>

            <!-- Filters -->
            <div class="card mb-4">
                <div class="card-body">
                    <form method="GET" class="row g-3">
                        <div class="col-md-4">
                            <input type="text" class="form-control" name="search" placeholder="নাম বা আইডি দিয়ে খুঁজুন" value="<?php echo htmlspecialchars($search); ?>">
                        </div>
                        <div class="col-md-3">
                            <select class="form-select" name="level">
                                <option value="">সব ধরনের নির্বাচন</option>
                                <?php foreach ($election_levels as $level => $label): ?>
                                    <option value="<?php echo $level; ?>" <?php echo $level_filter === $level ? 'selected' : ''; ?>>
                                        <?php echo $label; ?>
                                    </option>
                                <?php endforeach; ?>
                            </select>
                        </div>
                        <div class="col-md-3">
                            <select class="form-select" name="area">
                                <option value="">সব এলাকা</option>
                                <?php foreach ($areas as $area): ?>
                                    <option value="<?php echo $area['id']; ?>" <?php echo $area_filter == $area['id'] ? 'selected' : ''; ?>>
                                        <?php echo $area['name_bn'] ?: $area['name']; ?> (<?php echo ucfirst($area['type']); ?>)
                                    </option>
                                <?php endforeach; ?>
                            </select>
                        </div>
                        <div class="col-md-2">
                            <button type="submit" class="btn btn-outline-primary w-100">
                                <i class="fas fa-search"></i> খুঁজুন
                            </button>
                        </div>
                    </form>
                </div>
            </div>

            <!-- Candidates Cards -->
            <?php if (empty($candidates)): ?>
                <div class="text-center py-5">
                    <i class="fas fa-user-tie fa-5x text-muted mb-4"></i>
                    <h3 class="text-muted">কোন প্রার্থী পাওয়া যায়নি</h3>
                    <p class="text-muted">নতুন প্রার্থী যোগ করতে উপরের বাটনে ক্লিক করুন</p>
                    <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addCandidateModal">
                        <i class="fas fa-plus"></i> প্রথম প্রার্থী যোগ করুন
                    </button>
                </div>
            <?php else: ?>
                <div class="row">
                    <?php foreach ($candidates as $candidate): ?>
                        <div class="col-lg-4 col-md-6 mb-4">
                            <div class="card candidate-card">
                                <!-- Election Level Badge -->
                                <span class="badge bg-<?php echo getElectionLevelColor($candidate['election_level']); ?> election-level-badge">
                                    <?php echo $election_levels[$candidate['election_level']] ?? $candidate['election_level']; ?>
                                </span>
                                
                                <!-- Candidate Header -->
                                <div class="candidate-header text-center">
                                    <!-- Photo -->
                                    <?php if ($candidate['photo']): ?>
                                        <img src="../<?php echo $candidate['photo']; ?>" 
                                             alt="<?php echo htmlspecialchars($candidate['name']); ?>" 
                                             class="rounded-circle candidate-photo mb-3">
                                    <?php else: ?>
                                        <div class="bg-white rounded-circle candidate-photo d-flex align-items-center justify-content-center mb-3 mx-auto">
                                            <i class="fas fa-user fa-2x text-muted"></i>
                                        </div>
                                    <?php endif; ?>
                                    
                                    <!-- Name -->
                                    <h5 class="mb-1"><?php echo htmlspecialchars($candidate['name']); ?></h5>
                                    <?php if ($candidate['name_bn']): ?>
                                        <p class="mb-2 opacity-75"><?php echo htmlspecialchars($candidate['name_bn']); ?></p>
                                    <?php endif; ?>
                                    
                                    <!-- Candidate ID -->
                                    <small class="badge bg-light text-dark"><?php echo $candidate['candidate_id']; ?></small>
                                </div>
                                
                                <!-- Candidate Body -->
                                <div class="candidate-body">
                                    <!-- Position Info -->
                                    <?php if ($candidate['position_title']): ?>
                                        <div class="text-center mb-3">
                                            <span class="badge bg-success">
                                                <i class="fas fa-briefcase"></i>
                                                <?php echo htmlspecialchars($candidate['position_title_bn'] ?: $candidate['position_title']); ?>
                                            </span>
                                        </div>
                                    <?php endif; ?>

                                    <!-- Party Info -->
                                    <?php if ($candidate['party_name']): ?>
                                        <div class="text-center mb-3">
                                            <span class="badge party-badge">
                                                <i class="fas fa-flag"></i>
                                                <?php echo htmlspecialchars($candidate['party_name_bn'] ?: $candidate['party_name']); ?>
                                            </span>
                                        </div>
                                    <?php endif; ?>

                                    <!-- Area Info -->
                                    <?php if ($candidate['area_name']): ?>
                                        <div class="text-center mb-3">
                                            <i class="fas fa-map-marker-alt text-primary"></i>
                                            <small><?php echo htmlspecialchars($candidate['area_name_bn'] ?: $candidate['area_name']); ?></small>
                                        </div>
                                    <?php endif; ?>
                                    
                                    <!-- Symbol -->
                                    <?php if ($candidate['symbol']): ?>
                                        <div class="text-center mb-3">
                                            <div class="d-flex align-items-center justify-content-center">
                                                <img src="../<?php echo $candidate['symbol']; ?>" 
                                                     alt="Symbol" class="candidate-symbol me-2">
                                                <?php if ($candidate['symbol_name']): ?>
                                                    <div>
                                                        <small class="d-block fw-bold">
                                                            <?php echo htmlspecialchars($candidate['symbol_name_bn'] ?: $candidate['symbol_name']); ?>
                                                        </small>
                                                        <small class="text-muted">নির্বাচনী প্রতীক</small>
                                                    </div>
                                                <?php endif; ?>
                                            </div>
                                        </div>
                                    <?php endif; ?>
                                    
                                    <!-- Description -->
                                    <?php if ($candidate['description'] || $candidate['description_bn']): ?>
                                        <div class="mb-3">
                                            <small class="text-muted">
                                                <?php echo htmlspecialchars($candidate['description_bn'] ?: $candidate['description']); ?>
                                            </small>
                                        </div>
                                    <?php endif; ?>
                                    
                                    <!-- Contact Info -->
                                    <?php if ($candidate['contact_mobile'] || $candidate['contact_email']): ?>
                                        <div class="contact-info">
                                            <h6 class="mb-2"><i class="fas fa-phone"></i> যোগাযোগ</h6>
                                            <?php if ($candidate['contact_mobile']): ?>
                                                <div class="mb-1">
                                                    <i class="fas fa-mobile-alt text-success"></i>
                                                    <small><?php echo $candidate['contact_mobile']; ?></small>
                                                </div>
                                            <?php endif; ?>
                                            <?php if ($candidate['contact_email']): ?>
                                                <div>
                                                    <i class="fas fa-envelope text-info"></i>
                                                    <small><?php echo $candidate['contact_email']; ?></small>
                                                </div>
                                            <?php endif; ?>
                                        </div>
                                    <?php endif; ?>
                                    
                                    <!-- Action Buttons -->
                                    <div class="d-flex justify-content-between mt-3">
                                        <button type="button" class="btn btn-sm btn-outline-primary" 
                                                onclick="viewCandidate('<?php echo $candidate['candidate_id']; ?>')">
                                            <i class="fas fa-eye"></i> বিস্তারিত
                                        </button>
                                        <button type="button" class="btn btn-sm btn-outline-warning" 
                                                onclick="editCandidate('<?php echo $candidate['candidate_id']; ?>')">
                                            <i class="fas fa-edit"></i> সম্পাদনা
                                        </button>
                                        <button type="button" class="btn btn-sm btn-outline-danger" 
                                                onclick="deleteCandidate('<?php echo $candidate['candidate_id']; ?>', '<?php echo htmlspecialchars($candidate['name']); ?>')">
                                            <i class="fas fa-trash"></i> মুছুন
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    <?php endforeach; ?>
                </div>
                
                <!-- Statistics -->
                <div class="row mt-4">
                    <div class="col-12">
                        <div class="card">
                            <div class="card-body">
                                <h6><i class="fas fa-chart-bar"></i> পরিসংখ্যান</h6>
                                <div class="row text-center">
                                    <div class="col-md-2">
                                        <h4 class="text-primary"><?php echo count($candidates); ?></h4>
                                        <small>মোট প্রার্থী</small>
                                    </div>
                                    <?php
                                    $level_counts = [];
                                    foreach ($candidates as $candidate) {
                                        $level = $candidate['election_level'];
                                        $level_counts[$level] = ($level_counts[$level] ?? 0) + 1;
                                    }
                                    ?>
                                    <?php foreach ($level_counts as $level => $count): ?>
                                        <div class="col-md-2">
                                            <h4 class="text-<?php echo getElectionLevelColor($level); ?>"><?php echo $count; ?></h4>
                                            <small><?php echo $election_levels[$level] ?? $level; ?></small>
                                        </div>
                                    <?php endforeach; ?>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            <?php endif; ?>
        </div>
    </div>
</div>

<script>
function viewCandidate(candidateId) {
    alert('View candidate: ' + candidateId);
}

function editCandidate(candidateId) {
    alert('Edit candidate: ' + candidateId);
}

function deleteCandidate(candidateId, candidateName) {
    if (confirm('আপনি কি নিশ্চিত যে "' + candidateName + '" প্রার্থীকে মুছে ফেলতে চান?')) {
        // Implementation for delete
        alert('Delete candidate: ' + candidateId);
    }
}
</script>

<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
