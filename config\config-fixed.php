<?php
/**
 * Fixed Configuration File
 * Copy this to config.php and update your database settings
 */

// Error reporting (set to 0 in production)
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Timezone
date_default_timezone_set('UTC');

// Application settings
define('APP_NAME', 'Online Voting System');
define('APP_VERSION', '1.0.0');
define('APP_URL', 'http://localhost/ovs');  // UPDATE THIS to your URL
define('APP_DEBUG', true);

// Security settings
define('CSRF_TOKEN_NAME', 'csrf_token');
define('SESSION_LIFETIME', 3600); // 1 hour
define('PASSWORD_MIN_LENGTH', 6);
define('MAX_LOGIN_ATTEMPTS', 5);
define('LOGIN_LOCKOUT_TIME', 900); // 15 minutes

// File upload settings
define('UPLOAD_MAX_SIZE', 5 * 1024 * 1024); // 5MB
define('UPLOAD_ALLOWED_TYPES', ['jpg', 'jpeg', 'png', 'gif']);
define('UPLOAD_PATH', __DIR__ . '/../assets/uploads/');

// Pagination settings
define('ITEMS_PER_PAGE', 10);
define('MAX_ITEMS_PER_PAGE', 100);

// Poll settings
define('MAX_POLLS_PER_USER', 10);
define('MAX_POLL_OPTIONS', 20);
define('DEFAULT_POLL_DURATION', 7); // days

// Real-time update settings
define('AJAX_POLL_INTERVAL', 3000); // 3 seconds
define('ENABLE_REAL_TIME', true);

// Email settings (for notifications)
define('SMTP_HOST', '');
define('SMTP_PORT', 587);
define('SMTP_USERNAME', '');
define('SMTP_PASSWORD', '');
define('SMTP_FROM_EMAIL', '<EMAIL>');
define('SMTP_FROM_NAME', APP_NAME);

// Cache settings
define('CACHE_ENABLED', false);
define('CACHE_LIFETIME', 300); // 5 minutes

// Database configuration - UPDATE THESE VALUES
define('DB_HOST', 'localhost');
define('DB_NAME', 'ovs_db');        // Make sure this database exists
define('DB_USER', 'root');          // Your MySQL username
define('DB_PASS', '');              // Your MySQL password (empty for XAMPP default)
define('DB_CHARSET', 'utf8mb4');

// Include required files
require_once __DIR__ . '/database.php';
require_once __DIR__ . '/session.php';
require_once __DIR__ . '/../includes/functions.php';

// Start session after all configuration is loaded
if (session_status() === PHP_SESSION_NONE) {
    startSecureSession();
}

// CSRF Token generation
function generateCSRFToken() {
    if (!isset($_SESSION[CSRF_TOKEN_NAME])) {
        $_SESSION[CSRF_TOKEN_NAME] = bin2hex(random_bytes(32));
    }
    return $_SESSION[CSRF_TOKEN_NAME];
}

// CSRF Token validation
function validateCSRFToken($token) {
    return isset($_SESSION[CSRF_TOKEN_NAME]) && hash_equals($_SESSION[CSRF_TOKEN_NAME], $token);
}

// Get current user
function getCurrentUser() {
    return $_SESSION['user'] ?? null;
}

// Check if user is logged in
function isLoggedIn() {
    return isset($_SESSION['user']) && isset($_SESSION['user']['id']);
}

// Check if user has specific role
function hasRole($role) {
    $user = getCurrentUser();
    return $user && $user['role'] === $role;
}

// Check if user is admin
function isAdmin() {
    return hasRole('admin');
}

// Check if user is moderator or admin
function isModerator() {
    return hasRole('moderator') || hasRole('admin');
}

// Redirect function
function redirect($url, $permanent = false) {
    $status = $permanent ? 301 : 302;
    header("Location: $url", true, $status);
    exit();
}

// Flash message functions
function setFlashMessage($type, $message) {
    $_SESSION['flash'][] = ['type' => $type, 'message' => $message];
}

function getFlashMessages() {
    $messages = $_SESSION['flash'] ?? [];
    unset($_SESSION['flash']);
    return $messages;
}

// Sanitize input
function sanitizeInput($input) {
    return htmlspecialchars(trim($input), ENT_QUOTES, 'UTF-8');
}

// Validate email
function isValidEmail($email) {
    return filter_var($email, FILTER_VALIDATE_EMAIL) !== false;
}

// Generate random string
function generateRandomString($length = 32) {
    return bin2hex(random_bytes($length / 2));
}

// Format date for display
function formatDate($date, $format = 'Y-m-d H:i:s') {
    if ($date instanceof DateTime) {
        return $date->format($format);
    }
    return date($format, strtotime($date));
}

// Time ago function
function timeAgo($datetime) {
    $time = time() - strtotime($datetime);
    
    if ($time < 60) return 'just now';
    if ($time < 3600) return floor($time/60) . ' minutes ago';
    if ($time < 86400) return floor($time/3600) . ' hours ago';
    if ($time < 2592000) return floor($time/86400) . ' days ago';
    if ($time < 31536000) return floor($time/2592000) . ' months ago';
    return floor($time/31536000) . ' years ago';
}

// Log activity
function logActivity($action, $resource, $resource_id = null, $metadata = null) {
    try {
        $db = getDB();
        $user = getCurrentUser();
        
        $stmt = $db->prepare("
            INSERT INTO activity_logs (user_id, action, resource, resource_id, metadata, ip_address, user_agent) 
            VALUES (?, ?, ?, ?, ?, ?, ?)
        ");
        
        $stmt->execute([
            $user['id'] ?? null,
            $action,
            $resource,
            $resource_id,
            $metadata ? json_encode($metadata) : null,
            $_SERVER['REMOTE_ADDR'] ?? null,
            $_SERVER['HTTP_USER_AGENT'] ?? null
        ]);
    } catch (Exception $e) {
        error_log("Failed to log activity: " . $e->getMessage());
    }
}
?>
