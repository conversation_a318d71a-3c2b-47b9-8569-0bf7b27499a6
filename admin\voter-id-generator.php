<?php
/**
 * Voter ID Generator Tool
 * Test and generate voter IDs with different formats
 */

require_once '../config/config.php';
require_once '../includes/voter-id-generator.php';

// Check if user is admin
if (!isLoggedIn() || !isAdmin()) {
    redirect(APP_URL . '/auth/login.php');
}

echo "<h2>🎲 Voter ID Generator Tool</h2>";

$formats = getVoterIDFormats();

if (isset($_POST['generate_bulk'])) {
    $format = $_POST['format'];
    $count = intval($_POST['count']);
    
    try {
        $db = getDB();
        $voter_ids = bulkGenerateVoterIDs($db, $count, $format);
        
        echo "<h3>📋 Generated Voter IDs:</h3>";
        echo "<div style='background: #d4edda; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
        echo "<p><strong>Format:</strong> $format</p>";
        echo "<p><strong>Count:</strong> " . count($voter_ids) . "</p>";
        echo "<h4>Generated IDs:</h4>";
        echo "<ol>";
        foreach ($voter_ids as $id) {
            echo "<li><code>$id</code></li>";
        }
        echo "</ol>";
        echo "</div>";
        
    } catch (Exception $e) {
        echo "<div style='background: #f8d7da; padding: 15px; border-radius: 5px;'>";
        echo "<h4>❌ Error: " . $e->getMessage() . "</h4>";
        echo "</div>";
    }
}

if (isset($_POST['test_format'])) {
    $format = $_POST['test_format'];
    
    echo "<h3>🧪 Format Test Results:</h3>";
    echo "<div style='background: #e7f3ff; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
    echo "<p><strong>Format:</strong> <code>$format</code></p>";
    echo "<h4>Sample Generated IDs:</h4>";
    echo "<ol>";
    for ($i = 0; $i < 10; $i++) {
        $sample_id = generateVoterIDPattern($format);
        echo "<li><code>$sample_id</code></li>";
    }
    echo "</ol>";
    echo "</div>";
}

// Show current statistics
try {
    $db = getDB();
    
    echo "<h3>📊 Current Statistics:</h3>";
    
    // Total voters
    $stmt = $db->query("SELECT COUNT(*) as total FROM voters WHERE is_active = 1");
    $total_voters = $stmt->fetch()['total'];
    
    // Voter ID patterns
    $stmt = $db->query("
        SELECT 
            CASE 
                WHEN voter_id REGEXP '^V[0-9]{4}[0-9]{2}[0-9]{5}$' THEN 'Year-Month-Random'
                WHEN voter_id REGEXP '^V[0-9]{2}[A-Z]{2,3}[0-9]{4}$' THEN 'Year-Area-Random'
                WHEN voter_id REGEXP '^V[0-9]+$' THEN 'Simple Numeric'
                ELSE 'Custom Format'
            END as format_type,
            COUNT(*) as count
        FROM voters 
        WHERE is_active = 1 
        GROUP BY format_type
        ORDER BY count DESC
    ");
    $patterns = $stmt->fetchAll();
    
    echo "<div style='background: #f8f9fa; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
    echo "<p><strong>Total Active Voters:</strong> $total_voters</p>";
    echo "<h4>Existing ID Patterns:</h4>";
    echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
    echo "<tr style='background: #e9ecef;'>";
    echo "<th style='padding: 10px;'>Format Type</th>";
    echo "<th style='padding: 10px;'>Count</th>";
    echo "<th style='padding: 10px;'>Percentage</th>";
    echo "</tr>";
    
    foreach ($patterns as $pattern) {
        $percentage = $total_voters > 0 ? round(($pattern['count'] / $total_voters) * 100, 1) : 0;
        echo "<tr>";
        echo "<td style='padding: 10px;'>" . $pattern['format_type'] . "</td>";
        echo "<td style='padding: 10px;'>" . $pattern['count'] . "</td>";
        echo "<td style='padding: 10px;'>" . $percentage . "%</td>";
        echo "</tr>";
    }
    echo "</table>";
    echo "</div>";
    
} catch (Exception $e) {
    echo "<div style='background: #f8d7da; padding: 15px; border-radius: 5px;'>";
    echo "<h4>❌ Database Error: " . $e->getMessage() . "</h4>";
    echo "</div>";
}
?>

<!DOCTYPE html>
<html>
<head>
    <title>Voter ID Generator</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body>

<div class="container mt-4">
    <div class="row">
        <div class="col-md-6">
            <div class="card">
                <div class="card-header">
                    <h5>🎲 Generate Voter IDs</h5>
                </div>
                <div class="card-body">
                    <form method="POST">
                        <div class="mb-3">
                            <label for="format" class="form-label">Select Format</label>
                            <select class="form-select" name="format" required>
                                <?php foreach ($formats as $format => $info): ?>
                                <option value="<?php echo htmlspecialchars($format); ?>">
                                    <?php echo $info['name']; ?> - <?php echo $info['example']; ?>
                                </option>
                                <?php endforeach; ?>
                            </select>
                        </div>
                        <div class="mb-3">
                            <label for="count" class="form-label">Number of IDs</label>
                            <input type="number" class="form-control" name="count" min="1" max="50" value="5" required>
                        </div>
                        <button type="submit" name="generate_bulk" class="btn btn-primary">
                            <i class="fas fa-magic"></i> Generate IDs
                        </button>
                    </form>
                </div>
            </div>
        </div>
        
        <div class="col-md-6">
            <div class="card">
                <div class="card-header">
                    <h5>🧪 Test Format</h5>
                </div>
                <div class="card-body">
                    <form method="POST">
                        <div class="mb-3">
                            <label for="test_format" class="form-label">Custom Format</label>
                            <input type="text" class="form-control" name="test_format" 
                                   placeholder="V{YYYY}{MM}{RRRRR}" required>
                            <div class="form-text">
                                Use placeholders: {YYYY}, {YY}, {MM}, {DD}, {RRRRR}, {RRRR}, {RRR}, {RR}
                            </div>
                        </div>
                        <button type="submit" name="test_format" class="btn btn-info">
                            <i class="fas fa-flask"></i> Test Format
                        </button>
                    </form>
                </div>
            </div>
        </div>
    </div>
    
    <div class="row mt-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5>📋 Available Formats</h5>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-striped">
                            <thead>
                                <tr>
                                    <th>Format Name</th>
                                    <th>Pattern</th>
                                    <th>Example</th>
                                    <th>Description</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($formats as $pattern => $info): ?>
                                <tr>
                                    <td><strong><?php echo $info['name']; ?></strong></td>
                                    <td><code><?php echo htmlspecialchars($pattern); ?></code></td>
                                    <td><code><?php echo $info['example']; ?></code></td>
                                    <td><?php echo $info['description']; ?></td>
                                </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="row mt-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5>🔧 Quick Actions</h5>
                </div>
                <div class="card-body">
                    <a href="voters.php" class="btn btn-secondary me-2">
                        <i class="fas fa-users"></i> Back to Voters
                    </a>
                    <a href="test-template.php" class="btn btn-success me-2">
                        <i class="fas fa-download"></i> Test Template
                    </a>
                    <a href="debug-upload.php" class="btn btn-warning">
                        <i class="fas fa-bug"></i> Debug Upload
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// Live preview of format
document.addEventListener('DOMContentLoaded', function() {
    const formatSelect = document.querySelector('select[name="format"]');
    const testFormatInput = document.querySelector('input[name="test_format"]');
    
    if (formatSelect) {
        formatSelect.addEventListener('change', function() {
            console.log('Selected format:', this.value);
        });
    }
    
    if (testFormatInput) {
        testFormatInput.addEventListener('input', function() {
            // Could add live preview here
        });
    }
});
</script>

</body>
</html>
