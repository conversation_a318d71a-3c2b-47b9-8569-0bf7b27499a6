<?php
/**
 * Ward Code Demo
 * Demonstrate ward-based voter ID generation
 */

require_once '../config/config.php';
require_once '../includes/voter-id-generator.php';

// Check if user is admin
if (!isLoggedIn() || !isAdmin()) {
    redirect(APP_URL . '/auth/login.php');
}

echo "<h2>🏛️ Ward-Based Voter ID Demo</h2>";

if (isset($_POST['demo_ward_codes'])) {
    echo "<h3>📋 Ward Code Generation Demo:</h3>";
    
    try {
        $db = getDB();
        
        // Get sample wards
        $stmt = $db->query("
            SELECT w.id, w.name, w.name_bn,
                   u.name as union_name, u.name_bn as union_name_bn,
                   up.name as upazila_name, up.name_bn as upazila_name_bn,
                   d.name as district_name, d.name_bn as district_name_bn
            FROM areas w
            LEFT JOIN areas u ON w.parent_id = u.id AND u.type IN ('union', 'municipality')
            LEFT JOIN areas up ON u.parent_id = up.id AND up.type = 'upazila'
            LEFT JOIN areas d ON up.parent_id = d.id AND d.type = 'district'
            WHERE w.type = 'ward' AND w.is_active = 1
            ORDER BY w.id
            LIMIT 10
        ");
        $wards = $stmt->fetchAll();
        
        if (empty($wards)) {
            echo "<div class='alert alert-warning'>No wards found. Please create some wards first.</div>";
        } else {
            echo "<div style='background: #e7f3ff; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
            echo "<h4>Ward Codes and Sample Voter IDs:</h4>";
            echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
            echo "<tr style='background: #f8f9fa;'>";
            echo "<th style='padding: 10px;'>Ward</th>";
            echo "<th style='padding: 10px;'>Hierarchy</th>";
            echo "<th style='padding: 10px;'>Ward Code</th>";
            echo "<th style='padding: 10px;'>Sample Voter IDs</th>";
            echo "</tr>";
            
            foreach ($wards as $ward) {
                $ward_code = getWardCode($db, $ward['id']);
                
                // Generate sample voter IDs
                $sample_ids = [];
                for ($i = 0; $i < 3; $i++) {
                    $sample_ids[] = generateVoterIDWithArea($db, $ward['id'], 'V{WARD_CODE}{YYYY}{RRRRR}');
                }
                
                echo "<tr>";
                echo "<td style='padding: 10px;'>";
                echo "<strong>" . htmlspecialchars($ward['name']) . "</strong><br>";
                if ($ward['name_bn']) {
                    echo "<small>" . htmlspecialchars($ward['name_bn']) . "</small>";
                }
                echo "</td>";
                
                echo "<td style='padding: 10px;'>";
                $hierarchy = [];
                if ($ward['district_name']) $hierarchy[] = $ward['district_name'];
                if ($ward['upazila_name']) $hierarchy[] = $ward['upazila_name'];
                if ($ward['union_name']) $hierarchy[] = $ward['union_name'];
                echo "<small>" . implode(' → ', $hierarchy) . "</small>";
                echo "</td>";
                
                echo "<td style='padding: 10px;'>";
                echo "<code style='font-size: 1.2em; font-weight: bold; color: #0066cc;'>$ward_code</code>";
                echo "</td>";
                
                echo "<td style='padding: 10px;'>";
                foreach ($sample_ids as $id) {
                    echo "<code>$id</code><br>";
                }
                echo "</td>";
                
                echo "</tr>";
            }
            
            echo "</table>";
            echo "</div>";
            
            // Show pattern analysis
            echo "<div style='background: #d4edda; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
            echo "<h4>📊 Pattern Analysis:</h4>";
            
            $ward_groups = [];
            foreach ($wards as $ward) {
                $ward_code = getWardCode($db, $ward['id']);
                if (!isset($ward_groups[$ward_code])) {
                    $ward_groups[$ward_code] = [];
                }
                $ward_groups[$ward_code][] = $ward['name'];
            }
            
            echo "<ul>";
            foreach ($ward_groups as $code => $ward_names) {
                echo "<li><strong>Code '$code':</strong> " . implode(', ', $ward_names) . " (" . count($ward_names) . " ward" . (count($ward_names) > 1 ? 's' : '') . ")</li>";
            }
            echo "</ul>";
            
            echo "<p><strong>✅ Same ward = Same code:</strong> All voters from the same ward will have IDs starting with the same code.</p>";
            echo "</div>";
        }
        
    } catch (Exception $e) {
        echo "<div style='background: #f8d7da; padding: 15px; border-radius: 5px;'>";
        echo "<h4>❌ Error: " . $e->getMessage() . "</h4>";
        echo "</div>";
    }
}

// Show current ward codes
try {
    $db = getDB();
    
    echo "<h3>📊 Current Ward Codes:</h3>";
    
    $stmt = $db->query("
        SELECT id, name, name_bn, code 
        FROM areas 
        WHERE type = 'ward' AND is_active = 1 
        ORDER BY id 
        LIMIT 20
    ");
    $wards = $stmt->fetchAll();
    
    if (!empty($wards)) {
        echo "<div style='background: #f8f9fa; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
        echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
        echo "<tr style='background: #e9ecef;'>";
        echo "<th style='padding: 10px;'>Ward ID</th>";
        echo "<th style='padding: 10px;'>Ward Name</th>";
        echo "<th style='padding: 10px;'>Current Code</th>";
        echo "<th style='padding: 10px;'>Generated Code</th>";
        echo "<th style='padding: 10px;'>Status</th>";
        echo "</tr>";
        
        foreach ($wards as $ward) {
            $current_code = $ward['code'] ?: 'None';
            $generated_code = getWardCode($db, $ward['id']);
            $status = $ward['code'] ? '✅ Stored' : '🔄 Generated';
            
            echo "<tr>";
            echo "<td style='padding: 10px;'>" . $ward['id'] . "</td>";
            echo "<td style='padding: 10px;'>" . htmlspecialchars($ward['name']) . "</td>";
            echo "<td style='padding: 10px;'><code>$current_code</code></td>";
            echo "<td style='padding: 10px;'><code style='font-weight: bold;'>$generated_code</code></td>";
            echo "<td style='padding: 10px;'>$status</td>";
            echo "</tr>";
        }
        
        echo "</table>";
        echo "</div>";
    }
    
} catch (Exception $e) {
    echo "<div style='background: #f8d7da; padding: 15px; border-radius: 5px;'>";
    echo "<h4>❌ Database Error: " . $e->getMessage() . "</h4>";
    echo "</div>";
}
?>

<!DOCTYPE html>
<html>
<head>
    <title>Ward Code Demo</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body>

<div class="container mt-4">
    <div class="row">
        <div class="col-md-8">
            <div class="card">
                <div class="card-header">
                    <h5>🧪 Demo Ward-Based Voter IDs</h5>
                </div>
                <div class="card-body">
                    <p>This demo shows how voter IDs are generated with consistent ward codes - all voters from the same ward get IDs with the same prefix code.</p>
                    
                    <form method="POST">
                        <button type="submit" name="demo_ward_codes" class="btn btn-primary">
                            <i class="fas fa-play"></i> Generate Ward Code Demo
                        </button>
                    </form>
                    
                    <hr>
                    
                    <h6>Ward Code Generation Rules:</h6>
                    <ul>
                        <li><strong>District:</strong> First 2 letters (e.g., Dhaka → DH)</li>
                        <li><strong>Upazila:</strong> First 1 letter (e.g., Dhanmondi → D)</li>
                        <li><strong>Ward ID:</strong> 2-digit number (e.g., Ward 1 → 01)</li>
                        <li><strong>Result:</strong> DH + D + 01 = DHD01</li>
                    </ul>
                </div>
            </div>
        </div>
        
        <div class="col-md-4">
            <div class="card">
                <div class="card-header">
                    <h5>🔧 Quick Actions</h5>
                </div>
                <div class="card-body">
                    <a href="voter-id-generator.php" class="btn btn-info w-100 mb-2">
                        <i class="fas fa-magic"></i> ID Generator Tool
                    </a>
                    <a href="voters.php" class="btn btn-secondary w-100 mb-2">
                        <i class="fas fa-users"></i> Back to Voters
                    </a>
                    <a href="areas.php" class="btn btn-success w-100">
                        <i class="fas fa-map"></i> Manage Areas
                    </a>
                </div>
            </div>
            
            <div class="card mt-3">
                <div class="card-header">
                    <h5>📋 Ward Code Benefits</h5>
                </div>
                <div class="card-body">
                    <h6>✅ Advantages:</h6>
                    <ul>
                        <li>Easy ward identification</li>
                        <li>Consistent grouping</li>
                        <li>Geographic organization</li>
                        <li>Quick sorting/filtering</li>
                    </ul>
                    
                    <h6>🎯 Examples:</h6>
                    <ul>
                        <li><code>VDH0120241234</code> - Dhaka Ward 1</li>
                        <li><code>VDH0120241567</code> - Dhaka Ward 1</li>
                        <li><code>VCH0220241890</code> - Chittagong Ward 2</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
</div>

</body>
</html>
