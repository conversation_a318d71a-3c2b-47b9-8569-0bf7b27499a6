/**
 * Custom Styles for Dynamic Realtime Online Voting System
 */

/* Root Variables */
:root {
    --primary-color: #0d6efd;
    --secondary-color: #6c757d;
    --success-color: #198754;
    --danger-color: #dc3545;
    --warning-color: #ffc107;
    --info-color: #0dcaf0;
    --light-color: #f8f9fa;
    --dark-color: #212529;
    --border-radius: 0.375rem;
    --box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    --transition: all 0.15s ease-in-out;
}

/* Global Styles */
body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    line-height: 1.6;
    color: var(--dark-color);
}

/* Custom Button Styles */
.btn {
    border-radius: var(--border-radius);
    transition: var(--transition);
    font-weight: 500;
}

.btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 0.25rem 0.5rem rgba(0, 0, 0, 0.15);
}

/* Card Enhancements */
.card {
    border: none;
    box-shadow: var(--box-shadow);
    transition: var(--transition);
    border-radius: var(--border-radius);
}

.card:hover {
    box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
    transform: translateY(-2px);
}

/* Hero Section */
.hero-section {
    background: linear-gradient(135deg, var(--primary-color) 0%, #0056b3 100%);
    position: relative;
    overflow: hidden;
}

.hero-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="50" cy="50" r="1" fill="white" opacity="0.1"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
    opacity: 0.1;
}

.hero-section .container {
    position: relative;
    z-index: 1;
}

/* Feature Icons */
.feature-icon {
    font-size: 2rem;
    margin-bottom: 1rem;
}

/* Poll Cards */
.poll-card {
    transition: var(--transition);
    border-left: 4px solid var(--primary-color);
}

.poll-card:hover {
    border-left-color: var(--success-color);
}

/* Vote Options */
.vote-option {
    border: 2px solid #e9ecef;
    border-radius: var(--border-radius);
    padding: 1rem;
    margin-bottom: 0.5rem;
    cursor: pointer;
    transition: var(--transition);
    background: white;
}

.vote-option:hover {
    border-color: var(--primary-color);
    background-color: rgba(13, 110, 253, 0.05);
}

.vote-option.selected {
    border-color: var(--primary-color);
    background-color: rgba(13, 110, 253, 0.1);
}

.vote-option input[type="radio"],
.vote-option input[type="checkbox"] {
    margin-right: 0.75rem;
    transform: scale(1.2);
}

/* Results Visualization */
.result-bar {
    height: 30px;
    background: linear-gradient(90deg, var(--primary-color) 0%, var(--info-color) 100%);
    border-radius: var(--border-radius);
    position: relative;
    overflow: hidden;
}

.result-bar::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(90deg, transparent 0%, rgba(255, 255, 255, 0.2) 50%, transparent 100%);
    animation: shimmer 2s infinite;
}

@keyframes shimmer {
    0% { transform: translateX(-100%); }
    100% { transform: translateX(100%); }
}

.result-percentage {
    position: absolute;
    top: 50%;
    left: 10px;
    transform: translateY(-50%);
    color: white;
    font-weight: bold;
    font-size: 0.875rem;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
}

/* Real-time Updates */
.real-time-indicator {
    display: inline-block;
    width: 8px;
    height: 8px;
    background: var(--success-color);
    border-radius: 50%;
    animation: pulse 2s infinite;
    margin-right: 0.5rem;
}

@keyframes pulse {
    0% { opacity: 1; }
    50% { opacity: 0.5; }
    100% { opacity: 1; }
}

/* Loading States */
.loading {
    opacity: 0.6;
    pointer-events: none;
    position: relative;
}

.loading::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 20px;
    height: 20px;
    margin: -10px 0 0 -10px;
    border: 2px solid var(--primary-color);
    border-top-color: transparent;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    to { transform: rotate(360deg); }
}

/* Form Enhancements */
.form-control:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.2rem rgba(13, 110, 253, 0.25);
}

.form-floating > label {
    color: var(--secondary-color);
}

/* Alert Enhancements */
.alert {
    border: none;
    border-radius: var(--border-radius);
    border-left: 4px solid;
}

.alert-success { border-left-color: var(--success-color); }
.alert-danger { border-left-color: var(--danger-color); }
.alert-warning { border-left-color: var(--warning-color); }
.alert-info { border-left-color: var(--info-color); }

/* Navigation Enhancements */
.navbar-brand {
    font-size: 1.5rem;
}

.navbar-nav .nav-link {
    font-weight: 500;
    transition: var(--transition);
}

.navbar-nav .nav-link:hover {
    color: rgba(255, 255, 255, 0.8) !important;
}

/* Footer */
footer {
    margin-top: auto;
}

.social-links a {
    font-size: 1.25rem;
    transition: var(--transition);
}

.social-links a:hover {
    color: var(--primary-color) !important;
}

/* Responsive Design */
@media (max-width: 768px) {
    .hero-section {
        padding: 3rem 0 !important;
    }
    
    .hero-section .display-4 {
        font-size: 2rem;
    }
    
    .feature-icon {
        width: 60px !important;
        height: 60px !important;
        font-size: 1.5rem;
    }
}

/* Dark Mode Support */
@media (prefers-color-scheme: dark) {
    :root {
        --dark-color: #f8f9fa;
        --light-color: #212529;
    }
    
    body {
        background-color: #1a1a1a;
        color: var(--dark-color);
    }
    
    .card {
        background-color: #2d2d2d;
        color: var(--dark-color);
    }
    
    .vote-option {
        background-color: #2d2d2d;
        border-color: #495057;
        color: var(--dark-color);
    }
}

/* Utility Classes */
.text-gradient {
    background: linear-gradient(135deg, var(--primary-color), var(--info-color));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.shadow-soft {
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075) !important;
}

.border-start-primary {
    border-left: 4px solid var(--primary-color) !important;
}

.bg-gradient-primary {
    background: linear-gradient(135deg, var(--primary-color) 0%, #0056b3 100%) !important;
}
