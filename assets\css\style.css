/**
 * Custom Styles for Dynamic Realtime Online Voting System
 */

/* Root Variables */
:root {
    --primary-color: #0d6efd;
    --secondary-color: #6c757d;
    --success-color: #198754;
    --danger-color: #dc3545;
    --warning-color: #ffc107;
    --info-color: #0dcaf0;
    --light-color: #f8f9fa;
    --dark-color: #212529;
    --border-radius: 0.375rem;
    --box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    --transition: all 0.15s ease-in-out;
}

/* Global Styles */
body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    line-height: 1.6;
    color: var(--dark-color);
}

/* Custom Button Styles */
.btn {
    border-radius: var(--border-radius);
    transition: var(--transition);
    font-weight: 500;
}

.btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 0.25rem 0.5rem rgba(0, 0, 0, 0.15);
}

/* Card Enhancements */
.card {
    border: none;
    box-shadow: var(--box-shadow);
    transition: var(--transition);
    border-radius: var(--border-radius);
}

.card:hover {
    box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
    transform: translateY(-2px);
}

/* Hero Section */
.hero-section {
    background: linear-gradient(135deg, var(--primary-color) 0%, #0056b3 100%);
    position: relative;
    overflow: hidden;
}

.hero-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="50" cy="50" r="1" fill="white" opacity="0.1"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
    opacity: 0.1;
}

.hero-section .container {
    position: relative;
    z-index: 1;
}

/* Feature Icons */
.feature-icon {
    font-size: 2rem;
    margin-bottom: 1rem;
}

/* Poll Cards */
.poll-card {
    transition: var(--transition);
    border-left: 4px solid var(--primary-color);
}

.poll-card:hover {
    border-left-color: var(--success-color);
}

/* Vote Options */
.vote-option {
    border: 2px solid #e9ecef;
    border-radius: var(--border-radius);
    padding: 1rem;
    margin-bottom: 0.5rem;
    cursor: pointer;
    transition: var(--transition);
    background: white;
}

.vote-option:hover {
    border-color: var(--primary-color);
    background-color: rgba(13, 110, 253, 0.05);
}

.vote-option.selected {
    border-color: var(--primary-color);
    background-color: rgba(13, 110, 253, 0.1);
}

.vote-option input[type="radio"],
.vote-option input[type="checkbox"] {
    margin-right: 0.75rem;
    transform: scale(1.2);
}

/* Results Visualization */
.result-bar {
    height: 30px;
    background: linear-gradient(90deg, var(--primary-color) 0%, var(--info-color) 100%);
    border-radius: var(--border-radius);
    position: relative;
    overflow: hidden;
}

.result-bar::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(90deg, transparent 0%, rgba(255, 255, 255, 0.2) 50%, transparent 100%);
    animation: shimmer 2s infinite;
}

@keyframes shimmer {
    0% { transform: translateX(-100%); }
    100% { transform: translateX(100%); }
}

.result-percentage {
    position: absolute;
    top: 50%;
    left: 10px;
    transform: translateY(-50%);
    color: white;
    font-weight: bold;
    font-size: 0.875rem;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
}

/* Real-time Updates */
.real-time-indicator {
    display: inline-block;
    width: 8px;
    height: 8px;
    background: var(--success-color);
    border-radius: 50%;
    animation: pulse 2s infinite;
    margin-right: 0.5rem;
}

@keyframes pulse {
    0% { opacity: 1; }
    50% { opacity: 0.5; }
    100% { opacity: 1; }
}

/* Loading States */
.loading {
    opacity: 0.6;
    pointer-events: none;
    position: relative;
}

.loading::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 20px;
    height: 20px;
    margin: -10px 0 0 -10px;
    border: 2px solid var(--primary-color);
    border-top-color: transparent;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    to { transform: rotate(360deg); }
}

/* Form Enhancements */
.form-control:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.2rem rgba(13, 110, 253, 0.25);
}

.form-floating > label {
    color: var(--secondary-color);
}

/* Alert Enhancements */
.alert {
    border: none;
    border-radius: var(--border-radius);
    border-left: 4px solid;
}

.alert-success { border-left-color: var(--success-color); }
.alert-danger { border-left-color: var(--danger-color); }
.alert-warning { border-left-color: var(--warning-color); }
.alert-info { border-left-color: var(--info-color); }

/* Navigation Enhancements */
.navbar-brand {
    font-size: 1.5rem;
}

.navbar-nav .nav-link {
    font-weight: 500;
    transition: var(--transition);
}

.navbar-nav .nav-link:hover {
    color: rgba(255, 255, 255, 0.8) !important;
}

/* Footer */
footer {
    margin-top: auto;
}

.social-links a {
    font-size: 1.25rem;
    transition: var(--transition);
}

.social-links a:hover {
    color: var(--primary-color) !important;
}

/* Responsive Design */
@media (max-width: 768px) {
    .hero-section {
        padding: 3rem 0 !important;
    }
    
    .hero-section .display-4 {
        font-size: 2rem;
    }
    
    .feature-icon {
        width: 60px !important;
        height: 60px !important;
        font-size: 1.5rem;
    }
}

/* Dark Mode Support */
@media (prefers-color-scheme: dark) {
    :root {
        --dark-color: #f8f9fa;
        --light-color: #212529;
    }
    
    body {
        background-color: #1a1a1a;
        color: var(--dark-color);
    }
    
    .card {
        background-color: #2d2d2d;
        color: var(--dark-color);
    }
    
    .vote-option {
        background-color: #2d2d2d;
        border-color: #495057;
        color: var(--dark-color);
    }
}

/* Utility Classes */
.text-gradient {
    background: linear-gradient(135deg, var(--primary-color), var(--info-color));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.shadow-soft {
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075) !important;
}

.border-start-primary {
    border-left: 4px solid var(--primary-color) !important;
}

.bg-gradient-primary {
    background: linear-gradient(135deg, var(--primary-color) 0%, #0056b3 100%) !important;
}

/* Modern Sidebar Styles */
.modern-sidebar {
    width: 300px;
    height: 100vh;
    background: linear-gradient(180deg, #667eea 0%, #764ba2 50%, #f093fb 100%);
    color: white;
    z-index: 1050;
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    overflow-y: auto;
    overflow-x: hidden;
    box-shadow: 10px 0 30px rgba(0, 0, 0, 0.3);
    position: fixed;
    top: 0;
    left: 0;
    border-radius: 0 25px 25px 0;
    margin: 10px 0 10px 10px;
    display: flex;
    flex-direction: column;
}

/* Mobile: Hide sidebar by default */
@media (max-width: 991.98px) {
    .modern-sidebar {
        position: fixed;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100vh;
        margin: 0;
        border-radius: 0;
        z-index: 1060;
    }

    .modern-sidebar.active {
        left: 0;
    }

    .main-content {
        margin-left: 0 !important;
        width: 100% !important;
    }

    .sidebar-layout .main-content {
        margin-left: 0;
        margin: 0;
        border-radius: 0;
    }

    .sidebar-layout .page-content {
        margin: 0;
        border-radius: 0;
    }
}

.sidebar-header {
    padding: 2rem 1.5rem;
    background: linear-gradient(135deg, #ff6b6b, #feca57, #48dbfb, #ff9ff3);
    background-size: 400% 400%;
    animation: gradientShift 8s ease infinite;
    border-radius: 0 20px 20px 0;
    margin: 10px 10px 20px 0;
    display: flex;
    justify-content: space-between;
    align-items: center;
    box-shadow: 0 5px 20px rgba(0, 0, 0, 0.3);
}

@keyframes gradientShift {
    0% { background-position: 0% 50%; }
    50% { background-position: 100% 50%; }
    100% { background-position: 0% 50%; }
}

.sidebar-brand {
    display: flex;
    align-items: center;
    font-size: 1.8rem;
    font-weight: 900;
    color: white;
    text-decoration: none;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
}

.sidebar-brand i {
    margin-right: 0.75rem;
    font-size: 2rem;
    background: linear-gradient(45deg, #ffd700, #ff6b6b, #4ecdc4);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    animation: iconPulse 2s ease-in-out infinite;
}

@keyframes iconPulse {
    0%, 100% { transform: scale(1); }
    50% { transform: scale(1.1); }
}

.brand-text {
    background: linear-gradient(45deg, #ffffff, #f8f9fa, #e9ecef);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    font-family: 'Arial Black', sans-serif;
}

.sidebar-toggle {
    background: none;
    border: none;
    color: white;
    font-size: 1.2rem;
    cursor: pointer;
    padding: 0.5rem;
    border-radius: 50%;
    transition: background-color 0.3s ease;
}

.sidebar-toggle:hover {
    background-color: rgba(255, 255, 255, 0.1);
}

.sidebar-user {
    padding: 1.5rem;
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.1), rgba(255, 255, 255, 0.05));
    border-radius: 15px;
    margin: 0 15px 20px 15px;
    display: flex;
    align-items: center;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2);
    transition: all 0.3s ease;
}

.sidebar-user:hover {
    transform: translateY(-2px);
    box-shadow: 0 12px 35px rgba(0, 0, 0, 0.3);
}

.user-avatar {
    width: 60px;
    height: 60px;
    background: linear-gradient(45deg, #ff6b6b, #feca57, #48dbfb, #ff9ff3);
    background-size: 400% 400%;
    animation: gradientShift 6s ease infinite;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 1rem;
    font-size: 1.5rem;
    border: 3px solid rgba(255, 255, 255, 0.3);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
}

.user-info {
    flex: 1;
}

.user-name {
    font-weight: 700;
    margin-bottom: 0.25rem;
    font-size: 1.1rem;
    text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.3);
}

.user-role {
    font-size: 0.875rem;
    opacity: 0.9;
    text-transform: capitalize;
    background: linear-gradient(45deg, #ffd700, #ff6b6b);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    font-weight: 600;
}

.sidebar-nav {
    padding: 1rem 0;
    flex: 1;
    overflow-y: auto;
    overflow-x: hidden;
    max-height: calc(100vh - 200px);
}

.nav-list {
    list-style: none;
    padding: 0;
    margin: 0;
}

.nav-divider {
    margin: 1.5rem 0 1rem 0;
    padding: 0 1.5rem;
    position: relative;
}

.nav-divider::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 1.5rem;
    right: 1.5rem;
    height: 2px;
    background: linear-gradient(90deg, transparent, #ff6b6b, #feca57, #48dbfb, transparent);
    border-radius: 2px;
}

.divider-text {
    font-size: 0.8rem;
    text-transform: uppercase;
    letter-spacing: 2px;
    font-weight: 700;
    background: linear-gradient(45deg, #ffd700, #ff6b6b, #4ecdc4);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    text-align: center;
    padding: 0.5rem 1rem;
    background-color: rgba(255, 255, 255, 0.1);
    border-radius: 20px;
    backdrop-filter: blur(5px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.3);
}

.nav-item {
    margin: 0.25rem 0;
}

.nav-link {
    display: flex;
    align-items: center;
    padding: 1rem 1.5rem;
    color: rgba(255, 255, 255, 0.9);
    text-decoration: none;
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    border-radius: 0 30px 30px 0;
    margin: 5px 15px 5px 0;
    position: relative;
    overflow: hidden;
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.1), rgba(255, 255, 255, 0.05));
    backdrop-filter: blur(5px);
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.nav-link::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
    transition: left 0.6s ease;
}

.nav-link:hover::before {
    left: 100%;
}

.nav-link:hover {
    color: white;
    background: linear-gradient(135deg, #ff6b6b, #feca57);
    transform: translateX(8px) scale(1.02);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
    border: 1px solid rgba(255, 255, 255, 0.3);
}

.nav-link.active {
    background: linear-gradient(135deg, #48dbfb, #0abde3, #006ba6);
    color: white;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.4);
    transform: translateX(5px);
    border: 2px solid rgba(255, 255, 255, 0.4);
}

.nav-link i {
    width: 25px;
    margin-right: 1rem;
    text-align: center;
    font-size: 1.2rem;
    background: linear-gradient(45deg, #ffd700, #ff6b6b, #4ecdc4, #45b7d1);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    transition: all 0.3s ease;
}

.nav-link:hover i {
    background: linear-gradient(45deg, #ffffff, #f8f9fa);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    transform: scale(1.2) rotate(5deg);
}

.nav-link.active i {
    background: linear-gradient(45deg, #ffffff, #e3f2fd);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    transform: scale(1.1);
}

.nav-text {
    font-weight: 600;
    font-size: 0.95rem;
    text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.2);
}

.sidebar-footer {
    padding: 1.5rem;
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.1), rgba(255, 255, 255, 0.05));
    border-radius: 15px;
    margin: 20px 15px 15px 15px;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.logout-btn {
    display: flex;
    align-items: center;
    padding: 1rem;
    color: rgba(255, 255, 255, 0.9);
    text-decoration: none;
    border-radius: 12px;
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    margin-bottom: 1rem;
    background: linear-gradient(135deg, rgba(220, 53, 69, 0.2), rgba(255, 107, 107, 0.1));
    border: 1px solid rgba(255, 107, 107, 0.3);
    backdrop-filter: blur(5px);
}

.logout-btn:hover {
    background: linear-gradient(135deg, #ff6b6b, #ee5a52);
    color: white;
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(255, 107, 107, 0.4);
}

.logout-btn i {
    margin-right: 0.75rem;
    font-size: 1.1rem;
}

.app-version {
    text-align: center;
    font-size: 0.8rem;
    background: linear-gradient(45deg, #ffd700, #ff6b6b, #4ecdc4);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    font-weight: 600;
    padding: 0.5rem;
    border-radius: 8px;
    background-color: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(5px);
}

.sidebar-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    z-index: 1040;
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease;
}

.sidebar-overlay.active {
    opacity: 1;
    visibility: visible;
}

.mobile-sidebar-toggle {
    position: fixed;
    top: 20px;
    left: 20px;
    z-index: 1030;
    background: linear-gradient(135deg, #ff6b6b, #feca57, #48dbfb);
    background-size: 200% 200%;
    animation: gradientShift 3s ease infinite;
    color: white;
    border: none;
    width: 60px;
    height: 60px;
    border-radius: 50%;
    font-size: 1.4rem;
    cursor: pointer;
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    border: 3px solid rgba(255, 255, 255, 0.3);
    backdrop-filter: blur(10px);
}

.mobile-sidebar-toggle:hover {
    transform: scale(1.15) rotate(90deg);
    box-shadow: 0 12px 35px rgba(0, 0, 0, 0.4);
    border: 3px solid rgba(255, 255, 255, 0.6);
}

.mobile-sidebar-toggle:active {
    transform: scale(0.95);
}

/* Main content adjustment for sidebar */
.main-content {
    margin-left: 320px;
    transition: margin-left 0.3s ease-in-out;
    min-height: 100vh;
    width: calc(100% - 320px);
}

/* Mobile: Full width content */
@media (max-width: 991.98px) {
    .main-content {
        margin-left: 0;
    }

    .main-content.sidebar-active {
        margin-left: 0;
    }
}

/* Mobile Responsive Design */
@media (max-width: 768px) {
    .modern-sidebar {
        width: 100%;
        left: -100%;
    }

    .modern-sidebar.active {
        left: 0;
    }
}

/* Desktop: Show mobile toggle only on smaller screens */
@media (min-width: 992px) {
    .mobile-sidebar-toggle {
        display: none;
    }

    .sidebar-overlay {
        display: none;
    }
}

/* Sidebar Layout Specific Styles */
.sidebar-layout {
    min-height: 100vh;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    position: relative;
}

.sidebar-layout .main-content {
    display: flex;
    flex-direction: column;
    min-height: 100vh;
    background: linear-gradient(135deg, #f093fb 0%, #f5576c 4%, #4facfe 100%);
    position: relative;
}

.sidebar-layout .page-content {
    flex: 1;
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border-radius: 20px 0 0 0;
    margin: 10px 10px 0 0;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
}

.sidebar-layout .flash-messages {
    position: fixed;
    top: 20px;
    right: 20px;
    z-index: 1060;
    max-width: 400px;
}

.top-navbar {
    background: white;
    border-bottom: 1px solid #e9ecef;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.sidebar-footer {
    margin-top: auto;
}

/* Enhanced Navigation Badges */
.nav-link .badge {
    font-size: 0.7rem;
    padding: 0.25em 0.5em;
    border-radius: 10px;
}

/* Smooth Animations */
.nav-link, .card, .btn {
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Hover Effects */
.card:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0,0,0,0.15);
}

/* Loading States */
.sidebar-nav.loading {
    opacity: 0.6;
    pointer-events: none;
}

/* Custom Scrollbar for Sidebar */
.modern-sidebar::-webkit-scrollbar,
.sidebar-nav::-webkit-scrollbar {
    width: 8px;
}

.modern-sidebar::-webkit-scrollbar-track,
.sidebar-nav::-webkit-scrollbar-track {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 10px;
    margin: 10px 0;
}

.modern-sidebar::-webkit-scrollbar-thumb,
.sidebar-nav::-webkit-scrollbar-thumb {
    background: linear-gradient(180deg, #ff6b6b, #feca57, #48dbfb);
    border-radius: 10px;
    border: 2px solid rgba(255, 255, 255, 0.1);
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.3);
}

.modern-sidebar::-webkit-scrollbar-thumb:hover,
.sidebar-nav::-webkit-scrollbar-thumb:hover {
    background: linear-gradient(180deg, #feca57, #ff6b6b, #48dbfb);
    border: 2px solid rgba(255, 255, 255, 0.3);
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.4);
}

.modern-sidebar::-webkit-scrollbar-thumb:active,
.sidebar-nav::-webkit-scrollbar-thumb:active {
    background: linear-gradient(180deg, #48dbfb, #ff6b6b, #feca57);
}

/* Firefox Scrollbar */
.modern-sidebar,
.sidebar-nav {
    scrollbar-width: thin;
    scrollbar-color: rgba(255, 107, 107, 0.7) rgba(255, 255, 255, 0.1);
}

/* Responsive Adjustments */
@media (max-width: 576px) {
    .sidebar-layout .flash-messages {
        position: relative;
        top: auto;
        right: auto;
        max-width: none;
        margin: 1rem;
    }

    .mobile-sidebar-toggle {
        top: 15px;
        left: 15px;
        width: 45px;
        height: 45px;
        font-size: 1.1rem;
    }
}

/* Print Styles */
@media print {
    .modern-sidebar,
    .mobile-sidebar-toggle,
    .sidebar-overlay,
    .top-navbar {
        display: none !important;
    }

    .main-content {
        margin-left: 0 !important;
    }
}
