<?php
/**
 * Sample Voters Creation Script
 * Quick way to create test voters
 */

require_once 'config/config.php';

// Check if user is admin
if (!isLoggedIn() || !isAdmin()) {
    echo "<h2>Access Denied</h2>";
    echo "<p>Please login as admin first: <a href='auth/login.php'>Login</a></p>";
    exit;
}

echo "<h2>Sample Voters Creation</h2>";

try {
    $db = getDB();
    
    // Check if areas exist
    $stmt = $db->query("SELECT COUNT(*) as count FROM areas WHERE is_active = 1");
    $area_count = $stmt->fetch()['count'];
    
    if ($area_count == 0) {
        echo "<div style='background: #f8d7da; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
        echo "<h4>No Areas Found!</h4>";
        echo "<p>Please create areas first: <a href='admin/areas.php'>Area Management</a></p>";
        echo "</div>";
        exit;
    }
    
    // Get available areas
    $stmt = $db->query("SELECT id, name, name_bn, code FROM areas WHERE is_active = 1 ORDER BY id LIMIT 5");
    $areas = $stmt->fetchAll();
    
    echo "<h3>Available Areas:</h3>";
    foreach ($areas as $area) {
        echo "<li>{$area['name_bn']} ({$area['code']})</li>";
    }
    
    // Sample voters data
    $sample_voters = [
        [
            'name' => 'Ahmed Ali',
            'name_bn' => 'আহমেদ আলী',
            'nid' => '1234567890123',
            'mobile' => '01712345678',
            'address' => 'ধানমন্ডি, ঢাকা'
        ],
        [
            'name' => 'Fatema Khatun',
            'name_bn' => 'ফাতেমা খাতুন',
            'nid' => '1234567890124',
            'mobile' => '01812345678',
            'address' => 'ধানমন্ডি, ঢাকা'
        ],
        [
            'name' => 'Mohammad Rahim',
            'name_bn' => 'মোহাম্মদ রহিম',
            'nid' => '1234567890125',
            'mobile' => '01912345678',
            'address' => 'গুলশান, ঢাকা'
        ],
        [
            'name' => 'Rashida Begum',
            'name_bn' => 'রাশিদা বেগম',
            'nid' => '1234567890126',
            'mobile' => '01612345678',
            'address' => 'গুলশান, ঢাকা'
        ],
        [
            'name' => 'Karim Uddin',
            'name_bn' => 'করিম উদ্দিন',
            'nid' => '1234567890127',
            'mobile' => '01512345678',
            'address' => 'বনানী, ঢাকা'
        ],
        [
            'name' => 'Salma Akter',
            'name_bn' => 'সালমা আক্তার',
            'nid' => '1234567890128',
            'mobile' => '01412345678',
            'address' => 'বনানী, ঢাকা'
        ],
        [
            'name' => 'Abdul Kader',
            'name_bn' => 'আব্দুল কাদের',
            'nid' => '1234567890129',
            'mobile' => '01312345678',
            'address' => 'মিরপুর, ঢাকা'
        ],
        [
            'name' => 'Nasira Khatun',
            'name_bn' => 'নাসিরা খাতুন',
            'nid' => '1234567890130',
            'mobile' => '01212345678',
            'address' => 'মিরপুর, ঢাকা'
        ]
    ];
    
    if (isset($_POST['create_voters'])) {
        echo "<h3>Creating Sample Voters...</h3>";
        
        $success_count = 0;
        $error_count = 0;
        $errors = [];
        
        foreach ($sample_voters as $index => $voter_data) {
            try {
                // Generate voter ID
                $stmt = $db->query("SELECT COUNT(*) + 1 as next_id FROM voters");
                $next_id = $stmt->fetch()['next_id'];
                $voter_id = 'V' . str_pad($next_id, 3, '0', STR_PAD_LEFT);
                
                // Assign area (cycle through available areas)
                $area = $areas[$index % count($areas)];
                
                // Check if voter already exists
                $stmt = $db->prepare("SELECT id FROM voters WHERE nid = ? OR mobile = ?");
                $stmt->execute([$voter_data['nid'], $voter_data['mobile']]);
                if ($stmt->fetch()) {
                    $errors[] = "Voter {$voter_data['name']} already exists (NID: {$voter_data['nid']})";
                    $error_count++;
                    continue;
                }
                
                // Insert voter
                $stmt = $db->prepare("
                    INSERT INTO voters (voter_id, name, name_bn, nid, mobile, area_id, address, created_by) 
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?)
                ");
                
                $stmt->execute([
                    $voter_id,
                    $voter_data['name'],
                    $voter_data['name_bn'],
                    $voter_data['nid'],
                    $voter_data['mobile'],
                    $area['id'],
                    $voter_data['address'],
                    getCurrentUser()['id']
                ]);
                
                echo "<div style='background: #d4edda; padding: 10px; margin: 5px 0; border-radius: 3px;'>";
                echo "✓ Created: {$voter_data['name_bn']} ({$voter_id}) - {$area['name_bn']}";
                echo "</div>";
                
                $success_count++;
                
            } catch (Exception $e) {
                $errors[] = "Error creating {$voter_data['name']}: " . $e->getMessage();
                $error_count++;
            }
        }
        
        echo "<h3>Summary:</h3>";
        echo "<div style='background: #d4edda; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
        echo "<h4>✓ Successfully Created: $success_count voters</h4>";
        echo "</div>";
        
        if ($error_count > 0) {
            echo "<div style='background: #f8d7da; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
            echo "<h4>✗ Errors: $error_count</h4>";
            foreach ($errors as $error) {
                echo "<p>• $error</p>";
            }
            echo "</div>";
        }
        
        echo "<h3>Next Steps:</h3>";
        echo "<ol>";
        echo "<li><a href='admin/voters.php'>View All Voters</a></li>";
        echo "<li><a href='polls/create.php'>Create Area-Based Poll</a></li>";
        echo "<li><a href='admin/'>Admin Dashboard</a></li>";
        echo "</ol>";
        
    } else {
        // Show form
        echo "<h3>Create Sample Voters</h3>";
        echo "<p>This will create " . count($sample_voters) . " sample voters distributed across available areas.</p>";
        
        echo "<div style='background: #fff3cd; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
        echo "<h4>Sample Voters to be Created:</h4>";
        foreach ($sample_voters as $voter) {
            echo "<li>{$voter['name_bn']} ({$voter['name']}) - {$voter['mobile']}</li>";
        }
        echo "</div>";
        
        echo "<form method='POST'>";
        echo "<button type='submit' name='create_voters' style='background: #28a745; color: white; padding: 15px 30px; border: none; border-radius: 5px; font-size: 16px; cursor: pointer;'>";
        echo "Create Sample Voters";
        echo "</button>";
        echo "</form>";
    }
    
} catch (Exception $e) {
    echo "<div style='background: #f8d7da; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
    echo "<h4>Database Error!</h4>";
    echo "<p>Error: " . $e->getMessage() . "</p>";
    echo "</div>";
}

echo "<br><h3>Manual Options:</h3>";
echo "<ul>";
echo "<li><a href='admin/voters.php'>Voter Management (Add Individual Voters)</a></li>";
echo "<li><a href='admin/areas.php'>Area Management (Create Areas First)</a></li>";
echo "<li><a href='admin/'>Admin Dashboard</a></li>";
echo "</ul>";

echo "<br><p style='background: #e2e3e5; padding: 10px; border-radius: 5px;'>";
echo "<strong>Note:</strong> Delete this file after creating sample data for security.";
echo "</p>";
?>
