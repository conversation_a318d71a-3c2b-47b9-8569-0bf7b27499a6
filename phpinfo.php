<?php
/**
 * PHP Information & Extension Check
 * Check PHP configuration and available extensions
 */

echo "<h1>🔍 PHP Configuration Check</h1>";

// Check GD Extension
echo "<h2>📸 GD Extension Status:</h2>";
if (extension_loaded('gd')) {
    echo "<div style='background: #d4edda; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
    echo "<h3 style='color: #155724;'>✅ GD Extension is ENABLED</h3>";
    
    $gd_info = gd_info();
    echo "<ul>";
    echo "<li><strong>GD Version:</strong> " . $gd_info['GD Version'] . "</li>";
    echo "<li><strong>JPEG Support:</strong> " . ($gd_info['JPEG Support'] ? 'Yes' : 'No') . "</li>";
    echo "<li><strong>PNG Support:</strong> " . ($gd_info['PNG Support'] ? 'Yes' : 'No') . "</li>";
    echo "<li><strong>GIF Support:</strong> " . ($gd_info['GIF Read Support'] ? 'Yes' : 'No') . "</li>";
    echo "<li><strong>WebP Support:</strong> " . ($gd_info['WebP Support'] ?? false ? 'Yes' : 'No') . "</li>";
    echo "</ul>";
    echo "<p><strong>✅ Photo upload with resize will work perfectly!</strong></p>";
    echo "</div>";
} else {
    echo "<div style='background: #f8d7da; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
    echo "<h3 style='color: #721c24;'>❌ GD Extension is DISABLED</h3>";
    echo "<p>Photo upload will work but without auto-resize feature.</p>";
    echo "<h4>To Enable GD:</h4>";
    echo "<ol>";
    echo "<li>Open XAMPP Control Panel</li>";
    echo "<li>Click 'Config' next to Apache</li>";
    echo "<li>Select 'PHP (php.ini)'</li>";
    echo "<li>Find line: <code>;extension=gd</code></li>";
    echo "<li>Remove semicolon: <code>extension=gd</code></li>";
    echo "<li>Save file and restart Apache</li>";
    echo "</ol>";
    echo "</div>";
}

// Check other important extensions
echo "<h2>🔧 Other Important Extensions:</h2>";
$extensions = [
    'mysqli' => 'Database connection',
    'pdo' => 'Database PDO',
    'pdo_mysql' => 'MySQL PDO driver',
    'fileinfo' => 'File type detection',
    'mbstring' => 'Multi-byte string support',
    'openssl' => 'SSL/TLS support',
    'curl' => 'HTTP requests',
    'json' => 'JSON support',
    'session' => 'Session support'
];

echo "<table border='1' style='border-collapse: collapse; width: 100%; margin: 10px 0;'>";
echo "<tr style='background: #f8f9fa;'>";
echo "<th style='padding: 10px;'>Extension</th>";
echo "<th style='padding: 10px;'>Status</th>";
echo "<th style='padding: 10px;'>Purpose</th>";
echo "</tr>";

foreach ($extensions as $ext => $purpose) {
    $status = extension_loaded($ext);
    $status_text = $status ? '✅ Enabled' : '❌ Disabled';
    $bg_color = $status ? '#d4edda' : '#f8d7da';
    
    echo "<tr style='background: $bg_color;'>";
    echo "<td style='padding: 10px;'><strong>$ext</strong></td>";
    echo "<td style='padding: 10px;'>$status_text</td>";
    echo "<td style='padding: 10px;'>$purpose</td>";
    echo "</tr>";
}
echo "</table>";

// PHP Version and Settings
echo "<h2>⚙️ PHP Configuration:</h2>";
echo "<div style='background: #e7f3ff; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
echo "<ul>";
echo "<li><strong>PHP Version:</strong> " . PHP_VERSION . "</li>";
echo "<li><strong>Upload Max Filesize:</strong> " . ini_get('upload_max_filesize') . "</li>";
echo "<li><strong>Post Max Size:</strong> " . ini_get('post_max_size') . "</li>";
echo "<li><strong>Max Execution Time:</strong> " . ini_get('max_execution_time') . " seconds</li>";
echo "<li><strong>Memory Limit:</strong> " . ini_get('memory_limit') . "</li>";
echo "<li><strong>Error Reporting:</strong> " . ini_get('error_reporting') . "</li>";
echo "</ul>";
echo "</div>";

// Test Photo Upload Functionality
echo "<h2>🧪 Photo Upload Test:</h2>";
echo "<div style='background: #fff3cd; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
echo "<h4>Test Results:</h4>";

// Test upload directory
$upload_dir = __DIR__ . '/uploads/voters/';
if (file_exists($upload_dir)) {
    echo "<p>✅ Upload directory exists: <code>$upload_dir</code></p>";
    if (is_writable($upload_dir)) {
        echo "<p>✅ Upload directory is writable</p>";
    } else {
        echo "<p>❌ Upload directory is not writable</p>";
    }
} else {
    echo "<p>❌ Upload directory does not exist: <code>$upload_dir</code></p>";
}

// Test default avatar
$default_avatar = __DIR__ . '/assets/images/default-avatar.png';
if (file_exists($default_avatar)) {
    echo "<p>✅ Default avatar exists</p>";
} else {
    echo "<p>⚠️ Default avatar not found (will be created automatically)</p>";
}

echo "</div>";

// Quick Links
echo "<h2>🔗 Quick Links:</h2>";
echo "<ul>";
echo "<li><a href='setup-voter-photos.php'>Setup Voter Photos</a></li>";
echo "<li><a href='admin/voters.php'>Voter Management</a></li>";
echo "<li><a href='admin/'>Admin Dashboard</a></li>";
echo "<li><a href='index.php'>Home Page</a></li>";
echo "</ul>";

// Full PHP Info (collapsible)
echo "<h2>📋 Full PHP Info:</h2>";
echo "<details>";
echo "<summary style='cursor: pointer; background: #f8f9fa; padding: 10px; border-radius: 5px;'>Click to show full PHP configuration</summary>";
echo "<div style='margin-top: 10px; border: 1px solid #dee2e6; padding: 10px;'>";
phpinfo();
echo "</div>";
echo "</details>";

echo "<br><div style='background: #e2e3e5; padding: 10px; border-radius: 5px;'>";
echo "<strong>Security Note:</strong> Delete this file after checking configuration.";
echo "</div>";
?>
