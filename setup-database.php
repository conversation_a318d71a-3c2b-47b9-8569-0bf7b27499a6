<?php
/**
 * Database Setup Script
 * Creates missing tables and sample data
 */

// Display all errors
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h2>🗄️ Database Setup Script</h2>";

// Database settings
$db_host = 'localhost';
$db_name = 'ovs_db';
$db_user = 'root';
$db_pass = '';

try {
    $pdo = new PDO("mysql:host=$db_host;dbname=$db_name;charset=utf8mb4", $db_user, $db_pass);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "<div style='background: #d4edda; padding: 10px; border-radius: 5px; margin: 10px 0;'>";
    echo "✅ Database connected successfully!";
    echo "</div>";
    
    // Check existing tables
    $stmt = $pdo->query("SHOW TABLES");
    $existing_tables = $stmt->fetchAll(PDO::FETCH_COLUMN);
    
    echo "<h3>📋 Current Tables:</h3>";
    echo "<ul>";
    foreach ($existing_tables as $table) {
        echo "<li>✅ $table</li>";
    }
    echo "</ul>";
    
    $missing_tables = [];
    $required_tables = ['users', 'polls', 'poll_options', 'votes', 'areas', 'voters'];
    
    foreach ($required_tables as $table) {
        if (!in_array($table, $existing_tables)) {
            $missing_tables[] = $table;
        }
    }
    
    if (!empty($missing_tables)) {
        echo "<h3>❌ Missing Tables:</h3>";
        echo "<ul>";
        foreach ($missing_tables as $table) {
            echo "<li style='color: red;'>❌ $table</li>";
        }
        echo "</ul>";
    }
    
    if (isset($_POST['setup_database'])) {
        echo "<h3>🔧 Setting up database...</h3>";
        
        try {
            // Create areas table if missing
            if (in_array('areas', $missing_tables)) {
                $pdo->exec("
                    CREATE TABLE `areas` (
                      `id` int(11) NOT NULL AUTO_INCREMENT,
                      `name` varchar(100) NOT NULL,
                      `name_bn` varchar(100) DEFAULT NULL,
                      `type` enum('ward','area','district','division') NOT NULL DEFAULT 'ward',
                      `parent_id` int(11) DEFAULT NULL,
                      `code` varchar(20) DEFAULT NULL,
                      `description` text DEFAULT NULL,
                      `is_active` tinyint(1) NOT NULL DEFAULT 1,
                      `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
                      `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                      PRIMARY KEY (`id`),
                      UNIQUE KEY `code` (`code`),
                      KEY `parent_id` (`parent_id`),
                      KEY `idx_type` (`type`),
                      KEY `idx_active` (`is_active`)
                    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
                ");
                echo "<div style='background: #d4edda; padding: 10px; margin: 5px 0; border-radius: 3px;'>✅ Created areas table</div>";
            }
            
            // Create voters table if missing
            if (in_array('voters', $missing_tables)) {
                $pdo->exec("
                    CREATE TABLE `voters` (
                      `id` int(11) NOT NULL AUTO_INCREMENT,
                      `voter_id` varchar(50) NOT NULL,
                      `name` varchar(100) NOT NULL,
                      `name_bn` varchar(100) DEFAULT NULL,
                      `father_name` varchar(100) DEFAULT NULL,
                      `mother_name` varchar(100) DEFAULT NULL,
                      `nid` varchar(20) DEFAULT NULL,
                      `mobile` varchar(15) DEFAULT NULL,
                      `email` varchar(100) DEFAULT NULL,
                      `date_of_birth` date DEFAULT NULL,
                      `gender` enum('male','female','other') DEFAULT NULL,
                      `area_id` int(11) NOT NULL,
                      `address` text DEFAULT NULL,
                      `photo` varchar(255) DEFAULT NULL,
                      `is_active` tinyint(1) NOT NULL DEFAULT 1,
                      `user_id` int(11) DEFAULT NULL,
                      `created_by` int(11) DEFAULT NULL,
                      `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
                      `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                      PRIMARY KEY (`id`),
                      UNIQUE KEY `voter_id` (`voter_id`),
                      UNIQUE KEY `nid` (`nid`),
                      KEY `area_id` (`area_id`),
                      KEY `user_id` (`user_id`),
                      KEY `created_by` (`created_by`),
                      KEY `idx_active` (`is_active`)
                    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
                ");
                echo "<div style='background: #d4edda; padding: 10px; margin: 5px 0; border-radius: 3px;'>✅ Created voters table</div>";
            }
            
            // Update existing tables
            try {
                // Add columns to users table
                $pdo->exec("ALTER TABLE `users` ADD COLUMN `area_id` int(11) DEFAULT NULL");
                $pdo->exec("ALTER TABLE `users` ADD COLUMN `voter_id` varchar(50) DEFAULT NULL");
                echo "<div style='background: #d4edda; padding: 10px; margin: 5px 0; border-radius: 3px;'>✅ Updated users table</div>";
            } catch (Exception $e) {
                if (strpos($e->getMessage(), 'Duplicate column name') === false) {
                    echo "<div style='background: #fff3cd; padding: 10px; margin: 5px 0; border-radius: 3px;'>⚠️ Users table update: " . $e->getMessage() . "</div>";
                }
            }
            
            try {
                // Add columns to polls table
                $pdo->exec("ALTER TABLE `polls` ADD COLUMN `access_type` enum('public','area_based','mixed') NOT NULL DEFAULT 'public'");
                $pdo->exec("ALTER TABLE `polls` ADD COLUMN `allowed_areas` json DEFAULT NULL");
                $pdo->exec("ALTER TABLE `polls` ADD COLUMN `require_voter_verification` tinyint(1) NOT NULL DEFAULT 0");
                echo "<div style='background: #d4edda; padding: 10px; margin: 5px 0; border-radius: 3px;'>✅ Updated polls table</div>";
            } catch (Exception $e) {
                if (strpos($e->getMessage(), 'Duplicate column name') === false) {
                    echo "<div style='background: #fff3cd; padding: 10px; margin: 5px 0; border-radius: 3px;'>⚠️ Polls table update: " . $e->getMessage() . "</div>";
                }
            }
            
            try {
                // Add columns to votes table
                $pdo->exec("ALTER TABLE `votes` ADD COLUMN `voter_id` varchar(50) DEFAULT NULL");
                echo "<div style='background: #d4edda; padding: 10px; margin: 5px 0; border-radius: 3px;'>✅ Updated votes table</div>";
            } catch (Exception $e) {
                if (strpos($e->getMessage(), 'Duplicate column name') === false) {
                    echo "<div style='background: #fff3cd; padding: 10px; margin: 5px 0; border-radius: 3px;'>⚠️ Votes table update: " . $e->getMessage() . "</div>";
                }
            }
            
            // Insert sample areas
            $stmt = $pdo->query("SELECT COUNT(*) FROM areas");
            if ($stmt->fetchColumn() == 0) {
                $areas_data = [
                    ['ঢাকা বিভাগ', 'ঢাকা বিভাগ', 'division', 'DIV-01', 'ঢাকা বিভাগ'],
                    ['ঢাকা জেলা', 'ঢাকা জেলা', 'district', 'DIS-01', 'ঢাকা জেলা'],
                    ['ধানমন্ডি', 'ধানমন্ডি', 'area', 'AREA-01', 'ধানমন্ডি এলাকা'],
                    ['ওয়ার্ড নং ১', 'ওয়ার্ড নং ১', 'ward', 'WARD-01', 'ধানমন্ডি ওয়ার্ড নং ১'],
                    ['ওয়ার্ড নং ২', 'ওয়ার্ড নং ২', 'ward', 'WARD-02', 'ধানমন্ডি ওয়ার্ড নং ২'],
                    ['গুলশান', 'গুলশান', 'area', 'AREA-02', 'গুলশান এলাকা'],
                    ['ওয়ার্ড নং ৩', 'ওয়ার্ড নং ৩', 'ward', 'WARD-03', 'গুলশান ওয়ার্ড নং ৩'],
                    ['ওয়ার্ড নং ৪', 'ওয়ার্ড নং ৪', 'ward', 'WARD-04', 'গুলশান ওয়ার্ড নং ৪'],
                    ['বনানী', 'বনানী', 'area', 'AREA-03', 'বনানী এলাকা'],
                    ['ওয়ার্ড নং ৫', 'ওয়ার্ড নং ৫', 'ward', 'WARD-05', 'বনানী ওয়ার্ড নং ৫']
                ];
                
                $stmt = $pdo->prepare("INSERT INTO areas (name, name_bn, type, code, description) VALUES (?, ?, ?, ?, ?)");
                foreach ($areas_data as $area) {
                    $stmt->execute($area);
                }
                echo "<div style='background: #d4edda; padding: 10px; margin: 5px 0; border-radius: 3px;'>✅ Inserted sample areas data</div>";
            }
            
            echo "<div style='background: #d4edda; padding: 15px; border-radius: 5px; margin: 15px 0;'>";
            echo "<h4>🎉 Database Setup Complete!</h4>";
            echo "<p>All required tables have been created and sample data inserted.</p>";
            echo "</div>";
            
        } catch (Exception $e) {
            echo "<div style='background: #f8d7da; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
            echo "<h4>❌ Setup Error: " . $e->getMessage() . "</h4>";
            echo "</div>";
        }
    }
    
    // Show setup button if needed
    if (!empty($missing_tables) && !isset($_POST['setup_database'])) {
        echo "<form method='POST'>";
        echo "<button type='submit' name='setup_database' style='background: #28a745; color: white; padding: 15px 30px; border: none; border-radius: 5px; font-size: 16px; cursor: pointer;'>";
        echo "🔧 Setup Missing Tables";
        echo "</button>";
        echo "</form>";
    }
    
    // Final status check
    $stmt = $pdo->query("SHOW TABLES");
    $final_tables = $stmt->fetchAll(PDO::FETCH_COLUMN);
    
    echo "<h3>📊 Final Status:</h3>";
    foreach ($required_tables as $table) {
        $status = in_array($table, $final_tables) ? '✅' : '❌';
        echo "<div style='padding: 5px;'>$status $table</div>";
    }
    
} catch (PDOException $e) {
    echo "<div style='background: #f8d7da; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
    echo "<h4>❌ Database Connection Failed!</h4>";
    echo "<p>Error: " . $e->getMessage() . "</p>";
    echo "</div>";
}

echo "<br><h3>🔗 Next Steps:</h3>";
echo "<ol>";
echo "<li><a href='fix-admin-login.php'>Fix Admin Login</a></li>";
echo "<li><a href='admin/voters.php'>Voter Management</a></li>";
echo "<li><a href='admin/areas.php'>Area Management</a></li>";
echo "<li><a href='admin/'>Admin Dashboard</a></li>";
echo "</ol>";

echo "<br><div style='background: #e2e3e5; padding: 10px; border-radius: 5px;'>";
echo "<strong>Note:</strong> Delete this file after setup for security.";
echo "</div>";
?>
