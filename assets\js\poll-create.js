/**
 * Poll Creation JavaScript
 * Dynamic Realtime Online Voting System
 */

$(document).ready(function() {
    let optionCount = $('.option-item').length;
    const maxOptions = 20; // Should match MAX_POLL_OPTIONS from PHP
    
    // Add option functionality
    $('#addOption').on('click', function() {
        if (optionCount >= maxOptions) {
            OVS.showAlert(`Maximum ${maxOptions} options allowed.`, 'warning');
            return;
        }
        
        optionCount++;
        const optionHtml = `
            <div class="option-item mb-2">
                <div class="input-group">
                    <span class="input-group-text">${optionCount}</span>
                    <input type="text" class="form-control" name="options[]" 
                           placeholder="Enter option text" maxlength="200">
                    <button type="button" class="btn btn-outline-danger remove-option">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
            </div>
        `;
        
        $('#optionsList').append(optionHtml);
        updateOptionNumbers();
        updateRemoveButtons();
        
        // Focus on the new input
        $('#optionsList .option-item:last input').focus();
    });
    
    // Remove option functionality
    $(document).on('click', '.remove-option', function() {
        if ($('.option-item').length <= 2) {
            OVS.showAlert('Minimum 2 options required.', 'warning');
            return;
        }
        
        $(this).closest('.option-item').remove();
        optionCount--;
        updateOptionNumbers();
        updateRemoveButtons();
    });
    
    // Update option numbers
    function updateOptionNumbers() {
        $('.option-item').each(function(index) {
            $(this).find('.input-group-text').text(index + 1);
        });
    }
    
    // Update remove button states
    function updateRemoveButtons() {
        const optionItems = $('.option-item');
        const removeButtons = $('.remove-option');
        
        if (optionItems.length <= 2) {
            removeButtons.prop('disabled', true);
        } else {
            removeButtons.prop('disabled', false);
        }
        
        // Update add button state
        if (optionItems.length >= maxOptions) {
            $('#addOption').prop('disabled', true);
        } else {
            $('#addOption').prop('disabled', false);
        }
    }
    
    // Poll type change handler
    $('#type').on('change', function() {
        const type = $(this).val();
        const optionsSection = $('#optionsSection');

        if (type === 'text_response' || type === 'rating_scale') {
            optionsSection.hide();
        } else {
            optionsSection.show();
        }

        // Update placeholder text based on type
        if (type === 'rating_scale') {
            $('#title').attr('placeholder', 'e.g., How would you rate our service?');
        } else if (type === 'text_response') {
            $('#title').attr('placeholder', 'e.g., What is your feedback?');
        } else {
            $('#title').attr('placeholder', 'e.g., What is your favorite color?');
        }
    });

    // Access type change handler
    $('#access_type').on('change', function() {
        const accessType = $(this).val();
        const areaSelection = $('#area_selection');

        if (accessType === 'area_based' || accessType === 'mixed') {
            areaSelection.show();
        } else {
            areaSelection.hide();
        }
    });
    
    // Form validation
    $('#createPollForm').on('submit', function(e) {
        const title = $('#title').val().trim();
        const type = $('#type').val();
        
        if (!title) {
            e.preventDefault();
            OVS.showAlert('Poll title is required.', 'danger');
            $('#title').focus();
            return false;
        }
        
        // Validate options for choice-based polls
        if (type === 'single_choice' || type === 'multiple_choice') {
            const options = [];
            $('input[name="options[]"]').each(function() {
                const value = $(this).val().trim();
                if (value) {
                    options.push(value);
                }
            });
            
            if (options.length < 2) {
                e.preventDefault();
                OVS.showAlert('At least 2 options are required.', 'danger');
                $('input[name="options[]"]:first').focus();
                return false;
            }
            
            // Check for duplicate options
            const uniqueOptions = [...new Set(options)];
            if (uniqueOptions.length !== options.length) {
                e.preventDefault();
                OVS.showAlert('Duplicate options are not allowed.', 'danger');
                return false;
            }
        }
        
        // Validate end date
        const endDate = $('#end_date').val();
        if (endDate) {
            const endDateTime = new Date(endDate);
            const now = new Date();
            
            if (endDateTime <= now) {
                e.preventDefault();
                OVS.showAlert('End date must be in the future.', 'danger');
                $('#end_date').focus();
                return false;
            }
        }
        
        // Show loading state
        const submitBtn = $(this).find('button[type="submit"]');
        submitBtn.prop('disabled', true).html('<i class="fas fa-spinner fa-spin"></i> Creating Poll...');
    });
    
    // Character counter for title and description
    function addCharacterCounter(selector, maxLength) {
        const element = $(selector);
        const counter = $(`<small class="text-muted float-end char-counter"></small>`);
        element.parent().append(counter);
        
        function updateCounter() {
            const remaining = maxLength - element.val().length;
            counter.text(`${remaining} characters remaining`);
            
            if (remaining < 20) {
                counter.removeClass('text-muted').addClass('text-warning');
            } else if (remaining < 0) {
                counter.removeClass('text-warning').addClass('text-danger');
            } else {
                counter.removeClass('text-warning text-danger').addClass('text-muted');
            }
        }
        
        element.on('input', updateCounter);
        updateCounter();
    }
    
    addCharacterCounter('#title', 200);
    addCharacterCounter('#description', 1000);
    
    // Auto-save to localStorage
    function autoSave() {
        const formData = {
            title: $('#title').val(),
            description: $('#description').val(),
            type: $('#type').val(),
            options: [],
            allow_anonymous: $('#allow_anonymous').is(':checked'),
            show_results: $('#show_results').val(),
            end_date: $('#end_date').val()
        };
        
        $('input[name="options[]"]').each(function() {
            const value = $(this).val().trim();
            if (value) {
                formData.options.push(value);
            }
        });
        
        localStorage.setItem('poll_draft', JSON.stringify(formData));
    }
    
    // Load from localStorage
    function loadDraft() {
        const draft = localStorage.getItem('poll_draft');
        if (draft) {
            try {
                const data = JSON.parse(draft);
                
                if (confirm('Found a saved draft. Would you like to restore it?')) {
                    $('#title').val(data.title || '');
                    $('#description').val(data.description || '');
                    $('#type').val(data.type || 'single_choice').trigger('change');
                    $('#allow_anonymous').prop('checked', data.allow_anonymous !== false);
                    $('#show_results').val(data.show_results || 'after_vote');
                    $('#end_date').val(data.end_date || '');
                    
                    // Restore options
                    if (data.options && data.options.length > 0) {
                        $('#optionsList').empty();
                        optionCount = 0;
                        
                        data.options.forEach(function(option) {
                            optionCount++;
                            const optionHtml = `
                                <div class="option-item mb-2">
                                    <div class="input-group">
                                        <span class="input-group-text">${optionCount}</span>
                                        <input type="text" class="form-control" name="options[]" 
                                               value="${option}" placeholder="Enter option text" maxlength="200">
                                        <button type="button" class="btn btn-outline-danger remove-option">
                                            <i class="fas fa-times"></i>
                                        </button>
                                    </div>
                                </div>
                            `;
                            $('#optionsList').append(optionHtml);
                        });
                        
                        updateRemoveButtons();
                    }
                }
            } catch (e) {
                console.error('Error loading draft:', e);
            }
        }
    }
    
    // Auto-save on input changes
    $('#createPollForm').on('input change', 'input, textarea, select', function() {
        clearTimeout(window.autoSaveTimeout);
        window.autoSaveTimeout = setTimeout(autoSave, 1000);
    });
    
    // Clear draft on successful submission
    $('#createPollForm').on('submit', function() {
        localStorage.removeItem('poll_draft');
    });
    
    // Initialize
    updateRemoveButtons();
    $('#type').trigger('change');
    
    // Load draft if available
    if ($('#title').val() === '' && $('#description').val() === '') {
        loadDraft();
    }
    
    // Focus on title field
    $('#title').focus();
});
