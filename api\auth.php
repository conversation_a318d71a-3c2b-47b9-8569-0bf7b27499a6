<?php
/**
 * Authentication API Endpoints
 * Dynamic Realtime Online Voting System
 */

require_once '../config/config.php';

// Set JSON header
header('Content-Type: application/json');

// Handle CORS
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    header('Access-Control-Allow-Origin: *');
    header('Access-Control-Allow-Methods: GET, POST, OPTIONS');
    header('Access-Control-Allow-Headers: Content-Type, X-CSRF-Token');
    exit(0);
}

// Validate CSRF token for POST requests
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $csrfToken = $_SERVER['HTTP_X_CSRF_TOKEN'] ?? $_POST['csrf_token'] ?? '';
    if (!validateCSRFToken($csrfToken)) {
        http_response_code(403);
        echo json_encode(['success' => false, 'message' => 'Invalid CSRF token']);
        exit;
    }
}

$action = $_GET['action'] ?? $_POST['action'] ?? '';

try {
    switch ($action) {
        case 'login':
            handleLogin();
            break;
            
        case 'register':
            handleRegister();
            break;
            
        case 'check_username':
            handleCheckUsername();
            break;
            
        case 'check_email':
            handleCheckEmail();
            break;
            
        case 'get_session':
            handleGetSession();
            break;
            
        default:
            http_response_code(400);
            echo json_encode(['success' => false, 'message' => 'Invalid action']);
    }
} catch (Exception $e) {
    error_log("API Error: " . $e->getMessage());
    http_response_code(500);
    echo json_encode(['success' => false, 'message' => 'Internal server error']);
}

function handleLogin() {
    if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
        http_response_code(405);
        echo json_encode(['success' => false, 'message' => 'Method not allowed']);
        return;
    }
    
    $email = sanitizeInput($_POST['email'] ?? '');
    $password = $_POST['password'] ?? '';
    
    if (empty($email) || empty($password)) {
        echo json_encode(['success' => false, 'message' => 'Email and password are required']);
        return;
    }
    
    if (!checkLoginAttempts($email)) {
        echo json_encode(['success' => false, 'message' => 'Too many login attempts. Please try again later.']);
        return;
    }
    
    $result = authenticateUser($email, $password);
    
    if ($result['success']) {
        recordLoginAttempt($email, true);
        loginUser($result['user']);
        echo json_encode([
            'success' => true,
            'message' => 'Login successful',
            'user' => $result['user'],
            'redirect' => APP_URL
        ]);
    } else {
        recordLoginAttempt($email, false);
        echo json_encode($result);
    }
}

function handleRegister() {
    if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
        http_response_code(405);
        echo json_encode(['success' => false, 'message' => 'Method not allowed']);
        return;
    }
    
    $username = sanitizeInput($_POST['username'] ?? '');
    $email = sanitizeInput($_POST['email'] ?? '');
    $password = $_POST['password'] ?? '';
    $firstName = sanitizeInput($_POST['first_name'] ?? '');
    $lastName = sanitizeInput($_POST['last_name'] ?? '');
    
    // Validate input
    if (empty($username) || empty($email) || empty($password)) {
        echo json_encode(['success' => false, 'message' => 'Username, email, and password are required']);
        return;
    }
    
    if (!validateUsername($username)) {
        echo json_encode(['success' => false, 'message' => 'Invalid username format']);
        return;
    }
    
    if (!isValidEmail($email)) {
        echo json_encode(['success' => false, 'message' => 'Invalid email format']);
        return;
    }
    
    if (!validatePassword($password)) {
        echo json_encode(['success' => false, 'message' => 'Password must be at least ' . PASSWORD_MIN_LENGTH . ' characters']);
        return;
    }
    
    $result = createUser($username, $email, $password, $firstName ?: null, $lastName ?: null);
    echo json_encode($result);
}

function handleCheckUsername() {
    $username = sanitizeInput($_GET['username'] ?? '');
    
    if (empty($username)) {
        echo json_encode(['success' => false, 'message' => 'Username is required']);
        return;
    }
    
    if (!validateUsername($username)) {
        echo json_encode(['success' => false, 'available' => false, 'message' => 'Invalid username format']);
        return;
    }
    
    try {
        $db = getDB();
        $stmt = $db->prepare("SELECT id FROM users WHERE username = ?");
        $stmt->execute([$username]);
        $exists = $stmt->fetch() !== false;
        
        echo json_encode([
            'success' => true,
            'available' => !$exists,
            'message' => $exists ? 'Username is already taken' : 'Username is available'
        ]);
    } catch (Exception $e) {
        echo json_encode(['success' => false, 'message' => 'Error checking username']);
    }
}

function handleCheckEmail() {
    $email = sanitizeInput($_GET['email'] ?? '');
    
    if (empty($email)) {
        echo json_encode(['success' => false, 'message' => 'Email is required']);
        return;
    }
    
    if (!isValidEmail($email)) {
        echo json_encode(['success' => false, 'available' => false, 'message' => 'Invalid email format']);
        return;
    }
    
    try {
        $db = getDB();
        $stmt = $db->prepare("SELECT id FROM users WHERE email = ?");
        $stmt->execute([$email]);
        $exists = $stmt->fetch() !== false;
        
        echo json_encode([
            'success' => true,
            'available' => !$exists,
            'message' => $exists ? 'Email is already registered' : 'Email is available'
        ]);
    } catch (Exception $e) {
        echo json_encode(['success' => false, 'message' => 'Error checking email']);
    }
}

function handleGetSession() {
    echo json_encode([
        'success' => true,
        'logged_in' => isLoggedIn(),
        'user' => getCurrentUser(),
        'session_info' => getSessionInfo()
    ]);
}
?>
