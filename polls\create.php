<?php
/**
 * Create Poll Page
 * Dynamic Realtime Online Voting System
 */

require_once '../config/config.php';

// Require authentication
if (!isLoggedIn()) {
    $_SESSION['redirect_after_login'] = $_SERVER['REQUEST_URI'];
    redirect(APP_URL . '/auth/login.php');
}

$error = '';
$success = '';
$formData = [
    'title' => '',
    'description' => '',
    'type' => 'single_choice',
    'options' => ['', ''],
    'allow_anonymous' => true,
    'show_results' => 'after_vote',
    'end_date' => '',
    'access_type' => 'public',
    'allowed_areas' => [],
    'require_voter_verification' => false
];

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    // Validate CSRF token
    if (!validateCSRFToken($_POST['csrf_token'] ?? '')) {
        $error = 'Invalid request. Please try again.';
    } else {
        // Sanitize input
        $formData['title'] = sanitizeInput($_POST['title'] ?? '');
        $formData['description'] = sanitizeInput($_POST['description'] ?? '');
        $formData['type'] = sanitizeInput($_POST['type'] ?? 'single_choice');
        $formData['allow_anonymous'] = isset($_POST['allow_anonymous']);
        $formData['show_results'] = sanitizeInput($_POST['show_results'] ?? 'after_vote');
        $formData['end_date'] = sanitizeInput($_POST['end_date'] ?? '');
        $formData['access_type'] = sanitizeInput($_POST['access_type'] ?? 'public');
        $formData['allowed_areas'] = $_POST['allowed_areas'] ?? [];
        $formData['require_voter_verification'] = isset($_POST['require_voter_verification']);
        
        // Process options
        $options = [];
        if (isset($_POST['options']) && is_array($_POST['options'])) {
            foreach ($_POST['options'] as $option) {
                $option = sanitizeInput($option);
                if (!empty($option)) {
                    $options[] = $option;
                }
            }
        }
        $formData['options'] = $options;
        
        // Validate input
        if (empty($formData['title'])) {
            $error = 'Poll title is required.';
        } elseif (count($options) < 2) {
            $error = 'At least 2 options are required.';
        } elseif (count($options) > MAX_POLL_OPTIONS) {
            $error = 'Maximum ' . MAX_POLL_OPTIONS . ' options allowed.';
        } else {
            // Prepare settings
            $settings = [
                'allowAnonymous' => $formData['allow_anonymous'],
                'requireAuth' => !$formData['allow_anonymous'],
                'allowMultipleVotes' => false,
                'showResults' => $formData['show_results'],
                'ipRestriction' => false,
                'end_date' => $formData['end_date'] ? date('Y-m-d H:i:s', strtotime($formData['end_date'])) : null,
                'access_type' => $formData['access_type'],
                'allowed_areas' => $formData['allowed_areas'],
                'require_voter_verification' => $formData['require_voter_verification']
            ];
            
            // Create poll
            $user = getCurrentUser();
            $result = createPoll(
                $user['id'],
                $formData['title'],
                $formData['description'],
                $formData['type'],
                $options,
                $settings
            );
            
            if ($result['success']) {
                setFlashMessage('success', 'Poll created successfully!');
                redirect(APP_URL . '/polls/view.php?id=' . $result['poll_id']);
            } else {
                $error = $result['message'];
            }
        }
    }
}

// Get areas for area-based voting
try {
    $db = getDB();
    $stmt = $db->query("SELECT id, name, name_bn, type FROM areas WHERE is_active = 1 ORDER BY type, name");
    $areas = $stmt->fetchAll();
} catch (Exception $e) {
    $areas = [];
    error_log("Failed to fetch areas: " . $e->getMessage());
}

$page_title = 'Create Poll';
$page_js = ['poll-create.js'];
include '../includes/header.php';
?>

<div class="container mt-4">
    <div class="row">
        <div class="col-lg-8 mx-auto">
            <div class="card shadow">
                <div class="card-header bg-primary text-white">
                    <h3 class="card-title mb-0">
                        <i class="fas fa-plus"></i> Create New Poll
                    </h3>
                </div>
                <div class="card-body">
                    <?php if ($error): ?>
                    <div class="alert alert-danger" role="alert">
                        <i class="fas fa-exclamation-triangle"></i> <?php echo htmlspecialchars($error); ?>
                    </div>
                    <?php endif; ?>
                    
                    <form method="POST" action="" id="createPollForm">
                        <input type="hidden" name="csrf_token" value="<?php echo generateCSRFToken(); ?>">
                        
                        <!-- Basic Information -->
                        <div class="mb-4">
                            <h5 class="border-bottom pb-2">Basic Information</h5>
                            
                            <div class="form-floating mb-3">
                                <input type="text" class="form-control" id="title" name="title" 
                                       value="<?php echo htmlspecialchars($formData['title']); ?>" 
                                       maxlength="200" required>
                                <label for="title">Poll Title *</label>
                            </div>
                            
                            <div class="form-floating mb-3">
                                <textarea class="form-control" id="description" name="description" 
                                          style="height: 100px" maxlength="1000"><?php echo htmlspecialchars($formData['description']); ?></textarea>
                                <label for="description">Description (Optional)</label>
                            </div>
                            
                            <div class="form-floating mb-3">
                                <select class="form-select" id="type" name="type">
                                    <option value="single_choice" <?php echo $formData['type'] === 'single_choice' ? 'selected' : ''; ?>>
                                        Single Choice
                                    </option>
                                    <option value="multiple_choice" <?php echo $formData['type'] === 'multiple_choice' ? 'selected' : ''; ?>>
                                        Multiple Choice
                                    </option>
                                    <option value="text_response" <?php echo $formData['type'] === 'text_response' ? 'selected' : ''; ?>>
                                        Text Response
                                    </option>
                                    <option value="rating_scale" <?php echo $formData['type'] === 'rating_scale' ? 'selected' : ''; ?>>
                                        Rating Scale (1-10)
                                    </option>
                                </select>
                                <label for="type">Poll Type</label>
                            </div>
                        </div>
                        
                        <!-- Poll Options -->
                        <div class="mb-4" id="optionsSection">
                            <h5 class="border-bottom pb-2">Poll Options</h5>
                            <div id="optionsList">
                                <?php foreach ($formData['options'] as $index => $option): ?>
                                <div class="option-item mb-2">
                                    <div class="input-group">
                                        <span class="input-group-text"><?php echo $index + 1; ?></span>
                                        <input type="text" class="form-control" name="options[]" 
                                               value="<?php echo htmlspecialchars($option); ?>" 
                                               placeholder="Enter option text" maxlength="200">
                                        <button type="button" class="btn btn-outline-danger remove-option" 
                                                <?php echo count($formData['options']) <= 2 ? 'disabled' : ''; ?>>
                                            <i class="fas fa-times"></i>
                                        </button>
                                    </div>
                                </div>
                                <?php endforeach; ?>
                            </div>
                            
                            <button type="button" class="btn btn-outline-primary" id="addOption">
                                <i class="fas fa-plus"></i> Add Option
                            </button>
                            <small class="text-muted d-block mt-1">
                                Minimum 2 options, maximum <?php echo MAX_POLL_OPTIONS; ?> options
                            </small>
                        </div>
                        
                        <!-- Voting Access Control -->
                        <div class="mb-4">
                            <h5 class="border-bottom pb-2">ভোটিং অ্যাক্সেস কন্ট্রোল</h5>

                            <div class="form-floating mb-3">
                                <select class="form-select" id="access_type" name="access_type">
                                    <option value="public" <?php echo $formData['access_type'] === 'public' ? 'selected' : ''; ?>>
                                        পাবলিক (সবাই ভোট দিতে পারবে)
                                    </option>
                                    <option value="area_based" <?php echo $formData['access_type'] === 'area_based' ? 'selected' : ''; ?>>
                                        এলাকা/ওয়ার্ড ভিত্তিক (নির্দিষ্ট এলাকার মানুষ)
                                    </option>
                                    <option value="mixed" <?php echo $formData['access_type'] === 'mixed' ? 'selected' : ''; ?>>
                                        মিক্স (পাবলিক + নির্দিষ্ট এলাকা)
                                    </option>
                                </select>
                                <label for="access_type">ভোটিং অ্যাক্সেস ধরন</label>
                            </div>

                            <div id="area_selection" style="display: none;">
                                <label class="form-label">অনুমতিপ্রাপ্ত এলাকা/ওয়ার্ড নির্বাচন করুন:</label>
                                <div class="row">
                                    <?php foreach ($areas as $area): ?>
                                    <div class="col-md-6 mb-2">
                                        <div class="form-check">
                                            <input class="form-check-input" type="checkbox"
                                                   name="allowed_areas[]" value="<?php echo $area['id']; ?>"
                                                   id="area_<?php echo $area['id']; ?>"
                                                   <?php echo in_array($area['id'], $formData['allowed_areas']) ? 'checked' : ''; ?>>
                                            <label class="form-check-label" for="area_<?php echo $area['id']; ?>">
                                                <?php echo htmlspecialchars($area['name_bn'] ?: $area['name']); ?>
                                                <small class="text-muted">(<?php echo ucfirst($area['type']); ?>)</small>
                                            </label>
                                        </div>
                                    </div>
                                    <?php endforeach; ?>
                                </div>
                            </div>

                            <div class="form-check mb-3">
                                <input class="form-check-input" type="checkbox" id="require_voter_verification"
                                       name="require_voter_verification" <?php echo $formData['require_voter_verification'] ? 'checked' : ''; ?>>
                                <label class="form-check-label" for="require_voter_verification">
                                    ভোটার যাচাইকরণ প্রয়োজন
                                </label>
                                <div class="form-text">ভোটার আইডি দিয়ে যাচাই করতে হবে</div>
                            </div>
                        </div>

                        <!-- General Settings -->
                        <div class="mb-4">
                            <h5 class="border-bottom pb-2">সাধারণ সেটিংস</h5>

                            <div class="form-check mb-3">
                                <input class="form-check-input" type="checkbox" id="allow_anonymous"
                                       name="allow_anonymous" <?php echo $formData['allow_anonymous'] ? 'checked' : ''; ?>>
                                <label class="form-check-label" for="allow_anonymous">
                                    বেনামী ভোটিং অনুমতি দিন
                                </label>
                                <div class="form-text">লগইন ছাড়াই ভোট দেওয়ার সুবিধা</div>
                            </div>
                            
                            <div class="form-floating mb-3">
                                <select class="form-select" id="show_results" name="show_results">
                                    <option value="always" <?php echo $formData['show_results'] === 'always' ? 'selected' : ''; ?>>
                                        Always visible
                                    </option>
                                    <option value="after_vote" <?php echo $formData['show_results'] === 'after_vote' ? 'selected' : ''; ?>>
                                        After voting
                                    </option>
                                    <option value="after_end" <?php echo $formData['show_results'] === 'after_end' ? 'selected' : ''; ?>>
                                        After poll ends
                                    </option>
                                    <option value="never" <?php echo $formData['show_results'] === 'never' ? 'selected' : ''; ?>>
                                        Never (private)
                                    </option>
                                </select>
                                <label for="show_results">Show Results</label>
                            </div>
                            
                            <div class="form-floating mb-3">
                                <input type="datetime-local" class="form-control" id="end_date" name="end_date" 
                                       value="<?php echo $formData['end_date']; ?>">
                                <label for="end_date">End Date (Optional)</label>
                                <div class="form-text">Leave empty for no end date</div>
                            </div>
                        </div>
                        
                        <!-- Submit Buttons -->
                        <div class="d-flex gap-2">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save"></i> Create Poll
                            </button>
                            <a href="<?php echo APP_URL; ?>/polls/manage.php" class="btn btn-outline-secondary">
                                <i class="fas fa-times"></i> Cancel
                            </a>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<?php include '../includes/footer.php'; ?>
