<?php
/**
 * Simple Voter Upload Function
 * Minimal upload function for testing
 */

function uploadVotersSimple($file) {
    if (!$file || $file['error'] !== UPLOAD_ERR_OK) {
        return ['success' => false, 'message' => 'ফাইল আপলোড করতে ব্যর্থ: ' . $file['error']];
    }
    
    if (pathinfo($file['name'], PATHINFO_EXTENSION) !== 'csv') {
        return ['success' => false, 'message' => 'শুধুমাত্র CSV ফাইল গ্রহণযোগ্য'];
    }
    
    try {
        $db = getDB();
        $handle = fopen($file['tmp_name'], 'r');
        
        if (!$handle) {
            return ['success' => false, 'message' => 'ফাইল পড়তে ব্যর্থ'];
        }
        
        // Read and clean headers
        $headers = fgetcsv($handle);
        if (!$headers) {
            fclose($handle);
            return ['success' => false, 'message' => 'CSV headers পাওয়া যায়নি'];
        }
        
        // Clean headers (remove BOM)
        $headers = array_map(function($header) {
            return trim(str_replace("\xEF\xBB\xBF", '', $header));
        }, $headers);
        
        $success_count = 0;
        $error_count = 0;
        $errors = [];
        $line_number = 1;
        
        // Log headers for debugging
        error_log("CSV Headers: " . implode(', ', $headers));
        
        while (($row = fgetcsv($handle)) !== false) {
            $line_number++;
            
            // Skip empty rows
            if (empty(array_filter($row))) {
                continue;
            }
            
            try {
                // Find required columns (voter_id is now optional)
                $voter_id_col = array_search('voter_id', $headers);
                $name_col = array_search('name', $headers);
                $nid_col = array_search('nid', $headers);
                $mobile_col = array_search('mobile', $headers);
                $ward_id_col = array_search('ward_id', $headers);

                // Check if required columns exist (voter_id is optional)
                if ($name_col === false || $nid_col === false || $mobile_col === false || $ward_id_col === false) {
                    $errors[] = "Line $line_number: Required columns missing (name, nid, mobile, ward_id)";
                    $error_count++;
                    continue;
                }

                // Get values
                $voter_id = trim($row[$voter_id_col] ?? '');
                $name = trim($row[$name_col] ?? '');
                $nid = trim($row[$nid_col] ?? '');
                $mobile = trim($row[$mobile_col] ?? '');
                $ward_id = intval($row[$ward_id_col] ?? 0);

                // Auto-generate voter_id if empty
                if (empty($voter_id)) {
                    require_once '../includes/voter-id-generator.php';
                    $voter_id = generateVoterIDWithArea($db, $ward_id, 'V{WARD_CODE}{YYYY}{RRRRR}');
                    error_log("Auto-generated voter ID: $voter_id for line $line_number");
                }
                
                // Optional fields
                $name_bn_col = array_search('name_bn', $headers);
                $address_col = array_search('address', $headers);
                $father_name_col = array_search('father_name', $headers);
                $mother_name_col = array_search('mother_name', $headers);
                $email_col = array_search('email', $headers);
                $dob_col = array_search('date_of_birth', $headers);
                $gender_col = array_search('gender', $headers);
                
                $name_bn = $name_bn_col !== false ? trim($row[$name_bn_col] ?? '') : '';
                $address = $address_col !== false ? trim($row[$address_col] ?? '') : '';
                $father_name = $father_name_col !== false ? trim($row[$father_name_col] ?? '') : '';
                $mother_name = $mother_name_col !== false ? trim($row[$mother_name_col] ?? '') : '';
                $email = $email_col !== false ? trim($row[$email_col] ?? '') : '';
                $date_of_birth = $dob_col !== false ? trim($row[$dob_col] ?? '') : '';
                $gender = $gender_col !== false ? trim($row[$gender_col] ?? '') : '';
                
                // Validate required fields (voter_id now auto-generated if empty)
                if (empty($name) || empty($nid) || empty($mobile) || $ward_id <= 0) {
                    $errors[] = "Line $line_number: Empty required fields - name: '$name', nid: '$nid', mobile: '$mobile', ward_id: '$ward_id'";
                    $error_count++;
                    continue;
                }

                // Final check for voter_id (should be generated by now)
                if (empty($voter_id)) {
                    $errors[] = "Line $line_number: Failed to generate voter_id";
                    $error_count++;
                    continue;
                }

                // Clean and validate NID (flexible validation)
                $clean_nid = preg_replace('/[^0-9]/', '', $nid);
                if (strlen($clean_nid) < 10 || strlen($clean_nid) > 17) {
                    $errors[] = "Line $line_number: Invalid NID format for '$voter_id' (Original: '$nid', Cleaned: '$clean_nid', Length: " . strlen($clean_nid) . ")";
                    $error_count++;
                    continue;
                }
                $nid = $clean_nid; // Use cleaned NID

                // Clean and validate mobile
                $clean_mobile = preg_replace('/[^0-9]/', '', $mobile);
                if (strlen($clean_mobile) != 11 || !preg_match('/^01/', $clean_mobile)) {
                    $errors[] = "Line $line_number: Invalid mobile format for '$voter_id' (Original: '$mobile', Cleaned: '$clean_mobile')";
                    $error_count++;
                    continue;
                }
                $mobile = $clean_mobile; // Use cleaned mobile
                
                // Validate ward exists
                $stmt = $db->prepare("SELECT id FROM areas WHERE id = ? AND type = 'ward' AND is_active = 1");
                $stmt->execute([$ward_id]);
                if (!$stmt->fetch()) {
                    $errors[] = "Line $line_number: Ward ID '$ward_id' not found in database";
                    $error_count++;
                    continue;
                }
                
                // Check for NID duplicates only
                if (!empty($nid)) {
                    $stmt = $db->prepare("SELECT id, voter_id FROM voters WHERE nid = ?");
                    $stmt->execute([$nid]);
                    $existing = $stmt->fetch();
                    if ($existing) {
                        $errors[] = "Line $line_number: NID '{$nid}' already exists for voter {$existing['voter_id']}";
                        $error_count++;
                        continue;
                    }
                }
                
                // Validate date format if provided
                $dob_value = null;
                if (!empty($date_of_birth)) {
                    $dob_parsed = DateTime::createFromFormat('Y-m-d', $date_of_birth);
                    if ($dob_parsed) {
                        $dob_value = $date_of_birth;
                    } else {
                        $errors[] = "Line $line_number: Invalid date format '$date_of_birth' (use YYYY-MM-DD)";
                        $error_count++;
                        continue;
                    }
                }
                
                // Insert voter
                $stmt = $db->prepare("
                    INSERT INTO voters (
                        voter_id, name, name_bn, father_name, mother_name,
                        nid, mobile, email, date_of_birth, gender,
                        area_id, address, created_by, created_at
                    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, NOW())
                ");
                
                $result = $stmt->execute([
                    $voter_id,
                    $name,
                    $name_bn ?: null,
                    $father_name ?: null,
                    $mother_name ?: null,
                    $nid,
                    $mobile,
                    $email ?: null,
                    $dob_value,
                    $gender ?: null,
                    $ward_id,
                    $address ?: null,
                    getCurrentUser()['id']
                ]);
                
                if ($result) {
                    $success_count++;
                    error_log("Successfully inserted voter: $voter_id");
                } else {
                    $errors[] = "Line $line_number: Database insert failed for '$voter_id'";
                    $error_count++;
                }
                
            } catch (Exception $e) {
                $errors[] = "Line $line_number: Error - " . $e->getMessage();
                $error_count++;
                error_log("Upload error on line $line_number: " . $e->getMessage());
            }
        }
        
        fclose($handle);
        
        $message = "Upload completed: $success_count voters added successfully";
        if ($error_count > 0) {
            $message .= ", $error_count errors occurred";
        }
        
        return [
            'success' => $success_count > 0,
            'message' => $message,
            'details' => [
                'success' => $success_count,
                'errors' => $error_count,
                'error_list' => $errors,
                'total_lines' => $line_number - 1
            ]
        ];
        
    } catch (Exception $e) {
        error_log("Upload function error: " . $e->getMessage());
        return ['success' => false, 'message' => 'Upload failed: ' . $e->getMessage()];
    }
}
?>
