<?php
/**
 * অ্যাডমিন ড্যাশবোর্ড
 * Admin Dashboard
 */

require_once '../config/config.php';

// Require admin access
if (!isLoggedIn() || !isAdmin()) {
    redirect(APP_URL . '/auth/login.php');
}

// Get dashboard statistics
try {
    $db = getDB();
    
    // Total counts
    $stats = [];
    
    // Total users
    $stmt = $db->query("SELECT COUNT(*) as count FROM users WHERE is_active = 1");
    $stats['total_users'] = $stmt->fetch()['count'];
    
    // Total voters
    $stmt = $db->query("SELECT COUNT(*) as count FROM voters WHERE is_active = 1");
    $stats['total_voters'] = $stmt->fetch()['count'];
    
    // Total areas
    $stmt = $db->query("SELECT COUNT(*) as count FROM areas WHERE is_active = 1");
    $stats['total_areas'] = $stmt->fetch()['count'];
    
    // Total polls
    $stmt = $db->query("SELECT COUNT(*) as count FROM polls WHERE is_active = 1");
    $stats['total_polls'] = $stmt->fetch()['count'];
    
    // Total votes
    $stmt = $db->query("SELECT COUNT(*) as count FROM votes");
    $stats['total_votes'] = $stmt->fetch()['count'];
    
    // Active polls
    $stmt = $db->query("
        SELECT COUNT(*) as count FROM polls 
        WHERE is_active = 1 AND (end_date IS NULL OR end_date > NOW())
    ");
    $stats['active_polls'] = $stmt->fetch()['count'];
    
    // Recent activity
    $stmt = $db->prepare("
        SELECT al.*, u.username 
        FROM activity_logs al 
        LEFT JOIN users u ON al.user_id = u.id 
        ORDER BY al.created_at DESC 
        LIMIT 10
    ");
    $stmt->execute();
    $recent_activity = $stmt->fetchAll();
    
    // Poll types breakdown
    $stmt = $db->query("
        SELECT type, COUNT(*) as count 
        FROM polls 
        WHERE is_active = 1 
        GROUP BY type
    ");
    $poll_types = $stmt->fetchAll();
    
    // Area types breakdown
    $stmt = $db->query("
        SELECT type, COUNT(*) as count 
        FROM areas 
        WHERE is_active = 1 
        GROUP BY type
    ");
    $area_types = $stmt->fetchAll();
    
} catch (Exception $e) {
    $stats = [];
    $recent_activity = [];
    $poll_types = [];
    $area_types = [];
    error_log("Failed to load dashboard stats: " . $e->getMessage());
}

$page_title = 'অ্যাডমিন ড্যাশবোর্ড - Admin Dashboard';
$page_subtitle = 'সিস্টেম ওভারভিউ ও পরিসংখ্যান';
$show_top_nav = true;

// Start output buffering for content
ob_start();
?>

<div class="container-fluid py-4">
    <div class="row">
        <div class="col-12">
            <h2 class="mb-4">
                <i class="fas fa-tachometer-alt"></i> অ্যাডমিন ড্যাশবোর্ড
                <small class="text-muted">সিস্টেম ওভারভিউ</small>
            </h2>
        </div>
    </div>
    
    <!-- Statistics Cards -->
    <div class="row mb-4">
        <div class="col-lg-2 col-md-4 col-sm-6 mb-3">
            <div class="card bg-primary text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4 class="mb-0"><?php echo number_format($stats['total_users'] ?? 0); ?></h4>
                            <p class="mb-0">মোট ইউজার</p>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-users fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-lg-2 col-md-4 col-sm-6 mb-3">
            <div class="card bg-success text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4 class="mb-0"><?php echo number_format($stats['total_voters'] ?? 0); ?></h4>
                            <p class="mb-0">মোট ভোটার</p>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-user-check fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-lg-2 col-md-4 col-sm-6 mb-3">
            <div class="card bg-info text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4 class="mb-0"><?php echo number_format($stats['total_areas'] ?? 0); ?></h4>
                            <p class="mb-0">মোট এলাকা</p>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-map-marker-alt fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-lg-2 col-md-4 col-sm-6 mb-3">
            <div class="card bg-warning text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4 class="mb-0"><?php echo number_format($stats['total_polls'] ?? 0); ?></h4>
                            <p class="mb-0">মোট পোল</p>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-poll fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-lg-2 col-md-4 col-sm-6 mb-3">
            <div class="card bg-danger text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4 class="mb-0"><?php echo number_format($stats['total_votes'] ?? 0); ?></h4>
                            <p class="mb-0">মোট ভোট</p>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-vote-yea fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-lg-2 col-md-4 col-sm-6 mb-3">
            <div class="card bg-secondary text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4 class="mb-0"><?php echo number_format($stats['active_polls'] ?? 0); ?></h4>
                            <p class="mb-0">সক্রিয় পোল</p>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-play-circle fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Quick Actions -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0"><i class="fas fa-bolt"></i> দ্রুত অ্যাকশন</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-3 mb-2">
                            <a href="voters.php" class="btn btn-primary w-100">
                                <i class="fas fa-users"></i> ভোটার ম্যানেজমেন্ট
                            </a>
                        </div>
                        <div class="col-md-3 mb-2">
                            <a href="areas.php" class="btn btn-success w-100">
                                <i class="fas fa-map-marker-alt"></i> এলাকা ম্যানেজমেন্ট
                            </a>
                        </div>
                        <div class="col-md-3 mb-2">
                            <a href="../polls/create.php" class="btn btn-info w-100">
                                <i class="fas fa-plus"></i> নতুন পোল তৈরি
                            </a>
                        </div>
                        <div class="col-md-3 mb-2">
                            <a href="polls.php" class="btn btn-warning w-100">
                                <i class="fas fa-poll"></i> পোল ম্যানেজমেন্ট
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Charts and Activity -->
    <div class="row">
        <!-- Poll Types Chart -->
        <div class="col-md-6 mb-4">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0"><i class="fas fa-chart-pie"></i> পোলের ধরন</h5>
                </div>
                <div class="card-body">
                    <?php if (!empty($poll_types)): ?>
                        <?php foreach ($poll_types as $type): ?>
                        <div class="d-flex justify-content-between align-items-center mb-2">
                            <span><?php echo ucfirst(str_replace('_', ' ', $type['type'])); ?></span>
                            <span class="badge bg-primary"><?php echo $type['count']; ?></span>
                        </div>
                        <?php endforeach; ?>
                    <?php else: ?>
                        <p class="text-muted">কোন পোল পাওয়া যায়নি</p>
                    <?php endif; ?>
                </div>
            </div>
        </div>
        
        <!-- Area Types Chart -->
        <div class="col-md-6 mb-4">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0"><i class="fas fa-chart-bar"></i> এলাকার ধরন</h5>
                </div>
                <div class="card-body">
                    <?php if (!empty($area_types)): ?>
                        <?php foreach ($area_types as $type): ?>
                        <div class="d-flex justify-content-between align-items-center mb-2">
                            <span><?php echo ucfirst($type['type']); ?></span>
                            <span class="badge bg-success"><?php echo $type['count']; ?></span>
                        </div>
                        <?php endforeach; ?>
                    <?php else: ?>
                        <p class="text-muted">কোন এলাকা পাওয়া যায়নি</p>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Recent Activity -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0"><i class="fas fa-history"></i> সাম্প্রতিক কার্যকলাপ</h5>
                </div>
                <div class="card-body">
                    <?php if (!empty($recent_activity)): ?>
                    <div class="table-responsive">
                        <table class="table table-sm">
                            <thead>
                                <tr>
                                    <th>ইউজার</th>
                                    <th>অ্যাকশন</th>
                                    <th>রিসোর্স</th>
                                    <th>সময়</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($recent_activity as $activity): ?>
                                <tr>
                                    <td>
                                        <?php echo htmlspecialchars($activity['username'] ?: 'System'); ?>
                                    </td>
                                    <td>
                                        <span class="badge bg-info"><?php echo htmlspecialchars($activity['action']); ?></span>
                                    </td>
                                    <td>
                                        <?php echo htmlspecialchars($activity['resource']); ?>
                                        <?php if ($activity['resource_id']): ?>
                                            #<?php echo $activity['resource_id']; ?>
                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <small><?php echo timeAgo($activity['created_at']); ?></small>
                                    </td>
                                </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                    <?php else: ?>
                        <p class="text-muted">কোন কার্যকলাপ পাওয়া যায়নি</p>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
</div>

<?php
$content = ob_get_clean();

// Include the sidebar layout
include '../includes/header-with-sidebar.php';
?>
