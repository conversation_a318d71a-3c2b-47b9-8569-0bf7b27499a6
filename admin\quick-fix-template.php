<?php
/**
 * Quick Fix Template Generator
 * Generate working CSV template with actual database data
 */

require_once '../config/config.php';

// Check if user is admin
if (!isLoggedIn() || !isAdmin()) {
    redirect(APP_URL . '/auth/login.php');
}

try {
    $db = getDB();
    
    // Get actual ward data
    $stmt = $db->query("
        SELECT a.id, a.name, a.name_bn, a.type,
               p.name as parent_name, p.name_bn as parent_name_bn, p.type as parent_type
        FROM areas a
        LEFT JOIN areas p ON a.parent_id = p.id
        WHERE a.type = 'ward' AND a.is_active = 1
        ORDER BY a.id
        LIMIT 5
    ");
    $wards = $stmt->fetchAll();
    
    if (empty($wards)) {
        echo "<h2>❌ No Wards Found</h2>";
        echo "<p>Please create some wards first using the areas management.</p>";
        echo "<a href='areas.php'>Go to Areas Management</a>";
        exit;
    }
    
    // Set headers for CSV download
    header('Content-Type: text/csv; charset=utf-8');
    header('Content-Disposition: attachment; filename="working-voter-template.csv"');
    header('Cache-Control: no-cache, must-revalidate');
    
    // Create file pointer
    $output = fopen('php://output', 'w');
    
    // Add BOM for UTF-8
    fprintf($output, chr(0xEF).chr(0xBB).chr(0xBF));
    
    // Headers
    $headers = [
        'voter_id',
        'name', 
        'name_bn',
        'father_name',
        'mother_name', 
        'nid',
        'mobile',
        'email',
        'date_of_birth',
        'gender',
        'division_name',
        'district_name',
        'upazila_name',
        'union_name',
        'village_name',
        'ward_name',
        'ward_id',
        'address'
    ];
    
    fputcsv($output, $headers);
    
    // Generate sample data using actual ward IDs
    $sample_voters = [
        [
            'name' => 'Md. Rahman Khan',
            'name_bn' => 'মোঃ রহমান খান',
            'father_name' => 'Md. Abdul Khan',
            'mother_name' => 'Mst. Fatema Khan',
            'nid' => '1234567890123',
            'mobile' => '01712345678',
            'email' => '<EMAIL>',
            'gender' => 'male',
            'dob' => '1985-05-15'
        ],
        [
            'name' => 'Mst. Salma Begum',
            'name_bn' => 'মোসাঃ সালমা বেগম',
            'father_name' => 'Md. Karim Uddin',
            'mother_name' => 'Mst. Rashida Begum',
            'nid' => '2345678901234',
            'mobile' => '01823456789',
            'email' => '<EMAIL>',
            'gender' => 'female',
            'dob' => '1990-08-22'
        ],
        [
            'name' => 'Md. Kamal Hossain',
            'name_bn' => 'মোঃ কামাল হোসেন',
            'father_name' => 'Md. Nazrul Islam',
            'mother_name' => 'Mst. Rahima Khatun',
            'nid' => '3456789012345',
            'mobile' => '01934567890',
            'email' => '<EMAIL>',
            'gender' => 'male',
            'dob' => '1988-12-10'
        ],
        [
            'name' => 'Mst. Nasreen Akter',
            'name_bn' => 'মোসাঃ নাসরিন আক্তার',
            'father_name' => 'Md. Shahid Ullah',
            'mother_name' => 'Mst. Jahanara Begum',
            'nid' => '4567890123456',
            'mobile' => '01645678901',
            'email' => '<EMAIL>',
            'gender' => 'female',
            'dob' => '1992-03-18'
        ],
        [
            'name' => 'Md. Aminul Islam',
            'name_bn' => 'মোঃ আমিনুল ইসলাম',
            'father_name' => 'Md. Rafiqul Islam',
            'mother_name' => 'Mst. Rokeya Begum',
            'nid' => '5678901234567',
            'mobile' => '01756789012',
            'email' => '<EMAIL>',
            'gender' => 'male',
            'dob' => '1987-07-25'
        ]
    ];
    
    // Generate rows with actual ward data
    foreach ($sample_voters as $index => $voter) {
        if (!isset($wards[$index])) break;
        
        $ward = $wards[$index];
        $voter_id = 'V' . str_pad($index + 1, 3, '0', STR_PAD_LEFT);
        
        $row = [
            $voter_id,
            $voter['name'],
            $voter['name_bn'],
            $voter['father_name'],
            $voter['mother_name'],
            $voter['nid'],
            $voter['mobile'],
            $voter['email'],
            $voter['dob'],
            $voter['gender'],
            'ঢাকা বিভাগ', // Default division
            'ঢাকা জেলা', // Default district
            'ধানমন্ডি উপজেলা', // Default upazila
            $ward['parent_name_bn'] ?: $ward['parent_name'] ?: 'ধানমন্ডি পৌরসভা',
            '', // Village (optional)
            $ward['name_bn'] ?: $ward['name'],
            $ward['id'], // Actual ward ID from database
            'বাড়ি নং ' . (($index + 1) * 123) . ', রোড নং ' . (($index + 1) * 5) . ', ঢাকা'
        ];
        
        fputcsv($output, $row);
    }
    
    fclose($output);
    
} catch (Exception $e) {
    echo "<h2>❌ Error: " . $e->getMessage() . "</h2>";
    echo "<p>Please check your database connection and try again.</p>";
}
?>
