<?php
/**
 * ভোটার ম্যানেজমেন্ট পেজ
 * Voter Management Page
 */

require_once '../config/config.php';
require_once '../includes/hierarchical-upload.php';
require_once '../includes/simple-upload.php';
require_once '../includes/voter-id-generator.php';

// Use simple photo upload if GD is not available
if (extension_loaded('gd')) {
    require_once '../includes/photo-upload.php';
} else {
    require_once '../includes/simple-photo-upload.php';
}

// Require admin access
if (!isLoggedIn() || !isAdmin()) {
    redirect(APP_URL . '/auth/login.php');
}

$action = $_GET['action'] ?? 'list';
$message = '';
$error = '';

// Get upload result for modal display
$upload_result = null;
if (isset($_SESSION['upload_result'])) {
    $upload_result = $_SESSION['upload_result'];
    unset($_SESSION['upload_result']); // Clear after use
}

// Handle actions
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (!validateCSRFToken($_POST['csrf_token'] ?? '')) {
        $error = 'Invalid request. Please try again.';
    } else {
        switch ($action) {
            case 'add':
                $result = addVoter($_POST);
                if ($result['success']) {
                    setFlashMessage('success', $result['message']);
                    redirect($_SERVER['PHP_SELF']);
                } else {
                    $error = $result['message'];
                }
                break;
                
            case 'upload':
                // Try simple upload first for better debugging
                $result = uploadVotersSimple($_FILES['voter_file'] ?? null);

                // Store upload result in session for modal display
                $_SESSION['upload_result'] = $result;

                // Always redirect to show modal
                redirect($_SERVER['PHP_SELF'] . '?show_upload_result=1');
                break;

            case 'upload_photo':
                $voter_id = intval($_POST['voter_id'] ?? 0);
                if ($voter_id > 0 && isset($_FILES['photo'])) {
                    // Use appropriate upload function based on GD availability
                    if (extension_loaded('gd')) {
                        $result = uploadVoterPhoto($_FILES['photo'], $voter_id, getCurrentUser()['id']);
                    } else {
                        $result = simpleUploadVoterPhoto($_FILES['photo'], $voter_id, getCurrentUser()['id']);
                    }

                    if ($result['success']) {
                        setFlashMessage('success', $result['message']);
                    } else {
                        setFlashMessage('error', $result['message']);
                    }
                } else {
                    setFlashMessage('error', 'অবৈধ ভোটার বা ছবি');
                }
                redirect($_SERVER['PHP_SELF']);
                break;

            case 'delete_photo':
                $voter_id = intval($_POST['voter_id'] ?? 0);
                if ($voter_id > 0) {
                    // Use appropriate delete function based on GD availability
                    if (extension_loaded('gd')) {
                        $result = deleteVoterPhoto($voter_id);
                    } else {
                        $result = simpleDeleteVoterPhoto($voter_id);
                    }

                    if ($result['success']) {
                        setFlashMessage('success', $result['message']);
                    } else {
                        setFlashMessage('error', $result['message']);
                    }
                } else {
                    setFlashMessage('error', 'অবৈধ ভোটার');
                }
                redirect($_SERVER['PHP_SELF']);
                break;

            case 'edit':
                $voter_id = intval($_POST['voter_id'] ?? 0);
                if ($voter_id > 0) {
                    $result = updateVoter($voter_id, $_POST);
                    if ($result['success']) {
                        setFlashMessage('success', $result['message']);
                    } else {
                        setFlashMessage('error', $result['message']);
                    }
                } else {
                    setFlashMessage('error', 'অবৈধ ভোটার আইডি');
                }
                redirect($_SERVER['PHP_SELF']);
                break;

            case 'delete':
                $voter_id = intval($_POST['voter_id'] ?? 0);
                if ($voter_id > 0) {
                    $result = deleteVoter($voter_id);
                    if ($result['success']) {
                        setFlashMessage('success', $result['message']);
                    } else {
                        setFlashMessage('error', $result['message']);
                    }
                } else {
                    setFlashMessage('error', 'অবৈধ ভোটার আইডি');
                }
                redirect($_SERVER['PHP_SELF']);
                break;
        }
    }
}

// Get voters list
$page = max(1, intval($_GET['page'] ?? 1));
$search = sanitizeInput($_GET['search'] ?? '');
$area_filter = intval($_GET['area'] ?? 0);
$limit = 20;
$offset = ($page - 1) * $limit;

try {
    $db = getDB();
    
    // Build query
    $where_conditions = ['v.is_active = 1'];
    $params = [];
    
    if ($search) {
        $where_conditions[] = "(v.name LIKE ? OR v.name_bn LIKE ? OR v.voter_id LIKE ? OR v.nid LIKE ?)";
        $search_term = "%$search%";
        $params = array_merge($params, [$search_term, $search_term, $search_term, $search_term]);
    }
    
    if ($area_filter) {
        $where_conditions[] = "v.area_id = ?";
        $params[] = $area_filter;
    }
    
    $where_clause = implode(' AND ', $where_conditions);
    
    // Get voters
    $stmt = $db->prepare("
        SELECT v.*, a.name as area_name, a.name_bn as area_name_bn, a.type as area_type,
               u.username as linked_user
        FROM voters v 
        LEFT JOIN areas a ON v.area_id = a.id 
        LEFT JOIN users u ON v.user_id = u.id
        WHERE $where_clause
        ORDER BY v.created_at DESC 
        LIMIT ? OFFSET ?
    ");
    $params[] = $limit;
    $params[] = $offset;
    $stmt->execute($params);
    $voters = $stmt->fetchAll();
    
    // Get total count
    $count_params = array_slice($params, 0, -2); // Remove limit and offset
    $stmt = $db->prepare("SELECT COUNT(*) FROM voters v WHERE $where_clause");
    $stmt->execute($count_params);
    $total_voters = $stmt->fetchColumn();
    
    // Get areas for filter and edit form
    $stmt = $db->query("SELECT id, name, name_bn, type FROM areas WHERE is_active = 1 ORDER BY type, name");
    $areas = $stmt->fetchAll();
    
} catch (Exception $e) {
    $error = "Failed to load voters: " . $e->getMessage();
    $voters = [];
    $total_voters = 0;
    $areas = [];
}

$total_pages = ceil($total_voters / $limit);

$page_title = 'ভোটার ম্যানেজমেন্ট - Voter Management';
include '../includes/header.php';
?>

<div class="container-fluid mt-4">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h2><i class="fas fa-users"></i> ভোটার ম্যানেজমেন্ট</h2>
                <div class="btn-group">
                    <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addVoterModal">
                        <i class="fas fa-plus"></i> নতুন ভোটার
                    </button>
                    <div class="btn-group">
                        <button type="button" class="btn btn-success" data-bs-toggle="modal" data-bs-target="#uploadModal">
                            <i class="fas fa-upload"></i> ভোটার আপলোড
                        </button>
                        <button type="button" class="btn btn-success dropdown-toggle dropdown-toggle-split" data-bs-toggle="dropdown">
                            <span class="visually-hidden">Toggle Dropdown</span>
                        </button>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="download-template.php?format=csv">
                                <i class="fas fa-download"></i> টেমপ্লেট ডাউনলোড (CSV)
                            </a></li>
                            <li><a class="dropdown-item" href="download-template.php?format=areas">
                                <i class="fas fa-list"></i> এলাকা তালিকা (CSV)
                            </a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="#" data-bs-toggle="modal" data-bs-target="#uploadHelpModal">
                                <i class="fas fa-question-circle"></i> আপলোড সহায়তা
                            </a></li>
                        </ul>
                    </div>
                    <a href="areas.php" class="btn btn-info">
                        <i class="fas fa-map-marker-alt"></i> এলাকা ম্যানেজমেন্ট
                    </a>
                </div>
            </div>
            
            <?php if ($error): ?>
            <div class="alert alert-danger">
                <i class="fas fa-exclamation-triangle"></i> <?php echo htmlspecialchars($error); ?>
            </div>
            <?php endif; ?>
            
            <!-- Search and Filter -->
            <div class="card mb-4">
                <div class="card-body">
                    <form method="GET" class="row g-3">
                        <div class="col-md-4">
                            <input type="text" class="form-control" name="search" 
                                   placeholder="নাম, ভোটার আইডি বা এনআইডি দিয়ে খুঁজুন" 
                                   value="<?php echo htmlspecialchars($search); ?>">
                        </div>
                        <div class="col-md-3">
                            <select name="area" class="form-select">
                                <option value="">সব এলাকা</option>
                                <?php foreach ($areas as $area): ?>
                                <option value="<?php echo $area['id']; ?>" 
                                        <?php echo $area_filter == $area['id'] ? 'selected' : ''; ?>>
                                    <?php echo htmlspecialchars($area['name_bn'] ?: $area['name']); ?>
                                    (<?php echo ucfirst($area['type']); ?>)
                                </option>
                                <?php endforeach; ?>
                            </select>
                        </div>
                        <div class="col-md-2">
                            <button type="submit" class="btn btn-outline-primary w-100">
                                <i class="fas fa-search"></i> খুঁজুন
                            </button>
                        </div>
                        <div class="col-md-2">
                            <a href="<?php echo $_SERVER['PHP_SELF']; ?>" class="btn btn-outline-secondary w-100">
                                <i class="fas fa-times"></i> রিসেট
                            </a>
                        </div>
                    </form>
                </div>
            </div>
            
            <!-- Voters Table -->
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        ভোটার তালিকা (মোট: <?php echo number_format($total_voters); ?>)
                    </h5>
                </div>
                <div class="card-body p-0">
                    <div class="table-responsive">
                        <table class="table table-striped table-hover mb-0">
                            <thead class="table-dark">
                                <tr>
                                    <th>ছবি</th>
                                    <th>ভোটার আইডি</th>
                                    <th>নাম</th>
                                    <th>এনআইডি</th>
                                    <th>মোবাইল</th>
                                    <th>এলাকা/ওয়ার্ড</th>
                                    <th>ইউজার লিংক</th>
                                    <th>তারিখ</th>
                                    <th>অ্যাকশন</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php if (empty($voters)): ?>
                                <tr>
                                    <td colspan="9" class="text-center py-4">
                                        <i class="fas fa-users fa-3x text-muted mb-3"></i>
                                        <p class="text-muted">কোন ভোটার পাওয়া যায়নি</p>
                                    </td>
                                </tr>
                                <?php else: ?>
                                    <?php foreach ($voters as $voter): ?>
                                    <tr>
                                        <td>
                                            <div class="voter-photo-container" style="position: relative;">
                                                <img src="<?php echo extension_loaded('gd') ? getVoterPhotoUrl($voter['photo']) : simpleGetVoterPhotoUrl($voter['photo']); ?>"
                                                     alt="<?php echo htmlspecialchars($voter['name']); ?>"
                                                     class="voter-photo rounded-circle"
                                                     style="width: 50px; height: 50px; object-fit: cover; border: 2px solid #dee2e6;">
                                                <div class="photo-actions" style="position: absolute; top: -5px; right: -5px;">
                                                    <div class="btn-group-vertical">
                                                        <button type="button" class="btn btn-sm btn-primary rounded-circle p-1"
                                                                onclick="openPhotoUpload(<?php echo $voter['id']; ?>)"
                                                                title="ছবি আপলোড/পরিবর্তন">
                                                            <i class="fas fa-camera" style="font-size: 10px;"></i>
                                                        </button>
                                                        <?php if ($voter['photo']): ?>
                                                        <button type="button" class="btn btn-sm btn-danger rounded-circle p-1 mt-1"
                                                                onclick="deletePhoto(<?php echo $voter['id']; ?>)"
                                                                title="ছবি মুছুন">
                                                            <i class="fas fa-trash" style="font-size: 10px;"></i>
                                                        </button>
                                                        <?php endif; ?>
                                                    </div>
                                                </div>
                                            </div>
                                        </td>
                                        <td>
                                            <strong><?php echo htmlspecialchars($voter['voter_id']); ?></strong>
                                        </td>
                                        <td>
                                            <div>
                                                <strong><?php echo htmlspecialchars($voter['name_bn'] ?: $voter['name']); ?></strong>
                                                <?php if ($voter['name_bn'] && $voter['name']): ?>
                                                <br><small class="text-muted"><?php echo htmlspecialchars($voter['name']); ?></small>
                                                <?php endif; ?>
                                            </div>
                                        </td>
                                        <td><?php echo htmlspecialchars($voter['nid'] ?: 'N/A'); ?></td>
                                        <td><?php echo htmlspecialchars($voter['mobile'] ?: 'N/A'); ?></td>
                                        <td>
                                            <span class="badge bg-info">
                                                <?php echo htmlspecialchars($voter['area_name_bn'] ?: $voter['area_name']); ?>
                                            </span>
                                            <br><small class="text-muted"><?php echo ucfirst($voter['area_type']); ?></small>
                                        </td>
                                        <td>
                                            <?php if ($voter['linked_user']): ?>
                                                <span class="badge bg-success">
                                                    <i class="fas fa-link"></i> <?php echo htmlspecialchars($voter['linked_user']); ?>
                                                </span>
                                            <?php else: ?>
                                                <span class="badge bg-secondary">
                                                    <i class="fas fa-unlink"></i> No Link
                                                </span>
                                            <?php endif; ?>
                                        </td>
                                        <td>
                                            <small><?php echo formatDate($voter['created_at'], 'd/m/Y'); ?></small>
                                        </td>
                                        <td>
                                            <div class="btn-group btn-group-sm">
                                                <button class="btn btn-outline-primary btn-sm" 
                                                        onclick="editVoter(<?php echo $voter['id']; ?>)">
                                                    <i class="fas fa-edit"></i>
                                                </button>
                                                <button class="btn btn-outline-danger btn-sm" 
                                                        onclick="deleteVoter(<?php echo $voter['id']; ?>)">
                                                    <i class="fas fa-trash"></i>
                                                </button>
                                            </div>
                                        </td>
                                    </tr>
                                    <?php endforeach; ?>
                                <?php endif; ?>
                            </tbody>
                        </table>
                    </div>
                </div>
                
                <!-- Pagination -->
                <?php if ($total_pages > 1): ?>
                <div class="card-footer">
                    <nav>
                        <ul class="pagination justify-content-center mb-0">
                            <?php if ($page > 1): ?>
                            <li class="page-item">
                                <a class="page-link" href="?page=<?php echo $page-1; ?>&search=<?php echo urlencode($search); ?>&area=<?php echo $area_filter; ?>">
                                    <i class="fas fa-chevron-left"></i>
                                </a>
                            </li>
                            <?php endif; ?>
                            
                            <?php for ($i = max(1, $page-2); $i <= min($total_pages, $page+2); $i++): ?>
                            <li class="page-item <?php echo $i == $page ? 'active' : ''; ?>">
                                <a class="page-link" href="?page=<?php echo $i; ?>&search=<?php echo urlencode($search); ?>&area=<?php echo $area_filter; ?>">
                                    <?php echo $i; ?>
                                </a>
                            </li>
                            <?php endfor; ?>
                            
                            <?php if ($page < $total_pages): ?>
                            <li class="page-item">
                                <a class="page-link" href="?page=<?php echo $page+1; ?>&search=<?php echo urlencode($search); ?>&area=<?php echo $area_filter; ?>">
                                    <i class="fas fa-chevron-right"></i>
                                </a>
                            </li>
                            <?php endif; ?>
                        </ul>
                    </nav>
                </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>

<!-- Add Voter Modal -->
<div class="modal fade" id="addVoterModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">নতুন ভোটার যোগ করুন</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form method="POST" action="?action=add">
                <div class="modal-body">
                    <input type="hidden" name="csrf_token" value="<?php echo generateCSRFToken(); ?>">

                    <!-- Voter ID Section -->
                    <div class="row">
                        <div class="col-md-8">
                            <div class="form-floating mb-3">
                                <input type="text" class="form-control" id="add_voter_id" name="voter_id" required readonly>
                                <label>ভোটার আইডি *</label>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="mb-3">
                                <button type="button" class="btn btn-outline-primary w-100" onclick="generateNewVoterID()" style="height: 58px;">
                                    <i class="fas fa-refresh"></i><br>
                                    <small>নতুন আইডি</small>
                                </button>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-floating mb-3">
                                <input type="text" class="form-control" name="name" required>
                                <label>নাম (ইংরেজি) *</label>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-floating mb-3">
                                <input type="text" class="form-control" name="name_bn">
                                <label>নাম (বাংলা)</label>
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-floating mb-3">
                                <input type="text" class="form-control" name="nid" maxlength="20">
                                <label>এনআইডি নম্বর</label>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-floating mb-3">
                                <input type="tel" class="form-control" name="mobile" maxlength="15">
                                <label>মোবাইল নম্বর</label>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Hierarchical Area Selection -->
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-floating mb-3">
                                <select class="form-select" id="add_division" name="division_id">
                                    <option value="">বিভাগ নির্বাচন করুন</option>
                                </select>
                                <label>বিভাগ *</label>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-floating mb-3">
                                <select class="form-select" id="add_district" name="district_id" disabled>
                                    <option value="">জেলা নির্বাচন করুন</option>
                                </select>
                                <label>জেলা *</label>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-floating mb-3">
                                <select class="form-select" id="add_upazila" name="upazila_id" disabled>
                                    <option value="">উপজেলা নির্বাচন করুন</option>
                                </select>
                                <label>উপজেলা *</label>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-floating mb-3">
                                <select class="form-select" id="add_union" name="union_id" disabled>
                                    <option value="">ইউনিয়ন/পৌরসভা নির্বাচন করুন</option>
                                </select>
                                <label>ইউনিয়ন/পৌরসভা *</label>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-floating mb-3">
                                <select class="form-select" id="add_village" name="village_id" disabled>
                                    <option value="">গ্রাম/শহর নির্বাচন করুন</option>
                                </select>
                                <label>গ্রাম/শহর *</label>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-floating mb-3">
                                <select class="form-select" id="add_ward" name="area_id" disabled required>
                                    <option value="">ওয়ার্ড নির্বাচন করুন</option>
                                </select>
                                <label>ওয়ার্ড *</label>
                            </div>
                        </div>
                    </div>

                    <div class="alert alert-info">
                        <small>
                            <i class="fas fa-info-circle"></i>
                            <strong>নির্দেশনা:</strong> ক্রমানুসারে বিভাগ → জেলা → উপজেলা → ইউনিয়ন/পৌরসভা → গ্রাম/শহর → ওয়ার্ড নির্বাচন করুন
                        </small>
                    </div>
                    
                    <div class="form-floating mb-3">
                        <textarea class="form-control" name="address" style="height: 80px"></textarea>
                        <label>ঠিকানা</label>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">বাতিল</button>
                    <button type="submit" class="btn btn-primary">সংরক্ষণ করুন</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Upload Modal -->
<div class="modal fade" id="uploadModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">ভোটার তালিকা আপলোড</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form method="POST" action="?action=upload" enctype="multipart/form-data">
                <div class="modal-body">
                    <input type="hidden" name="csrf_token" value="<?php echo generateCSRFToken(); ?>">
                    
                    <div class="mb-3">
                        <label class="form-label">CSV ফাইল নির্বাচন করুন</label>
                        <input type="file" class="form-control" name="voter_file" accept=".csv" required>
                        <div class="form-text">
                            CSV ফরম্যাট: name, name_bn, nid, mobile, area_code, address
                        </div>
                    </div>
                    
                    <div class="alert alert-info">
                        <h6>CSV ফাইল ফরম্যাট:</h6>
                        <code>
                            name,name_bn,nid,mobile,area_code,address<br>
                            "John Doe","জন ডো","1234567890123","01712345678","WARD-01","ধানমন্ডি"
                        </code>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">বাতিল</button>
                    <button type="submit" class="btn btn-success">আপলোড করুন</button>
                </div>
            </form>
        </div>
    </div>
</div>

<?php
$inline_js = "
function editVoter(id) {
    // Get voter data via AJAX
    fetch('get-voter.php?id=' + id)
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                const voter = data.voter;

                // Populate basic edit form
                document.getElementById('edit_voter_id').value = voter.id;
                document.getElementById('edit_name').value = voter.name || '';
                document.getElementById('edit_name_bn').value = voter.name_bn || '';
                document.getElementById('edit_nid').value = voter.nid || '';
                document.getElementById('edit_mobile').value = voter.mobile || '';
                document.getElementById('edit_address').value = voter.address || '';

                // Set hierarchical area selection
                if (voter.area_id && editAreaSelector) {
                    editAreaSelector.setHierarchy(voter.area_id);
                }

                // Show modal
                const modal = new bootstrap.Modal(document.getElementById('editVoterModal'));
                modal.show();
            } else {
                alert('ভোটারের তথ্য লোড করতে ব্যর্থ: ' + data.message);
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('ভোটারের তথ্য লোড করতে ব্যর্থ');
        });
}

function deleteVoter(id) {
    if (confirm('আপনি কি নিশ্চিত যে এই ভোটার মুছে ফেলতে চান?\\n\\nসতর্কতা: এই কাজটি পূর্বাবস্থায় ফেরানো যাবে না।')) {
        const form = document.createElement('form');
        form.method = 'POST';
        form.action = '?action=delete';

        const csrfToken = document.createElement('input');
        csrfToken.type = 'hidden';
        csrfToken.name = 'csrf_token';
        csrfToken.value = '" . generateCSRFToken() . "';

        const voterIdInput = document.createElement('input');
        voterIdInput.type = 'hidden';
        voterIdInput.name = 'voter_id';
        voterIdInput.value = id;

        form.appendChild(csrfToken);
        form.appendChild(voterIdInput);
        document.body.appendChild(form);
        form.submit();
    }
}
";

?>

<!-- Photo Upload Modal -->
<div class="modal fade" id="photoUploadModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-camera"></i> ভোটার ছবি আপলোড
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form method="POST" enctype="multipart/form-data" action="?action=upload_photo">
                <div class="modal-body">
                    <input type="hidden" name="csrf_token" value="<?php echo generateCSRFToken(); ?>">
                    <input type="hidden" name="voter_id" id="photo_voter_id">

                    <div class="text-center mb-3">
                        <img id="photo_preview" src="" alt="Preview"
                             class="rounded-circle border"
                             style="width: 150px; height: 150px; object-fit: cover; display: none;">
                    </div>

                    <div class="mb-3">
                        <label for="photo" class="form-label">ছবি নির্বাচন করুন *</label>
                        <input type="file" class="form-control" id="photo" name="photo"
                               accept="image/*" required>
                        <div class="form-text">
                            <small>
                                <i class="fas fa-info-circle"></i>
                                সাপোর্টেড ফরম্যাট: JPG, PNG, GIF, WebP | সর্বোচ্চ সাইজ: 2MB
                            </small>
                        </div>
                    </div>

                    <div class="alert alert-info">
                        <i class="fas fa-lightbulb"></i>
                        <strong>টিপস:</strong>
                        <ul class="mb-0 mt-2">
                            <li>স্পষ্ট এবং উচ্চ মানের ছবি ব্যবহার করুন</li>
                            <li>ছবিতে শুধুমাত্র ভোটারের মুখ থাকা উচিত</li>
                            <li>ছবি স্বয়ংক্রিয়ভাবে 300x300 পিক্সেলে রিসাইজ হবে</li>
                        </ul>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                        বাতিল
                    </button>
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-upload"></i> আপলোড করুন
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Edit Voter Modal -->
<div class="modal fade" id="editVoterModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-edit"></i> ভোটার তথ্য সম্পাদনা
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form method="POST" action="?action=edit">
                <div class="modal-body">
                    <input type="hidden" name="csrf_token" value="<?php echo generateCSRFToken(); ?>">
                    <input type="hidden" name="voter_id" id="edit_voter_id">

                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-floating mb-3">
                                <input type="text" class="form-control" id="edit_name" name="name" required>
                                <label for="edit_name">নাম (ইংরেজি) *</label>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-floating mb-3">
                                <input type="text" class="form-control" id="edit_name_bn" name="name_bn">
                                <label for="edit_name_bn">নাম (বাংলা)</label>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-floating mb-3">
                                <input type="text" class="form-control" id="edit_nid" name="nid" required>
                                <label for="edit_nid">এনআইডি নম্বর *</label>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-floating mb-3">
                                <input type="tel" class="form-control" id="edit_mobile" name="mobile" required>
                                <label for="edit_mobile">মোবাইল নম্বর *</label>
                            </div>
                        </div>
                    </div>

                    <!-- Hierarchical Area Selection for Edit -->
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-floating mb-3">
                                <select class="form-select" id="edit_division" name="division_id">
                                    <option value="">বিভাগ নির্বাচন করুন</option>
                                </select>
                                <label>বিভাগ *</label>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-floating mb-3">
                                <select class="form-select" id="edit_district" name="district_id" disabled>
                                    <option value="">জেলা নির্বাচন করুন</option>
                                </select>
                                <label>জেলা *</label>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-floating mb-3">
                                <select class="form-select" id="edit_upazila" name="upazila_id" disabled>
                                    <option value="">উপজেলা নির্বাচন করুন</option>
                                </select>
                                <label>উপজেলা *</label>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-floating mb-3">
                                <select class="form-select" id="edit_union" name="union_id" disabled>
                                    <option value="">ইউনিয়ন/পৌরসভা নির্বাচন করুন</option>
                                </select>
                                <label>ইউনিয়ন/পৌরসভা *</label>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-floating mb-3">
                                <select class="form-select" id="edit_village" name="village_id" disabled>
                                    <option value="">গ্রাম/শহর নির্বাচন করুন</option>
                                </select>
                                <label>গ্রাম/শহর *</label>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-floating mb-3">
                                <select class="form-select" id="edit_ward" name="area_id" disabled required>
                                    <option value="">ওয়ার্ড নির্বাচন করুন</option>
                                </select>
                                <label>ওয়ার্ড *</label>
                            </div>
                        </div>
                    </div>

                    <div class="form-floating mb-3">
                        <textarea class="form-control" id="edit_address" name="address" style="height: 80px;"></textarea>
                        <label for="edit_address">ঠিকানা</label>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                        বাতিল
                    </button>
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-save"></i> সংরক্ষণ করুন
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Upload Help Modal -->
<div class="modal fade" id="uploadHelpModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-question-circle"></i> ভোটার আপলোড সহায়তা
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div class="row">
                    <div class="col-md-6">
                        <h6><i class="fas fa-file-csv"></i> CSV ফরম্যাট নির্দেশনা:</h6>
                        <ul class="list-group list-group-flush">
                            <li class="list-group-item">✅ UTF-8 encoding ব্যবহার করুন</li>
                            <li class="list-group-item">✅ প্রথম লাইনে headers রাখুন</li>
                            <li class="list-group-item">✅ Comma (,) দিয়ে আলাদা করুন</li>
                            <li class="list-group-item">✅ বাংলা টেক্সট সাপোর্টেড</li>
                        </ul>
                    </div>
                    <div class="col-md-6">
                        <h6><i class="fas fa-exclamation-triangle"></i> গুরুত্বপূর্ণ তথ্য:</h6>
                        <ul class="list-group list-group-flush">
                            <li class="list-group-item">⚠️ voter_id অবশ্যই unique হতে হবে</li>
                            <li class="list-group-item">⚠️ NID নম্বর unique হতে হবে</li>
                            <li class="list-group-item">⚠️ Mobile নম্বর unique হতে হবে</li>
                            <li class="list-group-item">⚠️ area_id অবশ্যই বিদ্যমান হতে হবে</li>
                        </ul>
                    </div>
                </div>

                <hr>

                <h6><i class="fas fa-table"></i> Required Fields (প্রয়োজনীয় ক্ষেত্র):</h6>
                <div class="table-responsive">
                    <table class="table table-sm">
                        <thead>
                            <tr>
                                <th>Field Name</th>
                                <th>বাংলা নাম</th>
                                <th>Example</th>
                                <th>Required</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td><code>voter_id</code></td>
                                <td>ভোটার আইডি</td>
                                <td>V001 (or leave empty)</td>
                                <td><span class="badge bg-success">Auto-Generated</span></td>
                            </tr>
                            <tr>
                                <td><code>name</code></td>
                                <td>নাম (ইংরেজি)</td>
                                <td>Md. Rahman Khan</td>
                                <td><span class="badge bg-danger">Required</span></td>
                            </tr>
                            <tr>
                                <td><code>name_bn</code></td>
                                <td>নাম (বাংলা)</td>
                                <td>মোঃ রহমান খান</td>
                                <td><span class="badge bg-secondary">Optional</span></td>
                            </tr>
                            <tr>
                                <td><code>nid</code></td>
                                <td>এনআইডি</td>
                                <td>1234567890123</td>
                                <td><span class="badge bg-danger">Required</span></td>
                            </tr>
                            <tr>
                                <td><code>mobile</code></td>
                                <td>মোবাইল</td>
                                <td>01712345678</td>
                                <td><span class="badge bg-success">Duplicate OK</span></td>
                            </tr>
                            <tr>
                                <td><code>division_name</code></td>
                                <td>বিভাগের নাম</td>
                                <td>ঢাকা বিভাগ</td>
                                <td><span class="badge bg-secondary">Optional</span></td>
                            </tr>
                            <tr>
                                <td><code>district_name</code></td>
                                <td>জেলার নাম</td>
                                <td>ঢাকা জেলা</td>
                                <td><span class="badge bg-secondary">Optional</span></td>
                            </tr>
                            <tr>
                                <td><code>upazila_name</code></td>
                                <td>উপজেলার নাম</td>
                                <td>ধানমন্ডি উপজেলা</td>
                                <td><span class="badge bg-secondary">Optional</span></td>
                            </tr>
                            <tr>
                                <td><code>union_name</code></td>
                                <td>ইউনিয়ন/পৌরসভার নাম</td>
                                <td>ধানমন্ডি পৌরসভা</td>
                                <td><span class="badge bg-secondary">Optional</span></td>
                            </tr>
                            <tr>
                                <td><code>village_name</code></td>
                                <td>গ্রাম/শহরের নাম</td>
                                <td>ধানমন্ডি গ্রাম</td>
                                <td><span class="badge bg-secondary">Optional</span></td>
                            </tr>
                            <tr>
                                <td><code>ward_name</code></td>
                                <td>ওয়ার্ডের নাম</td>
                                <td>ধানমন্ডি ওয়ার্ড ০১</td>
                                <td><span class="badge bg-secondary">Optional</span></td>
                            </tr>
                            <tr>
                                <td><code>ward_id</code></td>
                                <td>ওয়ার্ড আইডি</td>
                                <td>23</td>
                                <td><span class="badge bg-danger">Required</span></td>
                            </tr>
                            <tr>
                                <td><code>gender</code></td>
                                <td>লিঙ্গ</td>
                                <td>male/female</td>
                                <td><span class="badge bg-secondary">Optional</span></td>
                            </tr>
                            <tr>
                                <td><code>date_of_birth</code></td>
                                <td>জন্ম তারিখ</td>
                                <td>1985-05-15</td>
                                <td><span class="badge bg-secondary">Optional</span></td>
                            </tr>
                        </tbody>
                    </table>
                </div>

                <div class="alert alert-info">
                    <h6><i class="fas fa-lightbulb"></i> টিপস:</h6>
                    <ul class="mb-0">
                        <li>প্রথমে <strong>টেমপ্লেট ডাউনলোড</strong> করুন</li>
                        <li><strong>voter_id খালি রাখুন</strong> - automatically generate হবে</li>
                        <li><strong>এলাকা তালিকা</strong> ডাউনলোড করে সঠিক ward_id ব্যবহার করুন</li>
                        <li>Excel এ edit করার পর <strong>CSV format এ save</strong> করুন</li>
                        <li>বাংলা টেক্সট এর জন্য <strong>UTF-8 encoding</strong> ব্যবহার করুন</li>
                    </ul>
                </div>

                <div class="alert alert-success">
                    <h6><i class="fas fa-magic"></i> নতুন ফিচার: Ward-Based Auto Voter ID Generation!</h6>
                    <ul class="mb-0">
                        <li>✅ <strong>voter_id column খালি রাখুন</strong> - system automatically unique ID generate করবে</li>
                        <li>✅ <strong>Ward-based format:</strong> V{WardCode}{Year}{Random} (e.g., VDH0120241234)</li>
                        <li>✅ <strong>Same ward = Same code:</strong> একই ওয়ার্ডের সব ভোটারের ID একই কোড দিয়ে শুরু</li>
                        <li>✅ <strong>Uniqueness guaranteed:</strong> Duplicate ID হবে না</li>
                        <li>🏛️ <strong>Easy grouping:</strong> Ward অনুযায়ী ভোটার সহজে খুঁজে পাওয়া যাবে</li>
                    </ul>
                </div>

                <div class="alert alert-warning">
                    <h6><i class="fas fa-shield-alt"></i> Duplicate Check Policy:</h6>
                    <ul class="mb-0">
                        <li>🔒 <strong>NID:</strong> Duplicate হলে error - একই এনআইডি দুইবার ব্যবহার করা যাবে না</li>
                        <li>✅ <strong>Mobile:</strong> Duplicate OK - একই মোবাইল নম্বর একাধিক ভোটারের হতে পারে</li>
                        <li>✅ <strong>Voter ID:</strong> Auto-generated, always unique - কোন duplicate হবে না</li>
                        <li>ℹ️ <strong>কারণ:</strong> পারিবারিক মোবাইল নম্বর ব্যবহারের সুবিধার জন্য</li>
                    </ul>
                </div>
            </div>
            <div class="modal-footer">
                <a href="download-template.php?format=csv" class="btn btn-primary">
                    <i class="fas fa-download"></i> টেমপ্লেট ডাউনলোড
                </a>
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                    বন্ধ করুন
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Upload Result Modal -->
<div class="modal fade" id="uploadResultModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header" id="uploadResultHeader">
                <h5 class="modal-title" id="uploadResultTitle">
                    <i class="fas fa-upload"></i> আপলোড ফলাফল
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body" id="uploadResultBody">
                <!-- Content will be populated by JavaScript -->
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                    <i class="fas fa-times"></i> বন্ধ করুন
                </button>
                <button type="button" class="btn btn-primary" onclick="location.reload()">
                    <i class="fas fa-refresh"></i> পেজ রিফ্রেশ করুন
                </button>
            </div>
        </div>
    </div>
</div>

<style>
.voter-photo-container:hover .photo-actions {
    opacity: 1;
}

.photo-actions {
    opacity: 0;
    transition: opacity 0.3s ease;
}

.voter-photo-container:hover .photo-actions {
    opacity: 1;
}

.voter-photo {
    transition: transform 0.3s ease;
}

.voter-photo:hover {
    transform: scale(1.1);
}

@media (max-width: 768px) {
    .voter-photo-container {
        text-align: center;
    }

    .photo-actions {
        opacity: 1;
        position: static !important;
        margin-top: 5px;
    }

    .photo-actions .btn-group-vertical {
        flex-direction: row;
    }
}

/* Upload Result Modal Styles */
.bg-light-success {
    background-color: #d1e7dd !important;
}

.bg-light-danger {
    background-color: #f8d7da !important;
}

.modal-lg {
    max-width: 800px;
}

.list-group-numbered {
    counter-reset: section;
}

.list-group-numbered > .list-group-item::before {
    counter-increment: section;
    content: counter(section);
    position: absolute;
    left: 10px;
    top: 50%;
    transform: translateY(-50%);
    background: #dc3545;
    color: white;
    border-radius: 50%;
    width: 20px;
    height: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 12px;
    font-weight: bold;
}

.list-group-numbered > .list-group-item {
    padding-left: 40px;
}

.upload-stats {
    display: flex;
    justify-content: space-around;
    text-align: center;
}

.upload-stats .stat-item {
    flex: 1;
    padding: 15px;
}

.upload-stats .stat-item i {
    font-size: 2rem;
    margin-bottom: 10px;
}

.error-translation {
    font-size: 0.9em;
    color: #6c757d;
    font-style: italic;
}
</style>

<script>
// Photo upload functionality
function openPhotoUpload(voterId) {
    document.getElementById('photo_voter_id').value = voterId;
    document.getElementById('photo_preview').style.display = 'none';
    document.getElementById('photo').value = '';

    const modal = new bootstrap.Modal(document.getElementById('photoUploadModal'));
    modal.show();
}

function deletePhoto(voterId) {
    if (confirm('আপনি কি এই ভোটারের ছবি মুছে ফেলতে চান?')) {
        const form = document.createElement('form');
        form.method = 'POST';
        form.action = '?action=delete_photo';

        const csrfToken = document.createElement('input');
        csrfToken.type = 'hidden';
        csrfToken.name = 'csrf_token';
        csrfToken.value = '<?php echo generateCSRFToken(); ?>';

        const voterIdInput = document.createElement('input');
        voterIdInput.type = 'hidden';
        voterIdInput.name = 'voter_id';
        voterIdInput.value = voterId;

        form.appendChild(csrfToken);
        form.appendChild(voterIdInput);
        document.body.appendChild(form);
        form.submit();
    }
}

// Photo preview
document.getElementById('photo').addEventListener('change', function(e) {
    const file = e.target.files[0];
    const preview = document.getElementById('photo_preview');

    if (file) {
        // Validate file type
        const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/webp'];
        if (!allowedTypes.includes(file.type)) {
            alert('অবৈধ ফাইল টাইপ! শুধুমাত্র JPG, PNG, GIF, WebP ফাইল গ্রহণযোগ্য।');
            e.target.value = '';
            preview.style.display = 'none';
            return;
        }

        // Validate file size (2MB)
        if (file.size > 2 * 1024 * 1024) {
            alert('ফাইল সাইজ 2MB এর বেশি হতে পারবে না!');
            e.target.value = '';
            preview.style.display = 'none';
            return;
        }

        // Show preview
        const reader = new FileReader();
        reader.onload = function(e) {
            preview.src = e.target.result;
            preview.style.display = 'block';
        };
        reader.readAsDataURL(file);
    } else {
        preview.style.display = 'none';
    }
});

// Upload result data from PHP
<?php if ($upload_result): ?>
const uploadResultData = <?php echo json_encode($upload_result); ?>;
<?php endif; ?>

// Hierarchical Area Selection System
class HierarchicalAreaSelector {
    constructor(prefix = 'add') {
        this.prefix = prefix;
        this.selectors = {
            division: `#${prefix}_division`,
            district: `#${prefix}_district`,
            upazila: `#${prefix}_upazila`,
            union: `#${prefix}_union`,
            village: `#${prefix}_village`,
            ward: `#${prefix}_ward`
        };
        this.init();
    }

    init() {
        this.loadDivisions();
        this.bindEvents();
    }

    async loadDivisions() {
        try {
            const response = await fetch('../api/get-areas.php?action=get_all_divisions');
            const data = await response.json();

            if (data.success) {
                const divisionSelect = document.querySelector(this.selectors.division);
                divisionSelect.innerHTML = '<option value="">বিভাগ নির্বাচন করুন</option>';

                data.divisions.forEach(division => {
                    const option = document.createElement('option');
                    option.value = division.id;
                    option.textContent = division.name_bn || division.name;
                    divisionSelect.appendChild(option);
                });
            }
        } catch (error) {
            console.error('Error loading divisions:', error);
        }
    }

    async loadChildren(parentId, targetSelector, placeholder) {
        try {
            const response = await fetch(`../api/get-areas.php?action=get_children&parent_id=${parentId}`);
            const data = await response.json();

            if (data.success) {
                const targetSelect = document.querySelector(targetSelector);
                targetSelect.innerHTML = `<option value="">${placeholder}</option>`;

                data.areas.forEach(area => {
                    const option = document.createElement('option');
                    option.value = area.id;
                    option.textContent = area.name_bn || area.name;
                    targetSelect.appendChild(option);
                });

                targetSelect.disabled = false;
            }
        } catch (error) {
            console.error('Error loading children:', error);
        }
    }

    resetDownstream(fromLevel) {
        const levels = ['district', 'upazila', 'union', 'village', 'ward'];
        const startIndex = levels.indexOf(fromLevel);

        for (let i = startIndex; i < levels.length; i++) {
            const select = document.querySelector(this.selectors[levels[i]]);
            select.innerHTML = `<option value="">${this.getPlaceholder(levels[i])}</option>`;
            select.disabled = true;
        }
    }

    getPlaceholder(level) {
        const placeholders = {
            district: 'জেলা নির্বাচন করুন',
            upazila: 'উপজেলা নির্বাচন করুন',
            union: 'ইউনিয়ন/পৌরসভা নির্বাচন করুন',
            village: 'গ্রাম/শহর নির্বাচন করুন',
            ward: 'ওয়ার্ড নির্বাচন করুন'
        };
        return placeholders[level] || 'নির্বাচন করুন';
    }

    bindEvents() {
        // Division change
        document.querySelector(this.selectors.division).addEventListener('change', (e) => {
            const divisionId = e.target.value;
            this.resetDownstream('district');

            if (divisionId) {
                this.loadChildren(divisionId, this.selectors.district, 'জেলা নির্বাচন করুন');
            }
        });

        // District change
        document.querySelector(this.selectors.district).addEventListener('change', (e) => {
            const districtId = e.target.value;
            this.resetDownstream('upazila');

            if (districtId) {
                this.loadChildren(districtId, this.selectors.upazila, 'উপজেলা নির্বাচন করুন');
            }
        });

        // Upazila change
        document.querySelector(this.selectors.upazila).addEventListener('change', (e) => {
            const upazilaId = e.target.value;
            this.resetDownstream('union');

            if (upazilaId) {
                this.loadChildren(upazilaId, this.selectors.union, 'ইউনিয়ন/পৌরসভা নির্বাচন করুন');
            }
        });

        // Union change
        document.querySelector(this.selectors.union).addEventListener('change', (e) => {
            const unionId = e.target.value;
            this.resetDownstream('village');

            if (unionId) {
                this.loadChildren(unionId, this.selectors.village, 'গ্রাম/শহর নির্বাচন করুন');
            }
        });

        // Village change
        document.querySelector(this.selectors.village).addEventListener('change', (e) => {
            const villageId = e.target.value;
            this.resetDownstream('ward');

            if (villageId) {
                this.loadChildren(villageId, this.selectors.ward, 'ওয়ার্ড নির্বাচন করুন');
            }
        });
    }

    async setHierarchy(wardId) {
        try {
            const response = await fetch(`../api/get-areas.php?action=get_hierarchy&area_id=${wardId}`);
            const data = await response.json();

            if (data.success && data.hierarchy.length > 0) {
                const hierarchy = data.hierarchy;

                // Set values based on hierarchy
                for (const area of hierarchy) {
                    switch (area.type) {
                        case 'division':
                            document.querySelector(this.selectors.division).value = area.id;
                            await this.loadChildren(area.id, this.selectors.district, 'জেলা নির্বাচন করুন');
                            break;
                        case 'district':
                            document.querySelector(this.selectors.district).value = area.id;
                            await this.loadChildren(area.id, this.selectors.upazila, 'উপজেলা নির্বাচন করুন');
                            break;
                        case 'upazila':
                            document.querySelector(this.selectors.upazila).value = area.id;
                            await this.loadChildren(area.id, this.selectors.union, 'ইউনিয়ন/পৌরসভা নির্বাচন করুন');
                            break;
                        case 'union':
                        case 'municipality':
                            document.querySelector(this.selectors.union).value = area.id;
                            await this.loadChildren(area.id, this.selectors.village, 'গ্রাম/শহর নির্বাচন করুন');
                            break;
                        case 'village':
                        case 'city':
                            document.querySelector(this.selectors.village).value = area.id;
                            await this.loadChildren(area.id, this.selectors.ward, 'ওয়ার্ড নির্বাচন করুন');
                            break;
                        case 'ward':
                            document.querySelector(this.selectors.ward).value = area.id;
                            break;
                    }
                }
            }
        } catch (error) {
            console.error('Error setting hierarchy:', error);
        }
    }
}

// Initialize hierarchical selectors
let addAreaSelector, editAreaSelector;

// Voter ID Generation Functions
async function generateNewVoterID() {
    try {
        const response = await fetch('../api/generate-voter-id.php?action=generate');
        const data = await response.json();

        if (data.success) {
            document.getElementById('add_voter_id').value = data.voter_id;

            // Show success feedback
            const button = event.target.closest('button');
            const originalHTML = button.innerHTML;
            button.innerHTML = '<i class="fas fa-check text-success"></i><br><small>Generated!</small>';
            button.disabled = true;

            setTimeout(() => {
                button.innerHTML = originalHTML;
                button.disabled = false;
            }, 2000);
        } else {
            alert('Failed to generate voter ID: ' + data.message);
        }
    } catch (error) {
        console.error('Error generating voter ID:', error);
        alert('Error generating voter ID');
    }
}

async function generateVoterIDWithArea() {
    const wardSelect = document.getElementById('add_ward');
    const areaId = wardSelect.value;

    if (!areaId) {
        alert('Please select a ward first');
        return;
    }

    try {
        const response = await fetch(`../api/generate-voter-id.php?action=generate&area_id=${areaId}&format=V{WARD_CODE}{YYYY}{RRRRR}`);
        const data = await response.json();

        if (data.success) {
            document.getElementById('add_voter_id').value = data.voter_id;

            // Show ward code info
            const wardCode = data.voter_id.substring(1, data.voter_id.length - 9); // Extract ward code
            showWardCodeInfo(wardCode, areaId);
        } else {
            alert('Failed to generate voter ID: ' + data.message);
        }
    } catch (error) {
        console.error('Error generating voter ID:', error);
        alert('Error generating voter ID');
    }
}

function showWardCodeInfo(wardCode, areaId) {
    // Create or update ward code info display
    let infoDiv = document.getElementById('ward-code-info');
    if (!infoDiv) {
        infoDiv = document.createElement('div');
        infoDiv.id = 'ward-code-info';
        infoDiv.className = 'alert alert-info mt-2';

        const voterIdInput = document.getElementById('add_voter_id');
        voterIdInput.parentNode.appendChild(infoDiv);
    }

    infoDiv.innerHTML = `
        <small>
            <i class="fas fa-info-circle"></i>
            <strong>Ward Code:</strong> <code>${wardCode}</code> -
            এই ওয়ার্ডের সব ভোটারের ID <code>V${wardCode}...</code> দিয়ে শুরু হবে
        </small>
    `;
}

async function validateVoterID(voterId) {
    if (!voterId) return;

    try {
        const response = await fetch(`../api/generate-voter-id.php?action=validate&voter_id=${encodeURIComponent(voterId)}`);
        const data = await response.json();

        if (data.success) {
            const validation = data.validation;
            const input = document.getElementById('add_voter_id');

            if (validation.valid && validation.unique) {
                input.classList.remove('is-invalid');
                input.classList.add('is-valid');
            } else {
                input.classList.remove('is-valid');
                input.classList.add('is-invalid');

                // Show validation issues
                console.log('Validation issues:', validation.issues);
            }
        }
    } catch (error) {
        console.error('Error validating voter ID:', error);
    }
}

// Auto-generate voter ID when modal opens
function autoGenerateVoterID() {
    const voterIdInput = document.getElementById('add_voter_id');
    if (!voterIdInput.value) {
        generateNewVoterID();
    }
}

// Upload Result Modal Functions
function showUploadResultModal(result) {
    const modal = document.getElementById('uploadResultModal');
    const header = document.getElementById('uploadResultHeader');
    const title = document.getElementById('uploadResultTitle');
    const body = document.getElementById('uploadResultBody');

    // Set modal style based on result
    if (result.success) {
        header.className = 'modal-header bg-success text-white';
        title.innerHTML = '<i class="fas fa-check-circle"></i> আপলোড সফল হয়েছে';
    } else {
        header.className = 'modal-header bg-danger text-white';
        title.innerHTML = '<i class="fas fa-exclamation-triangle"></i> আপলোড ব্যর্থ হয়েছে';
    }

    // Generate modal content
    let content = generateUploadResultContent(result);
    body.innerHTML = content;

    // Show modal
    const bootstrapModal = new bootstrap.Modal(modal);
    bootstrapModal.show();
}

function generateUploadResultContent(result) {
    let html = '';

    // Summary section
    html += '<div class="row mb-4">';
    html += '<div class="col-12">';
    html += '<div class="card border-0 ' + (result.success ? 'bg-light-success' : 'bg-light-danger') + '">';
    html += '<div class="card-body text-center">';
    html += '<h4 class="mb-3">' + result.message + '</h4>';

    if (result.details) {
        html += '<div class="row">';
        html += '<div class="col-md-4">';
        html += '<div class="text-success">';
        html += '<i class="fas fa-check-circle fa-2x mb-2"></i>';
        html += '<h5>' + (result.details.success || 0) + '</h5>';
        html += '<small>সফল</small>';
        html += '</div>';
        html += '</div>';
        html += '<div class="col-md-4">';
        html += '<div class="text-danger">';
        html += '<i class="fas fa-times-circle fa-2x mb-2"></i>';
        html += '<h5>' + (result.details.errors || 0) + '</h5>';
        html += '<small>ব্যর্থ</small>';
        html += '</div>';
        html += '</div>';
        html += '<div class="col-md-4">';
        html += '<div class="text-info">';
        html += '<i class="fas fa-file-csv fa-2x mb-2"></i>';
        html += '<h5>' + (result.details.total_lines || 0) + '</h5>';
        html += '<small>মোট লাইন</small>';
        html += '</div>';
        html += '</div>';
        html += '</div>';
    }

    html += '</div>';
    html += '</div>';
    html += '</div>';
    html += '</div>';

    // Error details section
    if (result.details && result.details.error_list && result.details.error_list.length > 0) {
        html += '<div class="row">';
        html += '<div class="col-12">';
        html += '<div class="card border-warning">';
        html += '<div class="card-header bg-warning text-dark">';
        html += '<h6 class="mb-0"><i class="fas fa-exclamation-triangle"></i> ত্রুটির বিস্তারিত</h6>';
        html += '</div>';
        html += '<div class="card-body" style="max-height: 300px; overflow-y: auto;">';

        html += '<div class="alert alert-info">';
        html += '<small><i class="fas fa-info-circle"></i> <strong>নোট:</strong> প্রতিটি ত্রুটি লাইন নম্বর সহ দেখানো হয়েছে। CSV ফাইল সংশোধন করে পুনরায় আপলোড করুন।</small>';
        html += '</div>';

        html += '<ol class="list-group list-group-numbered">';
        result.details.error_list.forEach(function(error, index) {
            if (index < 20) { // Show max 20 errors
                html += '<li class="list-group-item d-flex justify-content-between align-items-start">';
                html += '<div class="ms-2 me-auto">';
                html += '<div class="fw-bold text-danger">' + translateError(error) + '</div>';
                html += '<small class="text-muted">' + error + '</small>';
                html += '</div>';
                html += '<span class="badge bg-danger rounded-pill">' + (index + 1) + '</span>';
                html += '</li>';
            }
        });

        if (result.details.error_list.length > 20) {
            html += '<li class="list-group-item text-center text-muted">';
            html += '<i class="fas fa-ellipsis-h"></i> আরও ' + (result.details.error_list.length - 20) + ' টি ত্রুটি রয়েছে';
            html += '</li>';
        }

        html += '</ol>';
        html += '</div>';
        html += '</div>';
        html += '</div>';
        html += '</div>';
    }

    // Success details section
    if (result.success && result.details && result.details.success > 0) {
        html += '<div class="row mt-3">';
        html += '<div class="col-12">';
        html += '<div class="card border-success">';
        html += '<div class="card-header bg-success text-white">';
        html += '<h6 class="mb-0"><i class="fas fa-check-circle"></i> সফল আপলোড</h6>';
        html += '</div>';
        html += '<div class="card-body">';
        html += '<div class="alert alert-success">';
        html += '<h6><i class="fas fa-thumbs-up"></i> অভিনন্দন!</h6>';
        html += '<p class="mb-0">' + result.details.success + ' জন ভোটার সফলভাবে সিস্টেমে যোগ করা হয়েছে।</p>';
        if (result.details.format_detected) {
            html += '<small class="text-muted">ফরম্যাট: ' + result.details.format_detected + '</small>';
        }
        html += '</div>';
        html += '</div>';
        html += '</div>';
        html += '</div>';
        html += '</div>';
    }

    // Recommendations section
    html += '<div class="row mt-3">';
    html += '<div class="col-12">';
    html += '<div class="card border-info">';
    html += '<div class="card-header bg-info text-white">';
    html += '<h6 class="mb-0"><i class="fas fa-lightbulb"></i> পরবর্তী পদক্ষেপ</h6>';
    html += '</div>';
    html += '<div class="card-body">';

    if (result.success) {
        html += '<ul class="list-unstyled mb-0">';
        html += '<li><i class="fas fa-check text-success"></i> ভোটার তালিকা দেখুন এবং যাচাই করুন</li>';
        html += '<li><i class="fas fa-camera text-info"></i> প্রয়োজনে ভোটারদের ছবি আপলোড করুন</li>';
        html += '<li><i class="fas fa-edit text-warning"></i> কোন তথ্য সংশোধনের প্রয়োজন হলে এডিট করুন</li>';
        html += '</ul>';
    } else {
        html += '<ul class="list-unstyled mb-0">';
        html += '<li><i class="fas fa-download text-primary"></i> নতুন টেমপ্লেট ডাউনলোড করুন</li>';
        html += '<li><i class="fas fa-edit text-warning"></i> CSV ফাইল সংশোধন করুন</li>';
        html += '<li><i class="fas fa-upload text-success"></i> পুনরায় আপলোড করুন</li>';
        html += '</ul>';
    }

    html += '</div>';
    html += '</div>';
    html += '</div>';
    html += '</div>';

    return html;
}

function translateError(error) {
    const translations = {
        'Invalid NID format': 'অবৈধ এনআইডি ফরম্যাট',
        'Invalid mobile format': 'অবৈধ মোবাইল নম্বর ফরম্যাট',
        'Ward ID not found': 'ওয়ার্ড আইডি পাওয়া যায়নি',
        'NID.*already exists': 'এনআইডি ইতিমধ্যে বিদ্যমান',
        'Missing required fields': 'প্রয়োজনীয় তথ্য অনুপস্থিত',
        'Insufficient columns': 'পর্যাপ্ত কলাম নেই',
        'Empty required fields': 'প্রয়োজনীয় ক্ষেত্র খালি',
        'Required columns missing': 'প্রয়োজনীয় কলাম অনুপস্থিত',
        'Failed to generate voter_id': 'ভোটার আইডি তৈরি করতে ব্যর্থ'
    };

    for (let key in translations) {
        if (error.includes(key) || error.match(new RegExp(key, 'i'))) {
            return translations[key];
        }
    }

    return error; // Return original if no translation found
}

document.addEventListener('DOMContentLoaded', function() {
    // Initialize for add modal
    addAreaSelector = new HierarchicalAreaSelector('add');

    // Initialize for edit modal
    editAreaSelector = new HierarchicalAreaSelector('edit');

    // Auto-generate voter ID when add modal opens
    const addModal = document.getElementById('addVoterModal');
    if (addModal) {
        addModal.addEventListener('shown.bs.modal', function() {
            autoGenerateVoterID();
        });
    }

    // Validate voter ID on input
    const voterIdInput = document.getElementById('add_voter_id');
    if (voterIdInput) {
        voterIdInput.addEventListener('input', function() {
            const voterId = this.value.trim();
            if (voterId.length >= 3) {
                validateVoterID(voterId);
            }
        });
    }

    // Check for upload result to show modal
    const urlParams = new URLSearchParams(window.location.search);
    if (urlParams.get('show_upload_result') === '1') {
        // Remove the parameter from URL
        window.history.replaceState({}, document.title, window.location.pathname);

        // Show upload result modal if data exists
        if (typeof uploadResultData !== 'undefined') {
            showUploadResultModal(uploadResultData);
        }
    }
});
</script>

<?php
// Helper functions for voter management

function updateVoter($voter_id, $data) {
    try {
        $db = getDB();

        // Validate input
        $name = sanitizeInput($data['name'] ?? '');
        $name_bn = sanitizeInput($data['name_bn'] ?? '');
        $nid = sanitizeInput($data['nid'] ?? '');
        $mobile = sanitizeInput($data['mobile'] ?? '');
        $area_id = intval($data['area_id'] ?? 0);
        $address = sanitizeInput($data['address'] ?? '');

        if (empty($name) || empty($nid) || empty($mobile) || $area_id <= 0) {
            return ['success' => false, 'message' => 'সব প্রয়োজনীয় তথ্য পূরণ করুন'];
        }

        // Check if NID or mobile already exists for other voters
        $stmt = $db->prepare("SELECT id FROM voters WHERE (nid = ? OR mobile = ?) AND id != ? AND is_active = 1");
        $stmt->execute([$nid, $mobile, $voter_id]);
        if ($stmt->fetch()) {
            return ['success' => false, 'message' => 'এই এনআইডি বা মোবাইল নম্বর অন্য ভোটারের জন্য ব্যবহৃত হয়েছে'];
        }

        // Update voter
        $stmt = $db->prepare("
            UPDATE voters
            SET name = ?, name_bn = ?, nid = ?, mobile = ?, area_id = ?, address = ?, updated_at = NOW()
            WHERE id = ?
        ");

        $stmt->execute([$name, $name_bn, $nid, $mobile, $area_id, $address, $voter_id]);

        logActivity('UPDATE', 'voter', $voter_id);

        return ['success' => true, 'message' => 'ভোটারের তথ্য সফলভাবে আপডেট হয়েছে'];

    } catch (Exception $e) {
        error_log("Failed to update voter: " . $e->getMessage());
        return ['success' => false, 'message' => 'ভোটার আপডেট করতে ব্যর্থ: ' . $e->getMessage()];
    }
}

function deleteVoter($voter_id) {
    try {
        $db = getDB();

        // Check if voter exists
        $stmt = $db->prepare("SELECT voter_id, name, photo FROM voters WHERE id = ? AND is_active = 1");
        $stmt->execute([$voter_id]);
        $voter = $stmt->fetch();

        if (!$voter) {
            return ['success' => false, 'message' => 'ভোটার পাওয়া যায়নি'];
        }

        // Check if voter has voted
        $stmt = $db->prepare("SELECT COUNT(*) FROM votes WHERE voter_id = ?");
        $stmt->execute([$voter['voter_id']]);
        $vote_count = $stmt->fetchColumn();

        if ($vote_count > 0) {
            return ['success' => false, 'message' => 'এই ভোটার ইতিমধ্যে ভোট দিয়েছেন, মুছে ফেলা যাবে না'];
        }

        // Delete photo if exists
        if ($voter['photo']) {
            $photo_path = __DIR__ . '/../uploads/voters/' . $voter['photo'];
            if (file_exists($photo_path)) {
                unlink($photo_path);
            }
        }

        // Soft delete voter
        $stmt = $db->prepare("UPDATE voters SET is_active = 0, updated_at = NOW() WHERE id = ?");
        $stmt->execute([$voter_id]);

        logActivity('DELETE', 'voter', $voter_id);

        return ['success' => true, 'message' => 'ভোটার সফলভাবে মুছে ফেলা হয়েছে'];

    } catch (Exception $e) {
        error_log("Failed to delete voter: " . $e->getMessage());
        return ['success' => false, 'message' => 'ভোটার মুছতে ব্যর্থ: ' . $e->getMessage()];
    }
}

include '../includes/footer.php';
?>

<?php
// Helper functions for voter management

function addVoter($data) {
    try {
        $db = getDB();

        // Use provided voter_id or generate new one
        $voter_id = sanitizeInput($data['voter_id'] ?? '');

        if (empty($voter_id)) {
            // Generate voter ID with ward code if area is provided
            $area_id = intval($data['area_id'] ?? 0);
            if ($area_id > 0) {
                $voter_id = generateVoterIDWithArea($db, $area_id, 'V{WARD_CODE}{YYYY}{RRRRR}');
            } else {
                $voter_id = generateVoterID($db, 'V{YYYY}{MM}{RRRRR}');
            }
        }
        // Note: No need to check voter_id duplicates as auto-generated IDs are always unique

        // Clean and validate mobile number
        $mobile = sanitizeInput($data['mobile'] ?? '');
        if (!empty($mobile)) {
            $clean_mobile = preg_replace('/[^0-9]/', '', $mobile);
            if (strlen($clean_mobile) == 11 && preg_match('/^01/', $clean_mobile)) {
                $mobile = $clean_mobile;
            } else {
                return ['success' => false, 'message' => 'অবৈধ মোবাইল নম্বর ফরম্যাট'];
            }
        }

        // Clean and validate NID
        $nid = sanitizeInput($data['nid'] ?? '');
        if (!empty($nid)) {
            $clean_nid = preg_replace('/[^0-9]/', '', $nid);
            if (strlen($clean_nid) >= 10 && strlen($clean_nid) <= 17) {
                $nid = $clean_nid;

                // Check for NID duplicates only
                $stmt = $db->prepare("SELECT id, voter_id FROM voters WHERE nid = ?");
                $stmt->execute([$nid]);
                $existing = $stmt->fetch();
                if ($existing) {
                    return ['success' => false, 'message' => "এনআইডি '{$nid}' ইতিমধ্যে ভোটার {$existing['voter_id']} এর জন্য ব্যবহৃত হয়েছে"];
                }
            } else {
                return ['success' => false, 'message' => 'অবৈধ এনআইডি ফরম্যাট'];
            }
        }

        // Insert voter with all fields
        $stmt = $db->prepare("
            INSERT INTO voters (
                voter_id, name, name_bn, father_name, mother_name,
                nid, mobile, email, date_of_birth, gender,
                area_id, address, created_by, created_at
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, NOW())
        ");

        $stmt->execute([
            $voter_id,
            sanitizeInput($data['name']),
            sanitizeInput($data['name_bn']) ?: null,
            sanitizeInput($data['father_name']) ?: null,
            sanitizeInput($data['mother_name']) ?: null,
            $nid ?: null,
            $mobile ?: null,
            sanitizeInput($data['email']) ?: null,
            sanitizeInput($data['date_of_birth']) ?: null,
            sanitizeInput($data['gender']) ?: null,
            intval($data['area_id']),
            sanitizeInput($data['address']) ?: null,
            getCurrentUser()['id']
        ]);
        
        logActivity('CREATE', 'voter', $db->lastInsertId());
        
        return ['success' => true, 'message' => 'ভোটার সফলভাবে যোগ করা হয়েছে'];
        
    } catch (Exception $e) {
        error_log("Failed to add voter: " . $e->getMessage());
        return ['success' => false, 'message' => 'ভোটার যোগ করতে ব্যর্থ: ' . $e->getMessage()];
    }
}

function uploadVoters($file) {
    if (!$file || $file['error'] !== UPLOAD_ERR_OK) {
        return ['success' => false, 'message' => 'ফাইল আপলোড করতে ব্যর্থ'];
    }
    
    if (pathinfo($file['name'], PATHINFO_EXTENSION) !== 'csv') {
        return ['success' => false, 'message' => 'শুধুমাত্র CSV ফাইল গ্রহণযোগ্য'];
    }
    
    try {
        $db = getDB();
        $handle = fopen($file['tmp_name'], 'r');
        
        if (!$handle) {
            return ['success' => false, 'message' => 'ফাইল পড়তে ব্যর্থ'];
        }
        
        // Skip header row
        fgetcsv($handle);
        
        $success_count = 0;
        $error_count = 0;
        $errors = [];
        
        while (($row = fgetcsv($handle)) !== false) {
            if (count($row) < 6) continue;
            
            try {
                // Get area by code
                $stmt = $db->prepare("SELECT id FROM areas WHERE code = ?");
                $stmt->execute([$row[4]]);
                $area = $stmt->fetch();
                
                if (!$area) {
                    $errors[] = "Area code '{$row[4]}' not found for {$row[0]}";
                    $error_count++;
                    continue;
                }
                
                // Generate voter ID
                $stmt = $db->query("SELECT COUNT(*) + 1 as next_id FROM voters");
                $next_id = $stmt->fetch()['next_id'];
                $voter_id = 'V' . str_pad($next_id, 3, '0', STR_PAD_LEFT);
                
                // Insert voter
                $stmt = $db->prepare("
                    INSERT INTO voters (voter_id, name, name_bn, nid, mobile, area_id, address, created_by) 
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?)
                ");
                
                $stmt->execute([
                    $voter_id,
                    trim($row[0]),
                    trim($row[1]) ?: null,
                    trim($row[2]) ?: null,
                    trim($row[3]) ?: null,
                    $area['id'],
                    trim($row[5]) ?: null,
                    getCurrentUser()['id']
                ]);
                
                $success_count++;
                
            } catch (Exception $e) {
                $errors[] = "Error importing {$row[0]}: " . $e->getMessage();
                $error_count++;
            }
        }
        
        fclose($handle);
        
        $message = "আপলোড সম্পন্ন: {$success_count} সফল, {$error_count} ব্যর্থ";
        if (!empty($errors)) {
            $message .= "\nErrors: " . implode(', ', array_slice($errors, 0, 5));
        }
        
        return ['success' => true, 'message' => $message];
        
    } catch (Exception $e) {
        return ['success' => false, 'message' => 'আপলোড ব্যর্থ: ' . $e->getMessage()];
    }
}
?>
