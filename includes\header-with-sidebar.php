<!DOCTYPE html>
<html lang="bn">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo isset($page_title) ? htmlspecialchars($page_title) . ' - ' . APP_NAME : APP_NAME; ?></title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    
    <!-- Chart.js -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    
    <!-- Custom CSS -->
    <link href="<?php echo APP_URL; ?>/assets/css/style.css" rel="stylesheet">
    
    <!-- Meta tags -->
    <meta name="description" content="<?php echo isset($page_description) ? htmlspecialchars($page_description) : 'Dynamic Realtime Online Voting System'; ?>">
    <meta name="keywords" content="voting, polls, surveys, online voting, real-time results">
    <meta name="author" content="<?php echo APP_NAME; ?>">
    
    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="<?php echo APP_URL; ?>/assets/images/favicon.ico">
    
    <!-- CSRF Token for AJAX requests -->
    <meta name="csrf-token" content="<?php echo generateCSRFToken(); ?>">
    
    <!-- App Configuration for JavaScript -->
    <script>
        window.APP_CONFIG = {
            url: '<?php echo APP_URL; ?>',
            ajaxPollInterval: <?php echo AJAX_POLL_INTERVAL; ?>,
            enableRealTime: <?php echo ENABLE_REAL_TIME ? 'true' : 'false'; ?>,
            csrfToken: '<?php echo generateCSRFToken(); ?>',
            isLoggedIn: <?php echo isLoggedIn() ? 'true' : 'false'; ?>,
            currentUser: <?php echo isLoggedIn() ? json_encode(getCurrentUser()) : 'null'; ?>
        };
    </script>
    
    <!-- Additional head content -->
    <?php if (isset($additional_head)): ?>
        <?php echo $additional_head; ?>
    <?php endif; ?>
</head>
<body class="sidebar-layout">
    
    <!-- Include Modern Sidebar -->
    <?php include_once __DIR__ . '/modern-sidebar.php'; ?>
    
    <!-- Main Content Area -->
    <div class="main-content" id="mainContent">
        <!-- Flash Messages -->
        <?php 
        $flash_messages = getFlashMessages();
        if (!empty($flash_messages)): 
        ?>
        <div class="flash-messages">
            <div class="container-fluid">
                <?php foreach ($flash_messages as $message): ?>
                <div class="alert alert-<?php echo $message['type']; ?> alert-dismissible fade show" role="alert">
                    <?php echo htmlspecialchars($message['message']); ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
                <?php endforeach; ?>
            </div>
        </div>
        <?php endif; ?>

        <!-- Top Navigation Bar (optional) -->
        <?php if (isset($show_top_nav) && $show_top_nav): ?>
        <nav class="top-navbar">
            <div class="container-fluid">
                <div class="d-flex justify-content-between align-items-center py-3">
                    <div class="page-title">
                        <h4 class="mb-0" style="color: white; text-shadow: 1px 1px 2px rgba(0,0,0,0.3);">
                            <?php echo isset($page_title) ? htmlspecialchars($page_title) : 'Dashboard'; ?>
                        </h4>
                        <?php if (isset($page_subtitle)): ?>
                        <small style="color: rgba(255,255,255,0.8);"><?php echo htmlspecialchars($page_subtitle); ?></small>
                        <?php endif; ?>
                    </div>
                    <div class="top-nav-actions">
                        <!-- Quick Actions -->
                        <div class="d-flex gap-2 align-items-center">
                            <!-- Notifications -->
                            <div class="dropdown">
                                <button class="btn btn-outline-light btn-sm dropdown-toggle" type="button" data-bs-toggle="dropdown">
                                    <i class="fas fa-bell"></i>
                                    <span class="badge bg-danger" id="notificationCount" style="display: none;"></span>
                                </button>
                                <ul class="dropdown-menu dropdown-menu-end">
                                    <li><h6 class="dropdown-header">নোটিফিকেশন</h6></li>
                                    <li><hr class="dropdown-divider"></li>
                                    <li><a class="dropdown-item" href="#">কোন নতুন নোটিফিকেশন নেই</a></li>
                                </ul>
                            </div>
                            
                            <!-- Quick Create -->
                            <?php if (isLoggedIn()): ?>
                            <div class="dropdown">
                                <button class="btn btn-success btn-sm dropdown-toggle" type="button" data-bs-toggle="dropdown">
                                    <i class="fas fa-plus"></i> তৈরি করুন
                                </button>
                                <ul class="dropdown-menu dropdown-menu-end">
                                    <li><a class="dropdown-item" href="<?php echo APP_URL; ?>/polls/create.php">
                                        <i class="fas fa-poll"></i> নতুন পোল
                                    </a></li>
                                    <?php if (isAdmin()): ?>
                                    <li><a class="dropdown-item" href="<?php echo APP_URL; ?>/admin/voters.php">
                                        <i class="fas fa-user-plus"></i> নতুন ভোটার
                                    </a></li>
                                    <li><a class="dropdown-item" href="<?php echo APP_URL; ?>/admin/candidates.php">
                                        <i class="fas fa-user-tie"></i> নতুন প্রার্থী
                                    </a></li>
                                    <?php endif; ?>
                                </ul>
                            </div>
                            <?php endif; ?>
                            
                            <!-- User Menu -->
                            <?php if (isLoggedIn()): ?>
                            <div class="dropdown">
                                <button class="btn btn-outline-light btn-sm dropdown-toggle" type="button" data-bs-toggle="dropdown">
                                    <i class="fas fa-user"></i> <?php echo htmlspecialchars(getCurrentUser()['username'] ?? 'User'); ?>
                                </button>
                                <ul class="dropdown-menu dropdown-menu-end">
                                    <li><a class="dropdown-item" href="<?php echo APP_URL; ?>/profile.php">
                                        <i class="fas fa-user"></i> প্রোফাইল
                                    </a></li>
                                    <li><a class="dropdown-item" href="<?php echo APP_URL; ?>/settings.php">
                                        <i class="fas fa-cog"></i> সেটিংস
                                    </a></li>
                                    <li><hr class="dropdown-divider"></li>
                                    <li><a class="dropdown-item" href="<?php echo APP_URL; ?>/auth/logout.php">
                                        <i class="fas fa-sign-out-alt"></i> লগআউট
                                    </a></li>
                                </ul>
                            </div>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            </div>
        </nav>
        <?php endif; ?>

        <!-- Page Content -->
        <main class="page-content">
            <?php if (isset($content)): ?>
                <?php echo $content; ?>
            <?php else: ?>
                <!-- Content will be included here -->
            <?php endif; ?>
        </main>
    </div>

    <!-- Footer -->
    <footer class="sidebar-footer bg-light py-3 mt-auto">
        <div class="container-fluid">
            <div class="row align-items-center">
                <div class="col-md-6">
                    <p class="mb-0 text-muted">
                        &copy; <?php echo date('Y'); ?> <?php echo APP_NAME; ?>. সকল অধিকার সংরক্ষিত।
                    </p>
                </div>
                <div class="col-md-6 text-md-end">
                    <small class="text-muted">
                        Version <?php echo APP_VERSION; ?> | 
                        <a href="<?php echo APP_URL; ?>/help.php" class="text-decoration-none">সাহায্য</a> |
                        <a href="<?php echo APP_URL; ?>/contact.php" class="text-decoration-none">যোগাযোগ</a>
                    </small>
                </div>
            </div>
        </div>
    </footer>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <!-- jQuery -->
    <script src="https://code.jquery.com/jquery-3.7.0.min.js"></script>
    
    <!-- Custom JS -->
    <script src="<?php echo APP_URL; ?>/assets/js/app.js"></script>
    
    <!-- Additional scripts -->
    <?php if (isset($additional_scripts)): ?>
        <?php echo $additional_scripts; ?>
    <?php endif; ?>
    
    <!-- Inline JavaScript -->
    <?php if (isset($inline_js)): ?>
    <script>
        $(document).ready(function() {
            <?php echo $inline_js; ?>
        });
    </script>
    <?php endif; ?>

</body>
</html>
