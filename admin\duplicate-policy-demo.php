<?php
/**
 * Duplicate Policy Demo
 * Demonstrate new duplicate check policy (NID only)
 */

require_once '../config/config.php';

// Check if user is admin
if (!isLoggedIn() || !isAdmin()) {
    redirect(APP_URL . '/auth/login.php');
}

echo "<h2>🔒 Duplicate Check Policy Demo</h2>";

if (isset($_POST['demo_duplicate'])) {
    echo "<h3>📋 Duplicate Check Demo Results:</h3>";
    
    // Simulate CSV data with duplicates
    $demo_data = [
        ['', '<PERSON> Khan', 'রহমান খান', '1234567890123', '01712345678', 1], // Original
        ['', '<PERSON><PERSON> Begum', 'সালমা বেগম', '2345678901234', '01712345678', 2], // Same mobile - OK
        ['', '<PERSON>', 'কামাল হোসেন', '1234567890123', '01934567890', 3], // Same NID - ERROR
        ['', '<PERSON><PERSON><PERSON> Akter', 'নাসরিন আক্তার', '3456789012345', '01712345678', 4], // Same mobile - OK
        ['', 'Aminul Islam', 'আমিনুল ইসলাম', '*************', '01756789012', 5], // All unique - OK
    ];
    
    echo "<div style='background: #e7f3ff; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
    echo "<h4>Processing Demo Data:</h4>";
    echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
    echo "<tr style='background: #f8f9fa;'>";
    echo "<th style='padding: 10px;'>Name</th>";
    echo "<th style='padding: 10px;'>NID</th>";
    echo "<th style='padding: 10px;'>Mobile</th>";
    echo "<th style='padding: 10px;'>Status</th>";
    echo "<th style='padding: 10px;'>Reason</th>";
    echo "</tr>";
    
    $processed_nids = [];
    
    foreach ($demo_data as $index => $row) {
        $name = $row[1];
        $nid = $row[3];
        $mobile = $row[4];
        
        // Check NID duplicate
        if (in_array($nid, $processed_nids)) {
            $status = '❌ Rejected';
            $reason = 'NID duplicate detected';
            $color = '#f8d7da';
        } else {
            $status = '✅ Accepted';
            $reason = 'All checks passed';
            $color = '#d4edda';
            $processed_nids[] = $nid;
        }
        
        echo "<tr style='background: $color;'>";
        echo "<td style='padding: 10px;'>" . htmlspecialchars($name) . "</td>";
        echo "<td style='padding: 10px;'><code>$nid</code></td>";
        echo "<td style='padding: 10px;'><code>$mobile</code></td>";
        echo "<td style='padding: 10px;'>$status</td>";
        echo "<td style='padding: 10px;'>$reason</td>";
        echo "</tr>";
    }
    
    echo "</table>";
    echo "</div>";
    
    echo "<div style='background: #fff3cd; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
    echo "<h4>📊 Summary:</h4>";
    echo "<ul>";
    echo "<li><strong>Total Records:</strong> " . count($demo_data) . "</li>";
    echo "<li><strong>Accepted:</strong> " . count($processed_nids) . "</li>";
    echo "<li><strong>Rejected:</strong> " . (count($demo_data) - count($processed_nids)) . "</li>";
    echo "<li><strong>Mobile Duplicates:</strong> 3 (All allowed)</li>";
    echo "<li><strong>NID Duplicates:</strong> 1 (Rejected)</li>";
    echo "</ul>";
    echo "</div>";
}

// Show current policy
echo "<h3>📋 Current Duplicate Check Policy:</h3>";
echo "<div style='background: #f8f9fa; padding: 15px; border-radius: 5px; margin: 10px 0;'>";

echo "<div class='row'>";
echo "<div class='col-md-4'>";
echo "<div class='card border-danger'>";
echo "<div class='card-header bg-danger text-white text-center'>";
echo "<h6 class='mb-0'><i class='fas fa-ban'></i> NID - Strict Check</h6>";
echo "</div>";
echo "<div class='card-body text-center'>";
echo "<h4 class='text-danger'>🔒 NO DUPLICATES</h4>";
echo "<p class='mb-0'>একই এনআইডি দুইবার ব্যবহার করা যাবে না</p>";
echo "<small class='text-muted'>কারণ: জাতীয় পরিচয়পত্র unique হতে হবে</small>";
echo "</div>";
echo "</div>";
echo "</div>";

echo "<div class='col-md-4'>";
echo "<div class='card border-success'>";
echo "<div class='card-header bg-success text-white text-center'>";
echo "<h6 class='mb-0'><i class='fas fa-check'></i> Mobile - Flexible</h6>";
echo "</div>";
echo "<div class='card-body text-center'>";
echo "<h4 class='text-success'>✅ DUPLICATES OK</h4>";
echo "<p class='mb-0'>একই মোবাইল একাধিক ভোটারের হতে পারে</p>";
echo "<small class='text-muted'>কারণ: পারিবারিক মোবাইল নম্বর</small>";
echo "</div>";
echo "</div>";
echo "</div>";

echo "<div class='col-md-4'>";
echo "<div class='card border-primary'>";
echo "<div class='card-header bg-primary text-white text-center'>";
echo "<h6 class='mb-0'><i class='fas fa-magic'></i> Voter ID - Auto</h6>";
echo "</div>";
echo "<div class='card-body text-center'>";
echo "<h4 class='text-primary'>🎲 AUTO-GENERATED</h4>";
echo "<p class='mb-0'>System automatically unique ID তৈরি করে</p>";
echo "<small class='text-muted'>কারণ: Random generation ensures uniqueness</small>";
echo "</div>";
echo "</div>";
echo "</div>";
echo "</div>";

echo "</div>";

// Show examples
echo "<h3>💡 Real-world Examples:</h3>";
echo "<div style='background: #e7f3ff; padding: 15px; border-radius: 5px; margin: 10px 0;'>";

echo "<h4>✅ Allowed Scenarios:</h4>";
echo "<ul>";
echo "<li><strong>পারিবারিক মোবাইল:</strong> বাবা-মা-সন্তান সবার একই মোবাইল নম্বর</li>";
echo "<li><strong>অফিস মোবাইল:</strong> একই অফিসের কয়েকজনের একই মোবাইল</li>";
echo "<li><strong>গ্রামীণ এলাকা:</strong> একটি মোবাইল পুরো পরিবারের জন্য</li>";
echo "</ul>";

echo "<h4>❌ Blocked Scenarios:</h4>";
echo "<ul>";
echo "<li><strong>একই এনআইডি:</strong> দুইজন ভোটারের একই জাতীয় পরিচয়পত্র নম্বর</li>";
echo "<li><strong>ভুল এনআইডি:</strong> টাইপিং মিসটেক বা copy-paste error</li>";
echo "<li><strong>Duplicate entry:</strong> একই ব্যক্তি দুইবার entry</li>";
echo "</ul>";

echo "</div>";
?>

<!DOCTYPE html>
<html>
<head>
    <title>Duplicate Policy Demo</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body>

<div class="container mt-4">
    <div class="row">
        <div class="col-md-8">
            <div class="card">
                <div class="card-header">
                    <h5>🧪 Demo Duplicate Check</h5>
                </div>
                <div class="card-body">
                    <p>This demo shows how the new duplicate check policy works - only NID duplicates are blocked.</p>
                    
                    <form method="POST">
                        <button type="submit" name="demo_duplicate" class="btn btn-primary">
                            <i class="fas fa-play"></i> Run Duplicate Check Demo
                        </button>
                    </form>
                    
                    <hr>
                    
                    <h6>Demo Data (CSV simulation):</h6>
                    <div class="table-responsive">
                        <table class="table table-sm table-bordered">
                            <thead>
                                <tr>
                                    <th>Name</th>
                                    <th>NID</th>
                                    <th>Mobile</th>
                                    <th>Expected Result</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr class="table-success">
                                    <td>Rahman Khan</td>
                                    <td>1234567890123</td>
                                    <td>01712345678</td>
                                    <td>✅ Accept (First entry)</td>
                                </tr>
                                <tr class="table-success">
                                    <td>Salma Begum</td>
                                    <td>2345678901234</td>
                                    <td>01712345678</td>
                                    <td>✅ Accept (Mobile duplicate OK)</td>
                                </tr>
                                <tr class="table-danger">
                                    <td>Kamal Hossain</td>
                                    <td>1234567890123</td>
                                    <td>01934567890</td>
                                    <td>❌ Reject (NID duplicate)</td>
                                </tr>
                                <tr class="table-success">
                                    <td>Nasreen Akter</td>
                                    <td>3456789012345</td>
                                    <td>01712345678</td>
                                    <td>✅ Accept (Mobile duplicate OK)</td>
                                </tr>
                                <tr class="table-success">
                                    <td>Aminul Islam</td>
                                    <td>*************</td>
                                    <td>01756789012</td>
                                    <td>✅ Accept (All unique)</td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-md-4">
            <div class="card">
                <div class="card-header">
                    <h5>🔧 Quick Actions</h5>
                </div>
                <div class="card-body">
                    <a href="test-template.php" class="btn btn-success w-100 mb-2">
                        <i class="fas fa-download"></i> Download Test Template
                    </a>
                    <a href="voters.php" class="btn btn-secondary w-100 mb-2">
                        <i class="fas fa-users"></i> Back to Voters
                    </a>
                    <a href="debug-upload.php" class="btn btn-warning w-100">
                        <i class="fas fa-bug"></i> Debug Upload
                    </a>
                </div>
            </div>
            
            <div class="card mt-3">
                <div class="card-header">
                    <h5>📋 Policy Benefits</h5>
                </div>
                <div class="card-body">
                    <h6>✅ Advantages:</h6>
                    <ul>
                        <li>Family mobile sharing</li>
                        <li>Rural area convenience</li>
                        <li>Office phone usage</li>
                        <li>Reduced upload errors</li>
                    </ul>
                    
                    <h6>🔒 Security:</h6>
                    <ul>
                        <li>NID uniqueness maintained</li>
                        <li>Identity verification intact</li>
                        <li>Voter ID auto-generated</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
</div>

</body>
</html>
