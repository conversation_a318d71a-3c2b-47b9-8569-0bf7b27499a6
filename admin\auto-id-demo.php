<?php
/**
 * Auto ID Generation Demo
 * Demonstrate auto voter ID generation during upload
 */

require_once '../config/config.php';
require_once '../includes/voter-id-generator.php';

// Check if user is admin
if (!isLoggedIn() || !isAdmin()) {
    redirect(APP_URL . '/auth/login.php');
}

echo "<h2>🎲 Auto Voter ID Generation Demo</h2>";

if (isset($_POST['demo_upload'])) {
    echo "<h3>📋 Demo Upload Results:</h3>";
    
    // Simulate CSV data with empty voter_ids
    $demo_data = [
        ['', '<PERSON>', 'রহমান খান', '1234567890123', '01712345678', 1],
        ['', '<PERSON><PERSON>', 'সালমা বেগম', '2345678901234', '01823456789', 2],
        ['', '<PERSON>', 'কামাল হোসেন', '3456789012345', '01934567890', 3],
        ['V999', 'Manual ID', 'ম্যানুয়াল আইডি', '4567890123456', '01645678901', 4], // Manual ID
        ['', 'Aminul Islam', 'আমিনুল ইসলাম', '5678901234567', '01756789012', 5],
    ];
    
    try {
        $db = getDB();
        
        echo "<div style='background: #e7f3ff; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
        echo "<h4>Processing Demo Data:</h4>";
        echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
        echo "<tr style='background: #f8f9fa;'>";
        echo "<th style='padding: 10px;'>Original voter_id</th>";
        echo "<th style='padding: 10px;'>Name</th>";
        echo "<th style='padding: 10px;'>Generated voter_id</th>";
        echo "<th style='padding: 10px;'>Status</th>";
        echo "</tr>";
        
        foreach ($demo_data as $index => $row) {
            $original_id = $row[0];
            $name = $row[1];
            $ward_id = $row[5];
            
            // Simulate auto-generation logic
            if (empty($original_id)) {
                $generated_id = generateVoterIDWithArea($db, $ward_id, 'V{YYYY}{MM}{RRRRR}');
                $status = '✅ Auto-Generated';
                $color = '#d4edda';
            } else {
                $generated_id = $original_id;
                $status = '📝 Manual ID Used';
                $color = '#fff3cd';
            }
            
            echo "<tr style='background: $color;'>";
            echo "<td style='padding: 10px;'>" . ($original_id ?: '<em>Empty</em>') . "</td>";
            echo "<td style='padding: 10px;'>" . htmlspecialchars($name) . "</td>";
            echo "<td style='padding: 10px;'><strong>$generated_id</strong></td>";
            echo "<td style='padding: 10px;'>$status</td>";
            echo "</tr>";
        }
        
        echo "</table>";
        echo "</div>";
        
    } catch (Exception $e) {
        echo "<div style='background: #f8d7da; padding: 15px; border-radius: 5px;'>";
        echo "<h4>❌ Error: " . $e->getMessage() . "</h4>";
        echo "</div>";
    }
}

// Show current auto-generation settings
try {
    $db = getDB();
    
    echo "<h3>⚙️ Auto-Generation Settings:</h3>";
    echo "<div style='background: #f8f9fa; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
    
    echo "<h4>Current Format:</h4>";
    echo "<ul>";
    echo "<li><strong>Pattern:</strong> <code>V{YYYY}{MM}{RRRRR}</code></li>";
    echo "<li><strong>Example:</strong> <code>" . generateVoterIDPattern('V{YYYY}{MM}{RRRRR}') . "</code></li>";
    echo "<li><strong>Description:</strong> V + Year + Month + 5-digit random number</li>";
    echo "</ul>";
    
    echo "<h4>Generation Rules:</h4>";
    echo "<ul>";
    echo "<li>✅ If <code>voter_id</code> column is <strong>empty</strong> → Auto-generate</li>";
    echo "<li>✅ If <code>voter_id</code> has value → Use provided ID</li>";
    echo "<li>✅ Check uniqueness → No duplicate IDs allowed</li>";
    echo "<li>✅ Area-based → Uses ward information for generation</li>";
    echo "</ul>";
    
    echo "</div>";
    
    // Show recent auto-generated IDs
    $stmt = $db->query("
        SELECT voter_id, name, created_at 
        FROM voters 
        WHERE voter_id REGEXP '^V[0-9]{4}[0-9]{2}[0-9]{5}$' 
        ORDER BY created_at DESC 
        LIMIT 10
    ");
    $recent_ids = $stmt->fetchAll();
    
    if (!empty($recent_ids)) {
        echo "<h4>Recent Auto-Generated IDs:</h4>";
        echo "<div style='background: #d4edda; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
        echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
        echo "<tr style='background: #c3e6cb;'>";
        echo "<th style='padding: 10px;'>Voter ID</th>";
        echo "<th style='padding: 10px;'>Name</th>";
        echo "<th style='padding: 10px;'>Created</th>";
        echo "</tr>";
        
        foreach ($recent_ids as $voter) {
            echo "<tr>";
            echo "<td style='padding: 10px;'><code>" . $voter['voter_id'] . "</code></td>";
            echo "<td style='padding: 10px;'>" . htmlspecialchars($voter['name']) . "</td>";
            echo "<td style='padding: 10px;'>" . date('Y-m-d H:i', strtotime($voter['created_at'])) . "</td>";
            echo "</tr>";
        }
        
        echo "</table>";
        echo "</div>";
    }
    
} catch (Exception $e) {
    echo "<div style='background: #f8d7da; padding: 15px; border-radius: 5px;'>";
    echo "<h4>❌ Database Error: " . $e->getMessage() . "</h4>";
    echo "</div>";
}
?>

<!DOCTYPE html>
<html>
<head>
    <title>Auto ID Generation Demo</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body>

<div class="container mt-4">
    <div class="row">
        <div class="col-md-8">
            <div class="card">
                <div class="card-header">
                    <h5>🧪 Demo Auto ID Generation</h5>
                </div>
                <div class="card-body">
                    <p>This demo shows how voter IDs are automatically generated during CSV upload when the voter_id column is empty.</p>
                    
                    <form method="POST">
                        <button type="submit" name="demo_upload" class="btn btn-primary">
                            <i class="fas fa-play"></i> Run Demo Upload
                        </button>
                    </form>
                    
                    <hr>
                    
                    <h6>Demo Data (CSV simulation):</h6>
                    <div class="table-responsive">
                        <table class="table table-sm table-bordered">
                            <thead>
                                <tr>
                                    <th>voter_id</th>
                                    <th>name</th>
                                    <th>name_bn</th>
                                    <th>nid</th>
                                    <th>mobile</th>
                                    <th>ward_id</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td><em style="color: #6c757d;">empty</em></td>
                                    <td>Rahman Khan</td>
                                    <td>রহমান খান</td>
                                    <td>1234567890123</td>
                                    <td>01712345678</td>
                                    <td>1</td>
                                </tr>
                                <tr>
                                    <td><em style="color: #6c757d;">empty</em></td>
                                    <td>Salma Begum</td>
                                    <td>সালমা বেগম</td>
                                    <td>2345678901234</td>
                                    <td>01823456789</td>
                                    <td>2</td>
                                </tr>
                                <tr>
                                    <td><em style="color: #6c757d;">empty</em></td>
                                    <td>Kamal Hossain</td>
                                    <td>কামাল হোসেন</td>
                                    <td>3456789012345</td>
                                    <td>01934567890</td>
                                    <td>3</td>
                                </tr>
                                <tr style="background: #fff3cd;">
                                    <td><strong>V999</strong></td>
                                    <td>Manual ID</td>
                                    <td>ম্যানুয়াল আইডি</td>
                                    <td>4567890123456</td>
                                    <td>01645678901</td>
                                    <td>4</td>
                                </tr>
                                <tr>
                                    <td><em style="color: #6c757d;">empty</em></td>
                                    <td>Aminul Islam</td>
                                    <td>আমিনুল ইসলাম</td>
                                    <td>5678901234567</td>
                                    <td>01756789012</td>
                                    <td>5</td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-md-4">
            <div class="card">
                <div class="card-header">
                    <h5>🔧 Quick Actions</h5>
                </div>
                <div class="card-body">
                    <a href="test-template.php" class="btn btn-success w-100 mb-2">
                        <i class="fas fa-download"></i> Download Test Template
                    </a>
                    <a href="voter-id-generator.php" class="btn btn-info w-100 mb-2">
                        <i class="fas fa-magic"></i> ID Generator Tool
                    </a>
                    <a href="voters.php" class="btn btn-secondary w-100 mb-2">
                        <i class="fas fa-users"></i> Back to Voters
                    </a>
                    <a href="debug-upload.php" class="btn btn-warning w-100">
                        <i class="fas fa-bug"></i> Debug Upload
                    </a>
                </div>
            </div>
            
            <div class="card mt-3">
                <div class="card-header">
                    <h5>📋 How It Works</h5>
                </div>
                <div class="card-body">
                    <ol>
                        <li><strong>Check voter_id:</strong> Is it empty?</li>
                        <li><strong>Auto-generate:</strong> Create unique ID</li>
                        <li><strong>Use ward info:</strong> Area-based generation</li>
                        <li><strong>Validate:</strong> Ensure uniqueness</li>
                        <li><strong>Insert:</strong> Save to database</li>
                    </ol>
                </div>
            </div>
        </div>
    </div>
</div>

</body>
</html>
