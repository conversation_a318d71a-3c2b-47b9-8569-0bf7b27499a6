<?php
/**
 * Main Landing Page
 * Dynamic Realtime Online Voting System
 */

require_once 'config/config.php';

// Get recent public polls
try {
    $db = getDB();
    $stmt = $db->prepare("
        SELECT p.*, u.username as creator_name,
               (SELECT COUNT(*) FROM votes WHERE poll_id = p.id) as vote_count
        FROM polls p 
        JOIN users u ON p.created_by = u.id 
        WHERE p.is_public = 1 AND p.is_active = 1 
        AND (p.end_date IS NULL OR p.end_date > NOW())
        ORDER BY p.created_at DESC 
        LIMIT 6
    ");
    $stmt->execute();
    $recent_polls = $stmt->fetchAll();
} catch (Exception $e) {
    $recent_polls = [];
    error_log("Failed to fetch recent polls: " . $e->getMessage());
}

// Get site statistics
try {
    $db = getDB();
    
    // Total polls
    $stmt = $db->query("SELECT COUNT(*) as count FROM polls WHERE is_public = 1");
    $total_polls = $stmt->fetch()['count'];
    
    // Total votes
    $stmt = $db->query("SELECT COUNT(*) as count FROM votes");
    $total_votes = $stmt->fetch()['count'];
    
    // Total users
    $stmt = $db->query("SELECT COUNT(*) as count FROM users WHERE is_active = 1");
    $total_users = $stmt->fetch()['count'];
    
    // Active polls
    $stmt = $db->query("
        SELECT COUNT(*) as count FROM polls 
        WHERE is_public = 1 AND is_active = 1 
        AND (end_date IS NULL OR end_date > NOW())
    ");
    $active_polls = $stmt->fetch()['count'];
    
} catch (Exception $e) {
    $total_polls = $total_votes = $total_users = $active_polls = 0;
    error_log("Failed to fetch statistics: " . $e->getMessage());
}

$page_title = "Welcome to " . APP_NAME;
include 'includes/header.php';
?>

<div class="hero-section bg-primary text-white py-5 mb-5">
    <div class="container">
        <div class="row align-items-center">
            <div class="col-lg-6">
                <h1 class="display-4 fw-bold mb-4">Dynamic Realtime Online Voting</h1>
                <p class="lead mb-4">Create polls, gather opinions, and see results in real-time. Perfect for surveys, elections, feedback collection, and decision making.</p>
                <div class="d-flex gap-3">
                    <?php if (isLoggedIn()): ?>
                        <a href="polls/create.php" class="btn btn-light btn-lg">
                            <i class="fas fa-plus"></i> Create Poll
                        </a>
                        <a href="polls/manage.php" class="btn btn-outline-light btn-lg">
                            <i class="fas fa-list"></i> My Polls
                        </a>
                    <?php else: ?>
                        <a href="auth/register.php" class="btn btn-light btn-lg">
                            <i class="fas fa-user-plus"></i> Get Started
                        </a>
                        <a href="auth/login.php" class="btn btn-outline-light btn-lg">
                            <i class="fas fa-sign-in-alt"></i> Login
                        </a>
                    <?php endif; ?>
                </div>
            </div>
            <div class="col-lg-6">
                <div class="row text-center">
                    <div class="col-6 mb-3">
                        <div class="bg-white bg-opacity-10 rounded p-3">
                            <h3 class="fw-bold"><?php echo number_format($total_polls); ?></h3>
                            <p class="mb-0">Total Polls</p>
                        </div>
                    </div>
                    <div class="col-6 mb-3">
                        <div class="bg-white bg-opacity-10 rounded p-3">
                            <h3 class="fw-bold"><?php echo number_format($total_votes); ?></h3>
                            <p class="mb-0">Votes Cast</p>
                        </div>
                    </div>
                    <div class="col-6">
                        <div class="bg-white bg-opacity-10 rounded p-3">
                            <h3 class="fw-bold"><?php echo number_format($total_users); ?></h3>
                            <p class="mb-0">Active Users</p>
                        </div>
                    </div>
                    <div class="col-6">
                        <div class="bg-white bg-opacity-10 rounded p-3">
                            <h3 class="fw-bold"><?php echo number_format($active_polls); ?></h3>
                            <p class="mb-0">Active Polls</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="container">
    <!-- Features Section -->
    <div class="row mb-5">
        <div class="col-12">
            <h2 class="text-center mb-5">Why Choose Our Voting System?</h2>
        </div>
        <div class="col-md-4 mb-4">
            <div class="text-center">
                <div class="feature-icon bg-primary text-white rounded-circle d-inline-flex align-items-center justify-content-center mb-3" style="width: 80px; height: 80px;">
                    <i class="fas fa-bolt fa-2x"></i>
                </div>
                <h4>Real-time Results</h4>
                <p>See vote counts update instantly as people participate in your polls.</p>
            </div>
        </div>
        <div class="col-md-4 mb-4">
            <div class="text-center">
                <div class="feature-icon bg-success text-white rounded-circle d-inline-flex align-items-center justify-content-center mb-3" style="width: 80px; height: 80px;">
                    <i class="fas fa-palette fa-2x"></i>
                </div>
                <h4>Customizable</h4>
                <p>Personalize your polls with custom themes, colors, and branding options.</p>
            </div>
        </div>
        <div class="col-md-4 mb-4">
            <div class="text-center">
                <div class="feature-icon bg-info text-white rounded-circle d-inline-flex align-items-center justify-content-center mb-3" style="width: 80px; height: 80px;">
                    <i class="fas fa-shield-alt fa-2x"></i>
                </div>
                <h4>Secure & Anonymous</h4>
                <p>Support for both authenticated and anonymous voting with security measures.</p>
            </div>
        </div>
    </div>

    <!-- Recent Polls Section -->
    <?php if (!empty($recent_polls)): ?>
    <div class="row mb-5">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h2>Recent Polls</h2>
                <a href="polls/" class="btn btn-outline-primary">View All Polls</a>
            </div>
        </div>
        <?php foreach ($recent_polls as $poll): ?>
        <div class="col-md-6 col-lg-4 mb-4">
            <div class="card h-100">
                <div class="card-body">
                    <h5 class="card-title"><?php echo htmlspecialchars($poll['title']); ?></h5>
                    <p class="card-text text-muted">
                        <?php echo htmlspecialchars(substr($poll['description'] ?? '', 0, 100)); ?>
                        <?php if (strlen($poll['description'] ?? '') > 100): ?>...<?php endif; ?>
                    </p>
                    <div class="d-flex justify-content-between align-items-center">
                        <small class="text-muted">
                            <i class="fas fa-user"></i> <?php echo htmlspecialchars($poll['creator_name']); ?>
                        </small>
                        <small class="text-muted">
                            <i class="fas fa-vote-yea"></i> <?php echo $poll['vote_count']; ?> votes
                        </small>
                    </div>
                </div>
                <div class="card-footer">
                    <a href="polls/view.php?id=<?php echo $poll['id']; ?>" class="btn btn-primary btn-sm">
                        <i class="fas fa-eye"></i> View & Vote
                    </a>
                    <small class="text-muted float-end">
                        <?php echo timeAgo($poll['created_at']); ?>
                    </small>
                </div>
            </div>
        </div>
        <?php endforeach; ?>
    </div>
    <?php endif; ?>

    <!-- Call to Action -->
    <?php if (!isLoggedIn()): ?>
    <div class="row">
        <div class="col-12">
            <div class="bg-light rounded p-5 text-center">
                <h3>Ready to Get Started?</h3>
                <p class="lead">Join thousands of users who trust our platform for their voting needs.</p>
                <a href="auth/register.php" class="btn btn-primary btn-lg me-3">
                    <i class="fas fa-user-plus"></i> Create Account
                </a>
                <a href="auth/login.php" class="btn btn-outline-primary btn-lg">
                    <i class="fas fa-sign-in-alt"></i> Login
                </a>
            </div>
        </div>
    </div>
    <?php endif; ?>
</div>

<?php include 'includes/footer.php'; ?>
