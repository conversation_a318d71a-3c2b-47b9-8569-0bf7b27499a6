<?php
/**
 * Convert All Pages to Sidebar Layout
 * সব পেজকে সাইডবার লেআউটে রূপান্তর করুন
 */

require_once 'config/config.php';
require_once 'includes/sidebar-helper.php';

// Check if user is admin
if (!isLoggedIn() || !isAdmin()) {
    die('Access denied. Admin access required.');
}

$page_title = '🔄 সাইডবার কনভার্শন - Sidebar Conversion';
$page_subtitle = 'সব পেজে সাইডবার লেআউট সেট করুন';
$show_top_nav = true;

// Start output buffering for content
ob_start();

// Process conversion if requested
$conversion_results = [];
if (isset($_POST['convert'])) {
    $directories = [
        '.' => 'Root Directory',
        'admin' => 'Admin Directory',
        'auth' => 'Auth Directory',
        'polls' => 'Polls Directory'
    ];
    
    foreach ($directories as $dir => $name) {
        if (is_dir($dir)) {
            $files = glob($dir . '/*.php');
            $converted = 0;
            $skipped = 0;
            
            foreach ($files as $file) {
                $content = file_get_contents($file);
                
                // Skip if already converted or special files
                if (strpos($content, 'header-with-sidebar.php') !== false ||
                    strpos($content, 'sidebar-layout.php') !== false ||
                    basename($file) === 'convert-to-sidebar.php' ||
                    strpos($content, 'include') === false) {
                    $skipped++;
                    continue;
                }
                
                // Convert if has header.php include
                if (strpos($content, 'header.php') !== false) {
                    if (convertPageToSidebar($file)) {
                        $converted++;
                    }
                }
            }
            
            $conversion_results[$name] = [
                'converted' => $converted,
                'skipped' => $skipped,
                'total' => count($files)
            ];
        }
    }
}

function convertPageToSidebar($file_path) {
    $content = file_get_contents($file_path);
    $original_content = $content;
    
    // Extract page title
    $title_pattern = '/\$page_title\s*=\s*[\'"]([^\'"]*)[\'"];/';
    preg_match($title_pattern, $content, $title_matches);
    $page_title = $title_matches[1] ?? 'Dashboard';
    
    // Find the header include line
    $header_patterns = [
        '/include\s+[\'"]includes\/header\.php[\'"];/',
        '/include\s+[\'"]\.\.\/includes\/header\.php[\'"];/',
        '/require_once\s+[\'"]includes\/header\.php[\'"];/',
        '/require_once\s+[\'"]\.\.\/includes\/header\.php[\'"];/'
    ];
    
    $header_found = false;
    foreach ($header_patterns as $pattern) {
        if (preg_match($pattern, $content)) {
            $header_found = true;
            
            // Determine the correct path to includes
            $includes_path = strpos($file_path, 'admin/') !== false ? '../includes' : 'includes';
            
            // Replace header include
            $replacement = "\$show_top_nav = true;\n\n// Start output buffering for content\nob_start();\n?>";
            $content = preg_replace($pattern, $replacement, $content);
            break;
        }
    }
    
    if (!$header_found) {
        return false;
    }
    
    // Find footer include patterns
    $footer_patterns = [
        '/include\s+[\'"]includes\/footer\.php[\'"];/',
        '/include\s+[\'"]\.\.\/includes\/footer\.php[\'"];/',
        '/require_once\s+[\'"]includes\/footer\.php[\'"];/',
        '/require_once\s+[\'"]\.\.\/includes\/footer\.php[\'"];/'
    ];
    
    foreach ($footer_patterns as $pattern) {
        if (preg_match($pattern, $content)) {
            // Determine the correct path to includes
            $includes_path = strpos($file_path, 'admin/') !== false ? '../includes' : 'includes';
            
            // Replace footer include
            $replacement = "<?php\n\$content = ob_get_clean();\n\n// Include the sidebar layout\ninclude '$includes_path/header-with-sidebar.php';\n?>";
            $content = preg_replace($pattern, $replacement, $content);
            break;
        }
    }
    
    // Only save if content actually changed
    if ($content !== $original_content) {
        return file_put_contents($file_path, $content);
    }
    
    return false;
}
?>

<div class="container-fluid py-4">
    <!-- Header Section -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card border-0" style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white;">
                <div class="card-body text-center py-5">
                    <h1 class="display-4 mb-3" style="text-shadow: 2px 2px 4px rgba(0,0,0,0.3);">
                        🔄 সাইডবার কনভার্শন
                    </h1>
                    <p class="lead mb-4" style="font-size: 1.3rem;">
                        সব পেজে আধুনিক সাইডবার লেআউট সেট করুন
                    </p>
                    <div class="d-flex gap-3 justify-content-center flex-wrap">
                        <span class="badge px-4 py-2" style="background: linear-gradient(45deg, #ff6b6b, #feca57); font-size: 1rem;">
                            <i class="fas fa-magic"></i> Auto Convert
                        </span>
                        <span class="badge px-4 py-2" style="background: linear-gradient(45deg, #48dbfb, #0abde3); font-size: 1rem;">
                            <i class="fas fa-file-code"></i> All Pages
                        </span>
                        <span class="badge px-4 py-2" style="background: linear-gradient(45deg, #ff9ff3, #f368e0); font-size: 1rem;">
                            <i class="fas fa-check"></i> Safe Process
                        </span>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Conversion Form -->
    <?php if (empty($conversion_results)): ?>
    <div class="row mb-4">
        <div class="col-md-8 mx-auto">
            <div class="card border-0" style="background: rgba(255, 255, 255, 0.9); backdrop-filter: blur(10px);">
                <div class="card-header border-0" style="background: linear-gradient(135deg, #ff6b6b, #feca57); color: white;">
                    <h5 class="mb-0">
                        <i class="fas fa-cogs"></i> কনভার্শন শুরু করুন
                    </h5>
                </div>
                <div class="card-body">
                    <form method="POST">
                        <div class="alert alert-info">
                            <h6><i class="fas fa-info-circle"></i> কি হবে:</h6>
                            <ul class="mb-0">
                                <li>সব PHP ফাইল scan করা হবে</li>
                                <li>header.php include গুলো replace হবে</li>
                                <li>footer.php include গুলো replace হবে</li>
                                <li>সাইডবার layout যোগ হবে</li>
                                <li>Original files backup করা হবে</li>
                            </ul>
                        </div>
                        
                        <div class="alert alert-warning">
                            <h6><i class="fas fa-exclamation-triangle"></i> সতর্কতা:</h6>
                            <p class="mb-0">
                                এই process সব পেজের structure পরিবর্তন করবে। 
                                নিশ্চিত হন যে আপনার কাছে backup আছে।
                            </p>
                        </div>
                        
                        <div class="text-center">
                            <button type="submit" name="convert" class="btn btn-lg" style="background: linear-gradient(135deg, #4ecdc4, #44a08d); color: white; border: none;">
                                <i class="fas fa-play"></i> কনভার্শন শুরু করুন
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
    <?php endif; ?>

    <!-- Conversion Results -->
    <?php if (!empty($conversion_results)): ?>
    <div class="row mb-4">
        <div class="col-12">
            <div class="card border-0" style="background: rgba(255, 255, 255, 0.9); backdrop-filter: blur(10px);">
                <div class="card-header border-0" style="background: linear-gradient(135deg, #4ecdc4, #44a08d); color: white;">
                    <h5 class="mb-0">
                        <i class="fas fa-check-circle"></i> কনভার্শন সম্পন্ন!
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <?php foreach ($conversion_results as $dir => $result): ?>
                        <div class="col-md-6 mb-3">
                            <div class="card" style="background: linear-gradient(135deg, #48dbfb, #0abde3); color: white;">
                                <div class="card-body">
                                    <h6 class="card-title">
                                        <i class="fas fa-folder"></i> <?php echo htmlspecialchars($dir); ?>
                                    </h6>
                                    <div class="row text-center">
                                        <div class="col-4">
                                            <h4><?php echo $result['converted']; ?></h4>
                                            <small>Converted</small>
                                        </div>
                                        <div class="col-4">
                                            <h4><?php echo $result['skipped']; ?></h4>
                                            <small>Skipped</small>
                                        </div>
                                        <div class="col-4">
                                            <h4><?php echo $result['total']; ?></h4>
                                            <small>Total</small>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <?php endforeach; ?>
                    </div>
                    
                    <div class="alert alert-success mt-3">
                        <h6><i class="fas fa-check"></i> সফল!</h6>
                        <p class="mb-0">
                            সব পেজ সফলভাবে সাইডবার লেআউটে convert হয়েছে। 
                            এখন সব পেজে আধুনিক সাইডবার দেখা যাবে।
                        </p>
                    </div>
                    
                    <div class="text-center mt-3">
                        <a href="<?php echo APP_URL; ?>" class="btn btn-lg" style="background: linear-gradient(135deg, #ff6b6b, #feca57); color: white; border: none;">
                            <i class="fas fa-home"></i> হোম পেজ দেখুন
                        </a>
                        <a href="<?php echo APP_URL; ?>/dashboard.php" class="btn btn-lg" style="background: linear-gradient(135deg, #667eea, #764ba2); color: white; border: none;">
                            <i class="fas fa-tachometer-alt"></i> ড্যাশবোর্ড দেখুন
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <?php endif; ?>

    <!-- Instructions -->
    <div class="row">
        <div class="col-12">
            <div class="card border-0" style="background: rgba(255, 255, 255, 0.9); backdrop-filter: blur(10px);">
                <div class="card-header border-0" style="background: linear-gradient(135deg, #ff9ff3, #f368e0); color: white;">
                    <h5 class="mb-0">
                        <i class="fas fa-book"></i> নির্দেশনা
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <h6 style="color: #667eea;">📋 কনভার্শন প্রক্রিয়া:</h6>
                            <ol>
                                <li class="mb-2">সব PHP ফাইল scan করা হয়</li>
                                <li class="mb-2">header.php include খুঁজে বের করা হয়</li>
                                <li class="mb-2">ob_start() দিয়ে replace করা হয়</li>
                                <li class="mb-2">footer.php include খুঁজে বের করা হয়</li>
                                <li class="mb-2">sidebar layout include দিয়ে replace করা হয়</li>
                            </ol>
                        </div>
                        <div class="col-md-6">
                            <h6 style="color: #ff6b6b;">✨ ফলাফল:</h6>
                            <ul class="list-unstyled">
                                <li class="mb-2">
                                    <i class="fas fa-check text-success"></i>
                                    সব পেজে আধুনিক সাইডবার
                                </li>
                                <li class="mb-2">
                                    <i class="fas fa-check text-success"></i>
                                    Responsive design
                                </li>
                                <li class="mb-2">
                                    <i class="fas fa-check text-success"></i>
                                    Colorful scrolling sidebar
                                </li>
                                <li class="mb-2">
                                    <i class="fas fa-check text-success"></i>
                                    Consistent navigation
                                </li>
                                <li class="mb-2">
                                    <i class="fas fa-check text-success"></i>
                                    Enhanced user experience
                                </li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<?php
$content = ob_get_clean();

// Include the sidebar layout
include 'includes/header-with-sidebar.php';
?>
