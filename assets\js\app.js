/**
 * Main Application JavaScript
 * Dynamic Realtime Online Voting System
 */

// Global application object
window.OVS = {
    config: window.APP_CONFIG || {},

    // Initialize application
    init: function() {
        this.setupCSRF();
        this.setupAjax();
        this.setupRealTime();
        this.setupUI();
        this.setupSidebar();
        console.log('OVS Application initialized');
    },
    
    // Setup CSRF token for all AJAX requests
    setupCSRF: function() {
        $.ajaxSetup({
            beforeSend: function(xhr, settings) {
                if (!/^(GET|HEAD|OPTIONS|TRACE)$/i.test(settings.type) && !this.crossDomain) {
                    xhr.setRequestHeader("X-CSRF-Token", OVS.config.csrfToken);
                }
            }
        });
    },
    
    // Setup global AJAX handlers
    setupAjax: function() {
        $(document).ajaxStart(function() {
            OVS.showLoading();
        });
        
        $(document).ajaxStop(function() {
            OVS.hideLoading();
        });
        
        $(document).ajaxError(function(event, xhr, settings, thrownError) {
            console.error('AJAX Error:', xhr.responseText);
            if (xhr.status === 401) {
                OVS.showAlert('Session expired. Please login again.', 'warning');
                setTimeout(() => {
                    window.location.href = OVS.config.url + '/auth/login.php';
                }, 2000);
            } else if (xhr.status === 403) {
                OVS.showAlert('Access denied.', 'danger');
            } else if (xhr.status >= 500) {
                OVS.showAlert('Server error. Please try again later.', 'danger');
            }
        });
    },
    
    // Setup real-time updates
    setupRealTime: function() {
        if (this.config.enableRealTime) {
            this.startRealTimeUpdates();
        }
    },
    
    // Setup UI enhancements
    setupUI: function() {
        // Auto-hide alerts after 5 seconds
        $('.alert').each(function() {
            const alert = $(this);
            setTimeout(() => {
                alert.fadeOut();
            }, 5000);
        });
        
        // Smooth scrolling for anchor links
        $('a[href^="#"]').on('click', function(e) {
            e.preventDefault();
            const target = $(this.getAttribute('href'));
            if (target.length) {
                $('html, body').animate({
                    scrollTop: target.offset().top - 100
                }, 500);
            }
        });
        
        // Form validation enhancement
        $('form').on('submit', function() {
            const form = $(this);
            const submitBtn = form.find('button[type="submit"]');
            submitBtn.prop('disabled', true).html('<i class="fas fa-spinner fa-spin"></i> Processing...');
        });
        
        // Auto-resize textareas
        $('textarea').on('input', function() {
            this.style.height = 'auto';
            this.style.height = (this.scrollHeight) + 'px';
        });
    },
    
    // Real-time update functions
    startRealTimeUpdates: function() {
        if (typeof this.updateInterval !== 'undefined') {
            clearInterval(this.updateInterval);
        }
        
        this.updateInterval = setInterval(() => {
            this.checkForUpdates();
        }, this.config.ajaxPollInterval);
    },
    
    stopRealTimeUpdates: function() {
        if (typeof this.updateInterval !== 'undefined') {
            clearInterval(this.updateInterval);
        }
    },
    
    checkForUpdates: function() {
        // Check for notifications if user is logged in
        if (this.config.isLoggedIn) {
            this.checkNotifications();
        }
        
        // Check for poll updates if on a poll page
        if (typeof window.currentPollId !== 'undefined') {
            this.updatePollResults(window.currentPollId);
        }
    },
    
    // Notification functions
    checkNotifications: function() {
        $.get(this.config.url + '/api/notifications.php', {
            action: 'check'
        }).done(function(response) {
            if (response.success && response.data.count > 0) {
                $('#notification-count').text(response.data.count).show();
                OVS.updateNotificationsList(response.data.notifications);
            } else {
                $('#notification-count').hide();
            }
        });
    },
    
    updateNotificationsList: function(notifications) {
        const list = $('#notifications-list');
        list.empty();
        
        if (notifications.length === 0) {
            list.html('<div class="dropdown-item text-muted text-center">No new notifications</div>');
            return;
        }
        
        notifications.forEach(notification => {
            const item = $(`
                <div class="dropdown-item notification-item" data-id="${notification.id}">
                    <div class="d-flex justify-content-between align-items-start">
                        <div>
                            <h6 class="mb-1">${notification.title}</h6>
                            <p class="mb-1 small text-muted">${notification.message}</p>
                            <small class="text-muted">${this.timeAgo(notification.created_at)}</small>
                        </div>
                        <button class="btn btn-sm btn-outline-secondary mark-read" data-id="${notification.id}">
                            <i class="fas fa-check"></i>
                        </button>
                    </div>
                </div>
            `);
            list.append(item);
        });
    },
    
    // Poll update functions
    updatePollResults: function(pollId) {
        $.get(this.config.url + '/api/results.php', {
            poll_id: pollId
        }).done(function(response) {
            if (response.success) {
                OVS.renderPollResults(response.data);
            }
        });
    },
    
    renderPollResults: function(results) {
        const container = $('#poll-results');
        if (container.length === 0) return;
        
        container.html('');
        
        results.options.forEach(option => {
            const percentage = results.total_votes > 0 ? 
                Math.round((option.vote_count / results.total_votes) * 100) : 0;
            
            const resultHtml = $(`
                <div class="mb-3">
                    <div class="d-flex justify-content-between align-items-center mb-1">
                        <span class="fw-medium">${option.text}</span>
                        <span class="text-muted">${option.vote_count} votes (${percentage}%)</span>
                    </div>
                    <div class="progress" style="height: 30px;">
                        <div class="progress-bar bg-primary" role="progressbar" 
                             style="width: ${percentage}%" aria-valuenow="${percentage}" 
                             aria-valuemin="0" aria-valuemax="100">
                            ${percentage}%
                        </div>
                    </div>
                </div>
            `);
            
            container.append(resultHtml);
        });
        
        // Update total votes
        $('#total-votes').text(results.total_votes);
    },
    
    // Voting functions
    castVote: function(pollId, voteData) {
        return $.post(this.config.url + '/api/vote.php', {
            action: 'cast',
            poll_id: pollId,
            vote_data: JSON.stringify(voteData)
        });
    },
    
    // Utility functions
    showAlert: function(message, type = 'info', duration = 5000) {
        const alertHtml = $(`
            <div class="alert alert-${type} alert-dismissible fade show" role="alert">
                ${message}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        `);
        
        $('.container').first().prepend(alertHtml);
        
        setTimeout(() => {
            alertHtml.fadeOut(() => alertHtml.remove());
        }, duration);
    },
    
    showLoading: function() {
        if ($('#loading-overlay').length === 0) {
            $('body').append(`
                <div id="loading-overlay" style="
                    position: fixed; top: 0; left: 0; width: 100%; height: 100%;
                    background: rgba(0,0,0,0.1); z-index: 9999; display: flex;
                    align-items: center; justify-content: center;
                ">
                    <div class="spinner-border text-primary" role="status">
                        <span class="visually-hidden">Loading...</span>
                    </div>
                </div>
            `);
        }
    },
    
    hideLoading: function() {
        $('#loading-overlay').remove();
    },
    
    timeAgo: function(dateString) {
        const now = new Date();
        const date = new Date(dateString);
        const diffInSeconds = Math.floor((now - date) / 1000);
        
        if (diffInSeconds < 60) return 'just now';
        if (diffInSeconds < 3600) return Math.floor(diffInSeconds / 60) + ' minutes ago';
        if (diffInSeconds < 86400) return Math.floor(diffInSeconds / 3600) + ' hours ago';
        if (diffInSeconds < 2592000) return Math.floor(diffInSeconds / 86400) + ' days ago';
        if (diffInSeconds < 31536000) return Math.floor(diffInSeconds / 2592000) + ' months ago';
        return Math.floor(diffInSeconds / 31536000) + ' years ago';
    },
    
    formatNumber: function(num) {
        return new Intl.NumberFormat().format(num);
    },
    
    copyToClipboard: function(text) {
        navigator.clipboard.writeText(text).then(() => {
            this.showAlert('Copied to clipboard!', 'success', 2000);
        }).catch(() => {
            this.showAlert('Failed to copy to clipboard', 'danger', 2000);
        });
    },

    // Modern Sidebar Functions
    setupSidebar: function() {
        const sidebar = $('#modernSidebar');
        const overlay = $('#sidebarOverlay');
        const mobileToggle = $('#mobileSidebarToggle');
        const sidebarToggle = $('#sidebarToggle');
        const mainContent = $('.main-content');

        // Mobile sidebar toggle
        mobileToggle.on('click', function() {
            if ($(window).width() <= 991) {
                sidebar.addClass('active');
                overlay.addClass('active');
                $('body').addClass('sidebar-open');
            }
        });

        // Close sidebar (only on mobile)
        function closeSidebar() {
            if ($(window).width() <= 991) {
                sidebar.removeClass('active');
                overlay.removeClass('active');
                $('body').removeClass('sidebar-open');
            }
        }

        // Sidebar close button
        sidebarToggle.on('click', closeSidebar);

        // Overlay click to close
        overlay.on('click', closeSidebar);

        // Close on escape key
        $(document).on('keydown', function(e) {
            if (e.key === 'Escape' && sidebar.hasClass('active')) {
                closeSidebar();
            }
        });

        // Auto-close on mobile when clicking nav links
        $('.nav-link').on('click', function() {
            if ($(window).width() <= 991) {
                setTimeout(closeSidebar, 300);
            }
        });

        // Handle window resize
        $(window).on('resize', function() {
            if ($(window).width() > 991) {
                closeSidebar();
                $('body').removeClass('sidebar-open');
            }
        });

        // Add smooth scrolling to nav links
        $('.nav-link').on('click', function(e) {
            const href = $(this).attr('href');
            if (href && href.includes('#')) {
                e.preventDefault();
                const target = $(href.split('#')[1]);
                if (target.length) {
                    $('html, body').animate({
                        scrollTop: target.offset().top - 100
                    }, 500);
                }
            }
        });

        // Add active state management
        this.updateActiveNavItem();
    },

    updateActiveNavItem: function() {
        const currentPath = window.location.pathname;
        $('.nav-link').removeClass('active');

        $('.nav-link').each(function() {
            const linkPath = new URL($(this).attr('href'), window.location.origin).pathname;
            if (linkPath === currentPath) {
                $(this).addClass('active');
            }
        });
    },

    // Sidebar notification badge
    updateSidebarBadges: function() {
        // Update notification badges in sidebar
        $.get(this.config.url + '/api/notifications.php?action=count')
            .done(function(response) {
                if (response.success) {
                    const count = response.count;
                    if (count > 0) {
                        $('.nav-link[href*="notifications"]').append(
                            `<span class="badge bg-danger ms-auto">${count}</span>`
                        );
                    }
                }
            });
    }
};

// Initialize when document is ready
$(document).ready(function() {
    OVS.init();
});

// Event handlers
$(document).on('click', '.mark-read', function(e) {
    e.preventDefault();
    e.stopPropagation();
    
    const notificationId = $(this).data('id');
    $.post(OVS.config.url + '/api/notifications.php', {
        action: 'mark_read',
        id: notificationId
    }).done(function(response) {
        if (response.success) {
            $(`.notification-item[data-id="${notificationId}"]`).fadeOut();
            OVS.checkNotifications(); // Refresh count
        }
    });
});

$(document).on('click', '.copy-link', function(e) {
    e.preventDefault();
    const link = $(this).data('link');
    OVS.copyToClipboard(link);
});

// Handle page visibility changes for real-time updates
document.addEventListener('visibilitychange', function() {
    if (document.hidden) {
        OVS.stopRealTimeUpdates();
    } else {
        if (OVS.config.enableRealTime) {
            OVS.startRealTimeUpdates();
        }
    }
});
