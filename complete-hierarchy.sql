-- Complete Administrative Hierarchy Setup
-- Run this in phpMyAdmin SQL tab

USE ovs_db;

-- Check current status
SELECT 'Current Areas:' as status;
SELECT type, COUNT(*) as count FROM areas WHERE is_active = 1 GROUP BY type;

-- Update areas table to support all types
ALTER TABLE `areas` 
MODIFY COLUMN `type` enum('division','district','upazila','municipality','union','village','city','ward') NOT NULL DEFAULT 'ward';

-- Add missing hierarchy levels

-- 1. Add Upazilas (if not exists)
INSERT IGNORE INTO `areas` (`name`, `name_bn`, `type`, `parent_id`, `code`, `description`) 
SELECT 'Dhanmondi Upazila', 'ধানমন্ডি উপজেলা', 'upazila', 
       (SELECT id FROM (SELECT id FROM areas WHERE type = 'district' LIMIT 1) as d), 
       'UPZ-01', 'ধানমন্ডি উপজেলা';

INSERT IGNORE INTO `areas` (`name`, `name_bn`, `type`, `parent_id`, `code`, `description`) 
SELECT 'Gulshan Upazila', 'গুলশান উপজেলা', 'upazila', 
       (SELECT id FROM (SELECT id FROM areas WHERE type = 'district' LIMIT 1) as d), 
       'UPZ-02', 'গুলশান উপজেলা';

INSERT IGNORE INTO `areas` (`name`, `name_bn`, `type`, `parent_id`, `code`, `description`) 
SELECT 'Tejgaon Upazila', 'তেজগাঁও উপজেলা', 'upazila', 
       (SELECT id FROM (SELECT id FROM areas WHERE type = 'district' LIMIT 1) as d), 
       'UPZ-03', 'তেজগাঁও উপজেলা';

-- 2. Add Municipality (if not exists)
INSERT IGNORE INTO `areas` (`name`, `name_bn`, `type`, `parent_id`, `code`, `description`) 
SELECT 'Dhanmondi Municipality', 'ধানমন্ডি পৌরসভা', 'municipality', 
       (SELECT id FROM (SELECT id FROM areas WHERE type = 'upazila' AND code = 'UPZ-01') as u), 
       'MUN-01', 'ধানমন্ডি পৌরসভা';

-- 3. Add Unions (if not exists)
INSERT IGNORE INTO `areas` (`name`, `name_bn`, `type`, `parent_id`, `code`, `description`) 
SELECT 'Gulshan Union', 'গুলশান ইউনিয়ন', 'union', 
       (SELECT id FROM (SELECT id FROM areas WHERE type = 'upazila' AND code = 'UPZ-02') as u), 
       'UNI-01', 'গুলশান ইউনিয়ন';

INSERT IGNORE INTO `areas` (`name`, `name_bn`, `type`, `parent_id`, `code`, `description`) 
SELECT 'Tejgaon Union', 'তেজগাঁও ইউনিয়ন', 'union', 
       (SELECT id FROM (SELECT id FROM areas WHERE type = 'upazila' AND code = 'UPZ-03') as u), 
       'UNI-02', 'তেজগাঁও ইউনিয়ন';

-- 4. Add Villages (if not exists)
INSERT IGNORE INTO `areas` (`name`, `name_bn`, `type`, `parent_id`, `code`, `description`) 
SELECT 'Gulshan Village', 'গুলশান গ্রাম', 'village', 
       (SELECT id FROM (SELECT id FROM areas WHERE type = 'union' AND code = 'UNI-01') as u), 
       'VIL-01', 'গুলশান গ্রাম';

INSERT IGNORE INTO `areas` (`name`, `name_bn`, `type`, `parent_id`, `code`, `description`) 
SELECT 'Tejgaon Village', 'তেজগাঁও গ্রাম', 'village', 
       (SELECT id FROM (SELECT id FROM areas WHERE type = 'union' AND code = 'UNI-02') as u), 
       'VIL-02', 'তেজগাঁও গ্রাম';

-- 5. Update existing wards to have proper parents
-- Link wards to municipality or villages

-- First, try to link to municipality
UPDATE `areas` 
SET `parent_id` = (SELECT id FROM (SELECT id FROM areas WHERE type = 'municipality' LIMIT 1) as m)
WHERE type = 'ward' AND parent_id IS NULL AND id <= (
    SELECT ward_count FROM (
        SELECT COUNT(*) as ward_count FROM areas WHERE type = 'ward' AND parent_id IS NULL
    ) as wc
) / 2;

-- Then link remaining wards to villages
UPDATE `areas` a1
SET `parent_id` = (
    SELECT id FROM (
        SELECT id FROM areas WHERE type = 'village' LIMIT 1 OFFSET 0
    ) as v
)
WHERE a1.type = 'ward' AND a1.parent_id IS NULL AND a1.id IN (
    SELECT id FROM (
        SELECT id FROM areas WHERE type = 'ward' AND parent_id IS NULL LIMIT 2
    ) as w1
);

UPDATE `areas` a2
SET `parent_id` = (
    SELECT id FROM (
        SELECT id FROM areas WHERE type = 'village' LIMIT 1 OFFSET 1
    ) as v
)
WHERE a2.type = 'ward' AND a2.parent_id IS NULL;

-- 6. Final status check
SELECT 'Final Hierarchy:' as status;
SELECT 
    type,
    COUNT(*) as count,
    CASE type 
        WHEN 'division' THEN 'বিভাগ'
        WHEN 'district' THEN 'জেলা'
        WHEN 'upazila' THEN 'উপজেলা'
        WHEN 'municipality' THEN 'পৌরসভা'
        WHEN 'union' THEN 'ইউনিয়ন'
        WHEN 'village' THEN 'গ্রাম'
        WHEN 'city' THEN 'শহর'
        WHEN 'ward' THEN 'ওয়ার্ড'
    END as bangla_name
FROM areas 
WHERE is_active = 1 
GROUP BY type 
ORDER BY 
    CASE type 
        WHEN 'division' THEN 1 
        WHEN 'district' THEN 2 
        WHEN 'upazila' THEN 3 
        WHEN 'municipality' THEN 4
        WHEN 'union' THEN 4
        WHEN 'village' THEN 5
        WHEN 'city' THEN 5
        WHEN 'ward' THEN 6 
    END;

-- 7. Show sample hierarchy tree
SELECT 'Sample Hierarchy Tree:' as status;
SELECT 
    CONCAT(
        CASE a.type 
            WHEN 'division' THEN ''
            WHEN 'district' THEN '  └─ '
            WHEN 'upazila' THEN '    └─ '
            WHEN 'municipality' THEN '      └─ '
            WHEN 'union' THEN '      └─ '
            WHEN 'village' THEN '        └─ '
            WHEN 'ward' THEN '          └─ '
        END,
        a.name_bn,
        ' (',
        CASE a.type 
            WHEN 'division' THEN 'বিভাগ'
            WHEN 'district' THEN 'জেলা'
            WHEN 'upazila' THEN 'উপজেলা'
            WHEN 'municipality' THEN 'পৌরসভা'
            WHEN 'union' THEN 'ইউনিয়ন'
            WHEN 'village' THEN 'গ্রাম'
            WHEN 'ward' THEN 'ওয়ার্ড'
        END,
        ')'
    ) as hierarchy_tree
FROM areas a
WHERE a.is_active = 1
ORDER BY 
    CASE a.type 
        WHEN 'division' THEN 1 
        WHEN 'district' THEN 2 
        WHEN 'upazila' THEN 3 
        WHEN 'municipality' THEN 4
        WHEN 'union' THEN 4
        WHEN 'village' THEN 5
        WHEN 'ward' THEN 6 
    END,
    a.id
LIMIT 20;

-- 8. Verify voter data integrity
SELECT 'Voter Data Check:' as status;
SELECT 
    v.voter_id,
    v.name_bn,
    a.name_bn as area_name,
    a.type as area_type
FROM voters v
LEFT JOIN areas a ON v.area_id = a.id
WHERE v.is_active = 1
LIMIT 5;
