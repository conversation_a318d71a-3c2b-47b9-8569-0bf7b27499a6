<?php
/**
 * Hierarchical Voter Upload Functions
 * Handle CSV upload with hierarchical area structure
 */

function uploadVotersHierarchical($file) {
    if (!$file || $file['error'] !== UPLOAD_ERR_OK) {
        return ['success' => false, 'message' => 'ফাইল আপলোড করতে ব্যর্থ'];
    }
    
    if (pathinfo($file['name'], PATHINFO_EXTENSION) !== 'csv') {
        return ['success' => false, 'message' => 'শুধুমাত্র CSV ফাইল গ্রহণযোগ্য'];
    }
    
    try {
        $db = getDB();
        $handle = fopen($file['tmp_name'], 'r');
        
        if (!$handle) {
            return ['success' => false, 'message' => 'ফাইল পড়তে ব্যর্থ'];
        }
        
        // Read header row to determine format
        $headers = fgetcsv($handle);

        if (!$headers || empty($headers)) {
            fclose($handle);
            return ['success' => false, 'message' => 'CSV ফাইলে কোন header পাওয়া যায়নি'];
        }

        // Clean headers (remove BOM and trim)
        $headers = array_map(function($header) {
            return trim(str_replace("\xEF\xBB\xBF", '', $header));
        }, $headers);

        $is_hierarchical = in_array('ward_id', $headers) && in_array('division_name', $headers);

        // Debug: Log headers
        error_log("CSV Headers: " . implode(', ', $headers));
        error_log("Is Hierarchical: " . ($is_hierarchical ? 'Yes' : 'No'));
        
        $success_count = 0;
        $error_count = 0;
        $errors = [];
        $line_number = 1; // Start from 1 (header is line 0)
        
        while (($row = fgetcsv($handle)) !== false) {
            $line_number++;

            // Debug: Log each row
            error_log("Processing line $line_number: " . implode('|', $row));

            if (count($row) < 6) { // Minimum required columns
                $errors[] = "Line $line_number: Insufficient columns (found " . count($row) . ", need at least 6)";
                $error_count++;
                continue;
            }

            // Skip empty rows
            if (empty(array_filter($row))) {
                continue;
            }
            
            try {
                $area_id = null;
                
                if ($is_hierarchical) {
                    // New hierarchical format
                    $ward_id_index = array_search('ward_id', $headers);
                    $area_id = intval($row[$ward_id_index] ?? 0);
                    
                    // Verify ward exists
                    $stmt = $db->prepare("SELECT id FROM areas WHERE id = ? AND type = 'ward' AND is_active = 1");
                    $stmt->execute([$area_id]);
                    if (!$stmt->fetch()) {
                        $errors[] = "Line $line_number: Ward ID '{$area_id}' not found";
                        $error_count++;
                        continue;
                    }
                } else {
                    // Old format - try to find by area_id
                    $area_id_index = array_search('area_id', $headers);
                    if ($area_id_index === false) {
                        $errors[] = "Line $line_number: No area_id or ward_id column found";
                        $error_count++;
                        continue;
                    }
                    
                    $area_id = intval($row[$area_id_index] ?? 0);
                    
                    $stmt = $db->prepare("SELECT id FROM areas WHERE id = ? AND is_active = 1");
                    $stmt->execute([$area_id]);
                    if (!$stmt->fetch()) {
                        $errors[] = "Line $line_number: Area ID '{$area_id}' not found";
                        $error_count++;
                        continue;
                    }
                }
                
                // Get field values by header position with better error handling
                $voter_id_index = array_search('voter_id', $headers);
                $name_index = array_search('name', $headers);
                $name_bn_index = array_search('name_bn', $headers);
                $father_name_index = array_search('father_name', $headers);
                $mother_name_index = array_search('mother_name', $headers);
                $nid_index = array_search('nid', $headers);
                $mobile_index = array_search('mobile', $headers);
                $email_index = array_search('email', $headers);
                $date_of_birth_index = array_search('date_of_birth', $headers);
                $gender_index = array_search('gender', $headers);
                $address_index = array_search('address', $headers);

                // Check if required columns exist (voter_id is now optional)
                if ($name_index === false || $nid_index === false || $mobile_index === false) {
                    $errors[] = "Line $line_number: Required columns missing (name, nid, mobile)";
                    $error_count++;
                    continue;
                }

                $voter_id = trim($row[$voter_id_index] ?? '');
                $name = trim($row[$name_index] ?? '');
                $name_bn = trim($row[$name_bn_index] ?? '');
                $father_name = trim($row[$father_name_index] ?? '');
                $mother_name = trim($row[$mother_name_index] ?? '');
                $nid = trim($row[$nid_index] ?? '');
                $mobile = trim($row[$mobile_index] ?? '');
                $email = trim($row[$email_index] ?? '');
                $date_of_birth = trim($row[$date_of_birth_index] ?? '');
                $gender = trim($row[$gender_index] ?? '');
                $address = trim($row[$address_index] ?? '');

                // Auto-generate voter_id if empty
                if (empty($voter_id)) {
                    require_once '../includes/voter-id-generator.php';
                    $voter_id = generateVoterIDWithArea($db, $area_id, 'V{WARD_CODE}{YYYY}{RRRRR}');
                    error_log("Auto-generated voter ID: $voter_id for line $line_number");
                }
                
                // Validate required fields (voter_id now auto-generated if empty)
                if (empty($name) || empty($nid) || empty($mobile) || !$area_id) {
                    $errors[] = "Line $line_number: Missing required fields (name, nid, mobile, area)";
                    $error_count++;
                    continue;
                }

                // Final check for voter_id (should be generated by now)
                if (empty($voter_id)) {
                    $errors[] = "Line $line_number: Failed to generate voter_id";
                    $error_count++;
                    continue;
                }
                
                // Validate NID (10-17 digits) - More flexible validation
                $clean_nid = preg_replace('/[^0-9]/', '', $nid); // Remove non-digits
                if (strlen($clean_nid) < 10 || strlen($clean_nid) > 17) {
                    $errors[] = "Line $line_number: Invalid NID format for {$voter_id} (NID: '$nid', cleaned: '$clean_nid', length: " . strlen($clean_nid) . ")";
                    $error_count++;
                    continue;
                }
                $nid = $clean_nid; // Use cleaned NID
                
                // Clean and validate mobile (11 digits starting with 01)
                $clean_mobile = preg_replace('/[^0-9]/', '', $mobile); // Remove non-digits
                if (strlen($clean_mobile) != 11 || !preg_match('/^01/', $clean_mobile)) {
                    $errors[] = "Line $line_number: Invalid mobile format for {$voter_id} (Original: '$mobile', Cleaned: '$clean_mobile', Length: " . strlen($clean_mobile) . ")";
                    $error_count++;
                    continue;
                }
                $mobile = $clean_mobile; // Use cleaned mobile
                
                // Check for NID duplicates only
                if (!empty($nid)) {
                    $stmt = $db->prepare("SELECT id, voter_id FROM voters WHERE nid = ?");
                    $stmt->execute([$nid]);
                    $existing = $stmt->fetch();
                    if ($existing) {
                        $errors[] = "Line $line_number: NID '{$nid}' already exists for voter {$existing['voter_id']}";
                        $error_count++;
                        continue;
                    }
                }
                
                // Validate date format if provided
                $dob = null;
                if (!empty($date_of_birth)) {
                    $dob = DateTime::createFromFormat('Y-m-d', $date_of_birth);
                    if (!$dob) {
                        $errors[] = "Line $line_number: Invalid date format for {$voter_id} (use YYYY-MM-DD)";
                        $error_count++;
                        continue;
                    }
                    $dob = $date_of_birth;
                }
                
                // Validate gender if provided
                if (!empty($gender) && !in_array(strtolower($gender), ['male', 'female'])) {
                    $errors[] = "Line $line_number: Invalid gender for {$voter_id} (use 'male' or 'female')";
                    $error_count++;
                    continue;
                }
                
                // Insert voter
                $stmt = $db->prepare("
                    INSERT INTO voters (
                        voter_id, name, name_bn, father_name, mother_name, 
                        nid, mobile, email, date_of_birth, gender, 
                        area_id, address, created_by, created_at
                    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, NOW())
                ");
                
                $stmt->execute([
                    $voter_id,
                    $name,
                    $name_bn ?: null,
                    $father_name ?: null,
                    $mother_name ?: null,
                    $nid,
                    $mobile,
                    $email ?: null,
                    $dob,
                    $gender ?: null,
                    $area_id,
                    $address ?: null,
                    getCurrentUser()['id']
                ]);
                
                $success_count++;
                
            } catch (Exception $e) {
                $errors[] = "Line $line_number: Error for {$voter_id}: " . $e->getMessage();
                $error_count++;
            }
        }
        
        fclose($handle);
        
        $message = "আপলোড সম্পন্ন: {$success_count} জন ভোটার সফলভাবে যোগ করা হয়েছে";
        if ($error_count > 0) {
            $message .= ", {$error_count} টি ত্রুটি হয়েছে";
        }
        
        return [
            'success' => $success_count > 0,
            'message' => $message,
            'details' => [
                'success' => $success_count,
                'errors' => $error_count,
                'error_list' => array_slice($errors, 0, 20), // Show first 20 errors
                'format_detected' => $is_hierarchical ? 'Hierarchical' : 'Legacy'
            ]
        ];
        
    } catch (Exception $e) {
        error_log("Failed to upload voters: " . $e->getMessage());
        return ['success' => false, 'message' => 'ভোটার আপলোড করতে ব্যর্থ: ' . $e->getMessage()];
    }
}

/**
 * Find or create area by hierarchical names
 */
function findAreaByHierarchy($db, $division_name, $district_name, $upazila_name, $union_name, $village_name, $ward_name) {
    try {
        // Build hierarchy query
        $query = "
            SELECT w.id as ward_id
            FROM areas w
            LEFT JOIN areas v ON w.parent_id = v.id AND v.type IN ('village', 'city')
            LEFT JOIN areas u ON (v.parent_id = u.id OR w.parent_id = u.id) AND u.type IN ('union', 'municipality')
            LEFT JOIN areas up ON u.parent_id = up.id AND up.type = 'upazila'
            LEFT JOIN areas d ON up.parent_id = d.id AND d.type = 'district'
            LEFT JOIN areas div ON d.parent_id = div.id AND div.type = 'division'
            WHERE w.type = 'ward' AND w.is_active = 1
        ";
        
        $params = [];
        
        if (!empty($ward_name)) {
            $query .= " AND (w.name LIKE ? OR w.name_bn LIKE ?)";
            $params[] = "%{$ward_name}%";
            $params[] = "%{$ward_name}%";
        }
        
        if (!empty($village_name)) {
            $query .= " AND (v.name LIKE ? OR v.name_bn LIKE ?)";
            $params[] = "%{$village_name}%";
            $params[] = "%{$village_name}%";
        }
        
        if (!empty($union_name)) {
            $query .= " AND (u.name LIKE ? OR u.name_bn LIKE ?)";
            $params[] = "%{$union_name}%";
            $params[] = "%{$union_name}%";
        }
        
        if (!empty($upazila_name)) {
            $query .= " AND (up.name LIKE ? OR up.name_bn LIKE ?)";
            $params[] = "%{$upazila_name}%";
            $params[] = "%{$upazila_name}%";
        }
        
        if (!empty($district_name)) {
            $query .= " AND (d.name LIKE ? OR d.name_bn LIKE ?)";
            $params[] = "%{$district_name}%";
            $params[] = "%{$district_name}%";
        }
        
        if (!empty($division_name)) {
            $query .= " AND (div.name LIKE ? OR div.name_bn LIKE ?)";
            $params[] = "%{$division_name}%";
            $params[] = "%{$division_name}%";
        }
        
        $query .= " LIMIT 1";
        
        $stmt = $db->prepare($query);
        $stmt->execute($params);
        $result = $stmt->fetch();
        
        return $result ? $result['ward_id'] : null;
        
    } catch (Exception $e) {
        error_log("Error finding area by hierarchy: " . $e->getMessage());
        return null;
    }
}
?>
